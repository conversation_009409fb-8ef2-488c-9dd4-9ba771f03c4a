<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AdminNotificationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'notification_type' => $this->notification_type,
            'notification_type_display' => $this->notification_type->getDisplayName(),
            'notification_icon' => $this->notification_type->getIconClass(),
            'is_read' => $this->computed_is_read ?? false,
            'notifiable_type' => $this->notifiable_type,
            'notifiable_id' => $this->notifiable_id,
            'notifiable' => $this->when($this->notifiable, function () {
                // Return different data based on notifiable type
                if ($this->notifiable_type === 'App\Models\User') {
                    return [
                        'id' => $this->notifiable->id,
                        'name' => $this->notifiable->name,
                        'first_name' => $this->notifiable->first_name,
                        'last_name' => $this->notifiable->last_name,
                        'email' => $this->notifiable->email,
                        'phone_number' => $this->notifiable->phone_number,
                        'user_type' => $this->notifiable->user_type,
                        'is_vendor_account_approved' => $this->notifiable->is_vendor_account_approved,
                    ];
                } elseif ($this->notifiable_type === 'App\Models\SupportRequest') {
                    return [
                        'id' => $this->notifiable->id,
                        'request_id' => $this->notifiable->request_id,
                        'subject' => $this->notifiable->subject,
                        'issue_type' => $this->notifiable->issue_type,
                        'status' => $this->notifiable->status,
                        'user' => [
                            'id' => $this->notifiable->user->id,
                            'name' => $this->notifiable->user->name,
                            'email' => $this->notifiable->user->email,
                            'user_type' => $this->notifiable->user->user_type,
                        ],
                        'created_at' => $this->notifiable->created_at,
                    ];
                }

                return $this->notifiable;
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
