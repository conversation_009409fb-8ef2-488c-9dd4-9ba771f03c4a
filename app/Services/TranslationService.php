<?php

namespace App\Services;

class TranslationService
{
    protected array $supportedLanguages = ['en', 'ar'];

    protected string $defaultLanguage = 'en';

    public function translate(string $text, string $targetLang, ?string $sourceLang = null): string
    {
        if (! in_array($targetLang, $this->supportedLanguages)) {
            return $text;
        }

        if ($targetLang === 'ar') {
            return "{$text}: واجهة برمجة التطبيقات الخاصة بنا في المستقبل";
        } else {
            return "Our Api In Future Translate this following in English: {$text}";
        }
    }

    public function getTranslations(string $content, string $currentLanguage): ?array
    {
        $result = [
            $currentLanguage => $content,
        ];

        // Add placeholder translations for other supported languages
        foreach ($this->supportedLanguages as $lang) {
            if ($lang !== $currentLanguage) {
                $result[$lang] = $this->translate($content, $lang, $currentLanguage);
            }
        }

        return $result;
    }

    public function extractForDisplay(array|string|null $storedContent, ?string $language = null): ?string
    {
        if (empty($storedContent)) {
            return null;
        }

        // If it's a string, just return it
        if (is_string($storedContent)) {
            return $storedContent;
        }

        // Use specified language or current app locale
        $language = $language ?? app()->getLocale();

        // Return content in requested language or fallback to default
        return $storedContent[$language] ?? $storedContent[$this->defaultLanguage] ?? null;
    }
}
