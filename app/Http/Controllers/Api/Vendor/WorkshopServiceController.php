<?php

namespace App\Http\Controllers\Api\Vendor;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\WorkshopServiceRequest;
use App\Http\Resources\Vendor\WorkshopServiceResource;
use App\Models\Workshop;
use App\Models\WorkshopService;
use App\Services\VendorWorkshopService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

class WorkshopServiceController extends Controller
{
    protected $vendorWorkshopService;

    public function __construct(VendorWorkshopService $vendorWorkshopService)
    {
        $this->vendorWorkshopService = $vendorWorkshopService;
    }

    public function index(Workshop $workshop): JsonResponse
    {
        if (Gate::denies('view', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        $services = $this->vendorWorkshopService->getServices($workshop);

        return ApiResponse::success([
            'services' => WorkshopServiceResource::collection($services),
        ], 'Workshop services retrieved successfully');
    }

    public function update(WorkshopServiceRequest $request, Workshop $workshop): JsonResponse
    {
        if (Gate::denies('update', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        $data = $request->validated();
        $serviceNames = $data['services'];

        $updatedServices = $this->vendorWorkshopService->syncServices($workshop, $serviceNames);

        return ApiResponse::success(
            ['services' => WorkshopServiceResource::collection($updatedServices)],
            'Services synchronized successfully'
        );
    }

    public function destroy(WorkshopService $workshopService)
    {
        if (Gate::denies('delete', $workshopService)) {
            return ApiResponse::error('You are not authorized to delete this service', 403);
        }

        $this->vendorWorkshopService->deleteService($workshopService);

        return ApiResponse::success(
            null,
            'Service deleted successfully'
        );
    }
}
