{"info": {"_postman_id": "admin-notifications-api-collection", "name": "Admin Notifications API", "description": "Complete API collection for testing Admin Notification System in Nabih API", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "item": [{"name": "Get Admin Notifications", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has notifications data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"notifications\");", "    pm.expect(jsonData.data).to.have.property(\"pagination\");", "});", "", "pm.test(\"Notifications have required fields\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.notifications.length > 0) {", "        var notification = jsonData.data.notifications[0];", "        pm.expect(notification).to.have.property(\"id\");", "        pm.expect(notification).to.have.property(\"title\");", "        pm.expect(notification).to.have.property(\"description\");", "        pm.expect(notification).to.have.property(\"notification_type\");", "        pm.expect(notification).to.have.property(\"is_read\");", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications?per_page=15", "host": ["{{base_url}}"], "path": ["admin", "notifications"], "query": [{"key": "per_page", "value": "15", "description": "Number of notifications per page"}, {"key": "is_read", "value": "false", "description": "Filter by read status (true/false)", "disabled": true}, {"key": "type", "value": "vendor_registration", "description": "Filter by notification type", "disabled": true}]}, "description": "Get paginated list of admin notifications with optional filters"}, "response": []}, {"name": "Get Unread Notifications Only", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All notifications are unread\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.notifications.length > 0) {", "        jsonData.data.notifications.forEach(function(notification) {", "            pm.expect(notification.is_read).to.eql(false);", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications?is_read=false&per_page=10", "host": ["{{base_url}}"], "path": ["admin", "notifications"], "query": [{"key": "is_read", "value": "false"}, {"key": "per_page", "value": "10"}]}, "description": "Get only unread admin notifications"}, "response": []}, {"name": "Get Vendor Notifications Only", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All notifications are vendor related\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.notifications.length > 0) {", "        jsonData.data.notifications.forEach(function(notification) {", "            pm.expect(notification.notification_type).to.be.oneOf([", "                \"vendor_registration\", ", "                \"vendor_approval_request\"", "            ]);", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications?type=vendor_registration", "host": ["{{base_url}}"], "path": ["admin", "notifications"], "query": [{"key": "type", "value": "vendor_registration"}]}, "description": "Get only vendor registration notifications"}, "response": []}, {"name": "Get Support Request Notifications", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"All notifications are support requests\", function () {", "    var jsonData = pm.response.json();", "    if (jsonData.data.notifications.length > 0) {", "        jsonData.data.notifications.forEach(function(notification) {", "            pm.expect(notification.notification_type).to.eql(\"customer_support_request\");", "        });", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications?type=customer_support_request", "host": ["{{base_url}}"], "path": ["admin", "notifications"], "query": [{"key": "type", "value": "customer_support_request"}]}, "description": "Get only customer support request notifications"}, "response": []}, {"name": "Get Single Notification", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has notification data\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"id\");", "    pm.expect(jsonData.data).to.have.property(\"title\");", "    pm.expect(jsonData.data).to.have.property(\"description\");", "    pm.expect(jsonData.data).to.have.property(\"notifiable\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications/{{notification_id}}", "host": ["{{base_url}}"], "path": ["admin", "notifications", "{{notification_id}}"]}, "description": "Get details of a specific notification by ID"}, "response": []}, {"name": "Get Unread Count", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has unread count\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"unread_count\");", "    pm.expect(jsonData.data.unread_count).to.be.a(\"number\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications/unread-count", "host": ["{{base_url}}"], "path": ["admin", "notifications", "unread-count"]}, "description": "Get count of unread admin notifications"}, "response": []}, {"name": "Get Notification Statistics", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has statistics\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"total_count\");", "    pm.expect(jsonData.data).to.have.property(\"unread_count\");", "    pm.expect(jsonData.data).to.have.property(\"read_count\");", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}], "url": {"raw": "{{base_url}}/admin/notifications/statistics", "host": ["{{base_url}}"], "path": ["admin", "notifications", "statistics"]}, "description": "Get comprehensive notification statistics"}, "response": []}, {"name": "Toggle Notification Read Status", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has read status\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"is_read\");", "    pm.expect(jsonData.data.is_read).to.be.a(\"boolean\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/admin/notifications/{{notification_id}}/toggle-read", "host": ["{{base_url}}"], "path": ["admin", "notifications", "{{notification_id}}", "toggle-read"]}, "description": "Toggle read/unread status of a specific notification"}, "response": []}, {"name": "Mark All Notifications as Read", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"Status code is 200\", function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test(\"Response has marked count\", function () {", "    var jsonData = pm.response.json();", "    pm.expect(jsonData.status).to.eql(\"success\");", "    pm.expect(jsonData.data).to.have.property(\"marked_count\");", "    pm.expect(jsonData.data.marked_count).to.be.a(\"number\");", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Accept", "value": "application/json", "type": "text"}, {"key": "Authorization", "value": "Bearer {{admin_token}}", "type": "text"}, {"key": "Content-Type", "value": "application/json", "type": "text"}], "body": {"mode": "raw", "raw": ""}, "url": {"raw": "{{base_url}}/admin/notifications/mark-all-read", "host": ["{{base_url}}"], "path": ["admin", "notifications", "mark-all-read"]}, "description": "Mark all admin notifications as read"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set default notification_id if not set", "if (!pm.variables.get(\"notification_id\")) {", "    pm.variables.set(\"notification_id\", \"1\");", "}"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Global test to check response time", "pm.test(\"Response time is less than 2000ms\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});", "", "// Global test to check content type", "pm.test(\"Content-Type is application/json\", function () {", "    pm.expect(pm.response.headers.get(\"Content-Type\")).to.include(\"application/json\");", "});"]}}], "variable": [{"key": "base_url", "value": "http://localhost/api", "type": "string"}, {"key": "admin_token", "value": "your_admin_bearer_token_here", "type": "string"}, {"key": "notification_id", "value": "1", "type": "string"}]}