<?php

namespace App\Helpers;

use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Log;

class ApiResponse
{
    private static function translateMessage($message)
    {
        if (empty($message)) {
            return $message;
        }

        try {
            $locale = App::getLocale();

            $translationKey = 'messages.'.$message;
            $translated = __($translationKey, [], $locale);

            if ($translated === $translationKey) {
                return $message;
            }

            return $translated;
        } catch (\Exception $e) {
            return $message;
        }
    }

    public static function success($data, $message = null)
    {
        return response()->json([
            'success' => true,
            'message' => self::translateMessage($message),
            'data' => $data,
        ], 200);
    }

    public static function error($message, $statusCode = 400)
    {
        return response()->json([
            'success' => false,
            'message' => self::translateMessage($message),
        ], $statusCode);
    }

    public static function errorWithLog($message, $exception, $statusCode = 400)
    {
        try {
            Log::error($exception->getMessage(), ['exception' => $exception]);
        } catch (\Exception $logException) {
            // If logging fails, continue without logging to prevent cascading failures
        }

        return static::error($message, $statusCode);
    }

    public static function safeError($message, $statusCode = 500)
    {
        // Safe error response that never depends on logging or external services
        return response()->json([
            'success' => false,
            'message' => self::translateMessage($message),
            'error_type' => 'internal_server_error',
            'timestamp' => date('Y-m-d H:i:s'),
        ], $statusCode);
    }

    public static function validationError($errors)
    {
        return response()->json([
            'success' => false,
            'message' => self::translateMessage('Validation failed'),
            'errors' => $errors,
        ], 422);
    }
}
