<?php

namespace App\Http\Requests\Faq;

use Illuminate\Foundation\Http\FormRequest;

class UpdateFaqRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'user_type' => 'nullable|string|in:customer,vendor',
            'order' => 'nullable|integer|min:1',
            'is_active' => 'nullable|boolean',
            'translations' => 'nullable|array',
            'translations.*.question' => 'required|string|max:255',
            'translations.*.answer' => 'required|string',
        ];
    }
}
