<?php

namespace App\Mail;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Mail\Mailable;
use Illuminate\Mail\Mailables\Content;
use Illuminate\Mail\Mailables\Envelope;
use Illuminate\Queue\SerializesModels;

class VendorEmailOtpMail extends Mailable implements ShouldQueue
{
    use Queueable, SerializesModels;

    /**
     * The OTP code
     *
     * @var string
     */
    public $otp;

    /**
     * The vendor name
     *
     * @var string
     */
    public $name;

    /**
     * OTP expiration time in minutes
     *
     * @var int
     */
    public $expiryMinutes;

    /**
     * Create a new message instance.
     */
    public function __construct(string $otp, string $name, int $expiryMinutes = 15)
    {
        $this->otp = $otp;
        $this->name = $name;
        $this->expiryMinutes = $expiryMinutes;
    }

    /**
     * Get the message envelope.
     */
    public function envelope(): Envelope
    {
        return new Envelope(
            subject: 'Your Verification Code - '.config('app.name'),
        );
    }

    /**
     * Get the message content definition.
     */
    public function content(): Content
    {
        return new Content(
            markdown: 'emails.vendor.otp',
        );
    }

    /**
     * Get the attachments for the message.
     *
     * @return array<int, \Illuminate\Mail\Mailables\Attachment>
     */
    public function attachments(): array
    {
        return [];
    }
}
