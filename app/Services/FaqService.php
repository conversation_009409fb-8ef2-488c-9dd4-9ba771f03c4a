<?php

namespace App\Services;

use App\Models\Faq;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class FaqService
{
    /**
     * Get all FAQs for specific user type.
     *
     * @param  string  $userType  'customer' or 'vendor'
     */
    public function getAllFaqsByUserType(string $userType, string $locale = 'en'): Collection
    {
        return Faq::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
            ->where('user_type', $userType)
            ->where('is_active', true)
            ->orderBy('order')
            ->get();
    }

    /**
     * Get all FAQs for admin.
     */
    public function getAllFaqsForAdmin(): Collection
    {
        return Faq::with('translations')
            ->orderBy('user_type')
            ->orderBy('order')
            ->get();
    }

    /**
     * Get FAQ by ID.
     */
    public function getFaqById(int $id): ?Faq
    {
        return Faq::with('translations')->find($id);
    }

    /**
     * Store a new FAQ.
     */
    public function store(array $data): Faq
    {
        return DB::transaction(function () use ($data) {
            // Create FAQ
            $faq = Faq::create([
                'user_type' => $data['user_type'],
                'order' => $data['order'] ?? Faq::where('user_type', $data['user_type'])->max('order') + 1,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Create translations
            foreach ($data['translations'] as $locale => $translation) {
                $faq->translations()->create([
                    'locale' => $locale,
                    'question' => $translation['question'],
                    'answer' => $translation['answer'],
                ]);
            }

            return $faq->load('translations');
        });
    }

    /**
     * Update a FAQ.
     */
    public function update(Faq $faq, array $data): Faq
    {
        return DB::transaction(function () use ($faq, $data) {
            // Update FAQ
            $faq->user_type = $data['user_type'] ?? $faq->user_type;
            $faq->order = $data['order'] ?? $faq->order;
            $faq->is_active = $data['is_active'] ?? $faq->is_active;
            $faq->save();

            // Update translations
            if (isset($data['translations'])) {
                foreach ($data['translations'] as $locale => $translation) {
                    $faq->translations()->updateOrCreate(
                        ['locale' => $locale],
                        [
                            'question' => $translation['question'],
                            'answer' => $translation['answer'],
                        ]
                    );
                }
            }

            return $faq->load('translations');
        });
    }

    /**
     * Delete a FAQ.
     */
    public function delete(Faq $faq): bool
    {
        return DB::transaction(function () use ($faq) {
            // Delete translations (will cascade due to foreign key constraint)
            return $faq->delete();
        });
    }

    /**
     * Reorder FAQs for a specific user type.
     *
     * @param  array  $faqIds  ordered array of FAQ IDs
     */
    public function reorder(string $userType, array $faqIds): bool
    {
        return DB::transaction(function () use ($userType, $faqIds) {
            foreach ($faqIds as $order => $id) {
                Faq::where('id', $id)
                    ->where('user_type', $userType)
                    ->update(['order' => $order + 1]);
            }

            return true;
        });
    }
}
