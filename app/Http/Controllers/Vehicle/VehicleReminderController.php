<?php

namespace App\Http\Controllers\Vehicle;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vehicle\VehicleReminderRequest;
use App\Http\Resources\Vehicle\VehicleReminderResource;
use App\Jobs\ProcessTranslationJob;
use App\Models\Vehicle;
use App\Models\VehicleReminder;
use App\Services\Vehicle\VehicleReminderService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VehicleReminderController extends Controller
{
    protected $vehicleReminderService;

    public function __construct(VehicleReminderService $vehicleReminderService)
    {
        $this->vehicleReminderService = $vehicleReminderService;
    }

    public function index(Request $request, int $vehicleId): JsonResponse
    {
        $vehicle = Vehicle::findOrFail($vehicleId);
        if (! $request->user()->can('viewAny', [VehicleReminder::class, $vehicle])) {
            return ApiResponse::error('You do not have permission to view these reminders', 403);
        }

        $reminders = $this->vehicleReminderService->getAllByVehicleId($vehicleId);

        return ApiResponse::success([
            'reminders' => VehicleReminderResource::collection($reminders),
        ], 'Reminders retrieved successfully');
    }

    public function store(VehicleReminderRequest $request, int $vehicleId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $reminder = $this->vehicleReminderService->create($dto);

            // Dispatch translation job if review exists
            if (! empty($dto->review)) {
                ProcessTranslationJob::dispatch(
                    VehicleReminder::class,
                    $reminder->id,
                    'review',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'reminder' => new VehicleReminderResource($reminder),
            ], 'Reminder added successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to create reminder: '.$e->getMessage(), 500);
        }
    }

    public function show(Request $request, int $vehicleId, int $reminderId): JsonResponse
    {
        $reminder = $this->vehicleReminderService->getById($reminderId);

        if (! $reminder) {
            return ApiResponse::error('Reminder not found', 404);
        }

        // Check if user has permission to view this reminder
        if (! $request->user()->can('view', $reminder)) {
            return ApiResponse::error('You do not have permission to view this reminder', 403);
        }

        return ApiResponse::success([
            'reminder' => new VehicleReminderResource($reminder),
        ], 'Reminder retrieved successfully');
    }

    public function update(VehicleReminderRequest $request, int $vehicleId, int $reminderId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $reminder = $this->vehicleReminderService->update($reminderId, $dto);

            if (! $reminder) {
                DB::rollBack();

                return ApiResponse::error('Reminder not found', 404);
            }

            // Dispatch translation job if review exists
            if (! empty($dto->review)) {
                ProcessTranslationJob::dispatch(
                    VehicleReminder::class,
                    $reminder->id,
                    'review',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'reminder' => new VehicleReminderResource($reminder),
            ], 'Reminder updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to update reminder: '.$e->getMessage(), 500);
        }
    }

    public function destroy(Request $request, int $vehicleId, int $reminderId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $reminder = VehicleReminder::find($reminderId);
            if (! $reminder) {
                DB::rollBack();

                return ApiResponse::error('Reminder not found', 404);
            }

            // Check if user has permission to delete this reminder
            if (! $request->user()->can('delete', $reminder)) {
                DB::rollBack();

                return ApiResponse::error('You do not have permission to delete this reminder', 403);
            }

            $deleted = $this->vehicleReminderService->delete($reminderId);

            if (! $deleted) {
                DB::rollBack();

                return ApiResponse::error('Failed to delete reminder', 500);
            }

            DB::commit();

            return ApiResponse::success([], 'Reminder deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to delete reminder: '.$e->getMessage(), 500);
        }
    }
}
