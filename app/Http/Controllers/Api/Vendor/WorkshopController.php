<?php

namespace App\Http\Controllers\Api\Vendor;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\WorkshopImageRequest;
use App\Http\Requests\Vendor\WorkshopRequest;
use App\Http\Resources\Vendor\WorkshopResource;
use App\Models\Workshop;
use App\Services\VendorWorkshopService;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Gate;

class WorkshopController extends Controller
{
    protected $vendorWorkshopService;

    public function __construct(VendorWorkshopService $vendorWorkshopService)
    {
        $this->vendorWorkshopService = $vendorWorkshopService;
    }

    /**
     * List all workshops for the authenticated vendor
     */
    public function index()
    {
        $workshops = $this->vendorWorkshopService->getWorkshops(Auth::user());

        return ApiResponse::success([
            'workshops' => WorkshopResource::collection($workshops),
        ], 'Workshops retrieved successfully');
    }

    /**
     * Get a specific workshop
     */
    public function show(Workshop $workshop)
    {
        if (Gate::denies('view', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        // Load relationships
        $workshop->load(['services', 'workingHours', 'user']);

        return ApiResponse::success(
            new WorkshopResource($workshop),
            'Workshop retrieved successfully'
        );
    }

    public function store(WorkshopRequest $request)
    {
        // Convert request to DTO
        $workshopDTO = $request->toDTO();

        $workshop = $this->vendorWorkshopService->createWorkshop(
            Auth::user(),
            $workshopDTO
        );

        return ApiResponse::success(
            new WorkshopResource($workshop),
            'Workshop created successfully'
        );
    }

    public function update(WorkshopRequest $request, Workshop $workshop)
    {
        // Convert request to DTO
        $workshopDTO = $request->toDTO($workshop->user_id);

        $workshop = $this->vendorWorkshopService->updateWorkshop(
            $workshop,
            $workshopDTO
        );

        return ApiResponse::success(
            new WorkshopResource($workshop),
            'Workshop updated successfully'
        );
    }

    public function uploadImage(WorkshopImageRequest $request, Workshop $workshop)
    {
        // Check authorization
        if (Gate::denies('update', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        $image = $request->file('image');
        $success = $this->vendorWorkshopService->uploadImage($workshop, $image);

        if ($success) {
            return ApiResponse::success(
                new WorkshopResource($workshop->fresh()),
                'Workshop image uploaded successfully'
            );
        }

        return ApiResponse::error('Failed to upload workshop image', 500);
    }

    public function deleteImage(Workshop $workshop)
    {
        // Check authorization
        if (Gate::denies('update', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        $success = $this->vendorWorkshopService->deleteImage($workshop);

        if ($success) {
            return ApiResponse::success(
                new WorkshopResource($workshop->fresh()),
                'Workshop image deleted successfully'
            );
        }

        return ApiResponse::error('Failed to delete workshop image', 500);
    }
}
