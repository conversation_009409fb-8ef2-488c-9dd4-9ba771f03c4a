<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\OnboardingScreen\ReorderOnboardingScreenRequest;
use App\Http\Requests\OnboardingScreen\StoreOnboardingScreenRequest;
use App\Http\Requests\OnboardingScreen\UpdateOnboardingScreenRequest;
use App\Http\Resources\OnboardingScreen\OnboardingScreenResource;
use App\Models\OnboardingScreen;
use App\Services\OnboardingScreenService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OnboardingScreenController extends Controller
{
    protected $onboardingScreenService;

    public function __construct(OnboardingScreenService $onboardingScreenService)
    {
        $this->onboardingScreenService = $onboardingScreenService;
    }

    public function index(): JsonResponse
    {
        try {
            $screens = $this->onboardingScreenService->getAllScreensForAdmin();

            return ApiResponse::success([
                'onboarding_screens' => OnboardingScreenResource::collection($screens),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve onboarding screens', $e);
        }
    }

    public function store(StoreOnboardingScreenRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $screen = $this->onboardingScreenService->store(
                $request->validated(),
                $request->file('image')
            );

            DB::commit();

            return ApiResponse::success(
                new OnboardingScreenResource($screen),
                'Onboarding screen created successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create onboarding screen', $e);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            $onboardingScreen = OnboardingScreen::findOrFail($id);

            return ApiResponse::success(
                new OnboardingScreenResource($onboardingScreen->load('translations'))
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve onboarding screen', $e);
        }
    }

    public function update(UpdateOnboardingScreenRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $screen = $this->onboardingScreenService->update(
                OnboardingScreen::findOrFail($id),
                $request->validated(),
                $request->file('image')
            );

            DB::commit();

            return ApiResponse::success(
                new OnboardingScreenResource($screen),
                'Onboarding screen updated successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update onboarding screen', $e);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            OnboardingScreen::findOrFail($id)->delete();

            DB::commit();

            return ApiResponse::success([], 'Onboarding screen deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete onboarding screen', $e);
        }
    }

    public function reorder(ReorderOnboardingScreenRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $this->onboardingScreenService->reorder($request->ids);

            DB::commit();

            return ApiResponse::success([], 'Onboarding screens reordered successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to reorder onboarding screens', $e);
        }
    }
}
