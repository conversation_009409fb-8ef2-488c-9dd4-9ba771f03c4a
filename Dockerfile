# Build stage
FROM composer:2.8 AS vendor

WORKDIR /app

# Install dependencies
COPY composer.json composer.lock ./
RUN composer install \
    --ignore-platform-reqs \
    --no-interaction \
    --no-plugins \
    --no-scripts \
    # --no-dev \
    --prefer-dist

# Application stage
FROM php:8.4-fpm

# Install system dependencies including gRPC requirements
RUN apt-get update && apt-get install -y \
    git \
    curl \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    zip \
    unzip \
    libzip-dev \
    build-essential \
    autoconf \
    libtool \
    pkg-config \
    zlib1g-dev \
    && docker-php-ext-install pdo_mysql mbstring exif pcntl bcmath gd zip \
    && pecl install redis && docker-php-ext-enable redis \
    && pecl install grpc && docker-php-ext-enable grpc \
    && docker-php-ext-install exif

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Install Composer
COPY --from=composer:2.8 /usr/bin/composer /usr/bin/composer

# Set working directory
WORKDIR /var/www

# Copy application files
COPY . /var/www

# Copy composer dependencies from vendor stage
COPY --from=vendor /app/vendor/ /var/www/vendor/

# Set permissions
RUN chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm

# Install frontend dependencies and build assets
RUN npm install && npm run build

# Expose port 9000 and start php-fpm server
EXPOSE 9000

# Start PHP-FPM directly
CMD ["php-fpm"]
