<?php

return [
    /*
    |--------------------------------------------------------------------------
    | Firebase Configuration
    |--------------------------------------------------------------------------
    |
    | Configuration for Firebase services including FCM
    | Uses HTTP v1 API with service account credentials
    |
    */

    'project_id' => env('FIREBASE_PROJECT_ID', 'nabih-59244'),

    'credentials' => [
        'type' => 'service_account',
        'project_id' => env('FIREBASE_PROJECT_ID', 'nabih-59244'),
        'private_key_id' => env('FIREBASE_PRIVATE_KEY_ID'),
        'private_key' => env('FIREBASE_PRIVATE_KEY_BASE64')
            ? base64_decode(env('FIREBASE_PRIVATE_KEY_BASE64'))
            : env('FIREBASE_PRIVATE_KEY'), // Fallback to direct key
        'client_email' => env('FIREBASE_CLIENT_EMAIL'),
        'client_id' => env('FIREBASE_CLIENT_ID'),
        'auth_uri' => env('FIREBASE_AUTH_URI', 'https://accounts.google.com/o/oauth2/auth'),
        'token_uri' => env('FIREBASE_TOKEN_URI', 'https://oauth2.googleapis.com/token'),
        'auth_provider_x509_cert_url' => env('FIREBASE_AUTH_PROVIDER_CERT_URL', 'https://www.googleapis.com/oauth2/v1/certs'),
        'client_x509_cert_url' => env('FIREBASE_CLIENT_X509_CERT_URL', 'https://www.googleapis.com/robot/v1/metadata/x509/'.urlencode(env('FIREBASE_CLIENT_EMAIL'))),
        'universe_domain' => env('FIREBASE_UNIVERSE_DOMAIN', 'googleapis.com'),
    ],

    'database_url' => env('FIREBASE_DATABASE_URL', 'https://nabih-59244.firebaseio.com'),
];
