<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OfferResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title, // Already cast as array by model with both languages
            'description' => $this->description, // Already cast as array by model with both languages
            'image_url' => $this->hasMedia('offer_image') ? $this->getFirstMediaUrl('offer_image') : null,
            'discount_text' => $this->discount_text, // Already cast as array by model with both languages
            'is_active' => $this->is_active,
            'display_order' => $this->display_order,
            'start_date' => $this->start_date ? $this->start_date->format('Y-m-d H:i:s') : null,
            'end_date' => $this->end_date ? $this->end_date->format('Y-m-d H:i:s') : null,
            'button_text' => $this->button_text, // Already cast as array by model
            'button_link' => $this->button_link,
            'is_limited_time' => $this->is_limited_time,
            'created_at' => $this->created_at->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at->format('Y-m-d H:i:s'),
        ];
    }
}
