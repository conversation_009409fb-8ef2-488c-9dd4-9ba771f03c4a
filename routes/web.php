<?php

use App\Http\Controllers\Api\FCMController;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Route;

Route::get('/', function () {
    return view('welcome');
});

Route::get('/ci-cd', function () {
    Log::info('CI/CD endpoint accessed');

    return response()->json(['message' => 'CI/CD endpoint Hello this is testing']);
});

Route::get('send-notification', [FCMController::class, 'sendNotification'])->name('send-notification');
