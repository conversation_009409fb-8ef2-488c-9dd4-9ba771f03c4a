<?php

namespace App\Services\Vehicle;

use App\DTOs\VehicleReminderDTO;
use App\Models\VehicleReminder;

class VehicleReminderService
{
    public function getAllByVehicleId(int $vehicleId)
    {
        return VehicleReminder::where('vehicle_id', $vehicleId)
            ->orderBy('reminder_date', 'desc')
            ->get();
    }

    public function create(VehicleReminderDTO $dto): VehicleReminder
    {
        return VehicleReminder::create($dto->toArray());
    }

    public function getById(int $id): ?VehicleReminder
    {
        return VehicleReminder::find($id);
    }

    public function update(int $id, VehicleReminderDTO|array $data): ?VehicleReminder
    {
        $reminder = $this->getById($id);

        if (! $reminder) {
            return null;
        }

        $updateData = $data instanceof VehicleReminderDTO ? $data->toArray() : $data;
        $reminder->update($updateData);

        return $reminder->fresh();
    }

    public function delete(int $id): bool
    {
        $reminder = $this->getById($id);

        if (! $reminder) {
            return false;
        }

        return $reminder->delete();
    }
}
