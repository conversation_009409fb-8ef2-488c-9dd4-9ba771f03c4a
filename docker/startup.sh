#!/bin/bash
set -e

# Change to the application directory
cd /var/www

# Ensure Laravel storage directories exist with proper structure
mkdir -p /var/www/storage/framework/{cache,sessions,views}
mkdir -p /var/www/storage/logs
mkdir -p /var/www/storage/app/public

# Set proper permissions for Laravel
chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache
chmod -R 775 /var/www/storage /var/www/bootstrap/cache

# Install Composer dependencies with gRPC platform requirement ignored
echo "Installing Composer dependencies..."
composer install --no-dev --optimize-autoloader --no-interaction --prefer-dist --classmap-authoritative --ignore-platform-req=ext-grpc

# Run Laravel key generation if not already set
php artisan key:generate --force

# Run migrations
php artisan migrate --force

# Clear all caches and rebuild for production
echo "Clearing and optimizing caches..."
php artisan optimize:clear
php artisan optimize

# Start supervisord
exec supervisord -n -c /etc/supervisor/supervisord.conf
