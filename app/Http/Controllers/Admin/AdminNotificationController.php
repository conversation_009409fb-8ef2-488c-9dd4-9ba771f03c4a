<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\AdminNotificationCollection;
use App\Http\Resources\Admin\AdminNotificationResource;
use App\Services\NotificationService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class AdminNotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Get admin notifications list with filtering
     */
    public function index(Request $request): JsonResponse
    {
        $adminUserId = Auth::id();

        $filters = [
            'is_read' => $request->boolean('is_read', null),
            'type' => $request->input('type'),
            'per_page' => $request->integer('per_page', 15),
        ];

        // Remove null values from filters
        $filters = array_filter($filters, function ($value) {
            return $value !== null;
        });

        $notifications = $this->notificationService->getAdminNotifications($adminUserId, $filters);

        return response()->json([
            'status' => 'success',
            'message' => 'Admin notifications retrieved successfully',
            'data' => new AdminNotificationCollection($notifications),
        ]);
    }

    /**
     * Get single admin notification
     */
    public function show(int $notificationId): JsonResponse
    {
        $adminUserId = Auth::id();

        $notification = $this->notificationService->getUserNotification($notificationId, $adminUserId);

        if (! $notification) {
            return response()->json([
                'status' => 'error',
                'message' => 'Notification not found',
            ], 404);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Admin notification retrieved successfully',
            'data' => new AdminNotificationResource($notification),
        ]);
    }

    /**
     * Toggle notification read status
     */
    public function toggleReadStatus(int $notificationId): JsonResponse
    {
        $adminUserId = Auth::id();

        try {
            $isRead = $this->notificationService->toggleNotificationReadStatus($notificationId, $adminUserId);

            return response()->json([
                'status' => 'success',
                'message' => 'Notification status updated successfully',
                'data' => [
                    'is_read' => $isRead,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update notification status',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Mark all admin notifications as read
     */
    public function markAllAsRead(): JsonResponse
    {
        $adminUserId = Auth::id();

        try {
            $count = $this->notificationService->markAllAsReadForUser($adminUserId);

            return response()->json([
                'status' => 'success',
                'message' => "Marked {$count} notifications as read",
                'data' => [
                    'marked_count' => $count,
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to mark notifications as read',
                'error' => $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Get unread notifications count
     */
    public function getUnreadCount(): JsonResponse
    {
        $adminUserId = Auth::id();

        $count = $this->notificationService->getUnreadAdminNotificationsCount($adminUserId);

        return response()->json([
            'status' => 'success',
            'message' => 'Unread notifications count retrieved successfully',
            'data' => [
                'unread_count' => $count,
            ],
        ]);
    }

    /**
     * Get notification statistics
     */
    public function getStatistics(): JsonResponse
    {
        $adminUserId = Auth::id();

        $totalCount = $this->notificationService->getAdminNotifications($adminUserId)->total();
        $unreadCount = $this->notificationService->getUnreadAdminNotificationsCount($adminUserId);
        $readCount = $totalCount - $unreadCount;

        return response()->json([
            'status' => 'success',
            'message' => 'Notification statistics retrieved successfully',
            'data' => [
                'total_count' => $totalCount,
                'unread_count' => $unreadCount,
                'read_count' => $readCount,
            ],
        ]);
    }
}
