<?php

namespace Database\Factories;

use App\Models\Offer;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Offer>
 */
class OfferFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Offer::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $startDate = Carbon::now()->addDays(rand(-30, 30));
        $endDate = Carbon::parse($startDate)->addDays(rand(5, 60));

        // Random Arabic titles for realistic data
        $arabicTitles = [
            'تخفيضات موسمية',
            'عروض خاصة',
            'خصم لفترة محدودة',
            'العرض الأفضل',
            'صفقة اليوم',
            'عروض نهاية الأسبوع',
            'تخفيضات الصيف',
            'عروض الشتاء',
        ];

        // Random Arabic descriptions
        $arabicDescriptions = [
            'استفد من أفضل العروض والخصومات على خدماتنا المتميزة لفترة محدودة',
            'خصومات حصرية على جميع الخدمات، لا تفوت الفرصة',
            'أسعار مخفضة على الخدمات المميزة لعملائنا الكرام',
            'عروض مميزة وأسعار تنافسية على كافة الخدمات',
            'خدمات متميزة بأسعار مناسبة للجميع',
        ];

        // Random Arabic button text
        $arabicButtonTexts = [
            'اطلب الآن',
            'احجز الآن',
            'المزيد من التفاصيل',
            'استفد من العرض',
            'اشترك الآن',
        ];

        // Random Arabic discount texts
        $arabicDiscountTexts = [
            'خصم حتى ٢٠٪',
            'خصم ٣٠٪ لفترة محدودة',
            'وفر حتى ٥٠٪',
            'اشترك واحصل على ٢٥٪ خصم',
            'عرض خاص: ٤٠٪ خصم',
        ];

        $isLimitedTime = $this->faker->boolean(70); // 70% chance of being limited time

        return [
            'title' => [
                'en' => 'Offer: '.$this->faker->words(rand(2, 4), true),
                'ar' => $this->faker->randomElement($arabicTitles),
            ],
            'description' => [
                'en' => $this->faker->paragraph(rand(1, 3)),
                'ar' => $this->faker->randomElement($arabicDescriptions),
            ],
            'discount_text' => [
                'en' => $this->faker->randomElement(['Up to 20% off', '30% discount', 'Save 50%', 'Special offer: 40% off']),
                'ar' => $this->faker->randomElement($arabicDiscountTexts),
            ],
            'button_text' => [
                'en' => $this->faker->randomElement(['Order Now', 'Book Now', 'Learn More', 'Get Offer', 'Subscribe']),
                'ar' => $this->faker->randomElement($arabicButtonTexts),
            ],
            'button_link' => $this->faker->randomElement(['/services', '/deals', '/special-offers', '/promotions']),
            'is_active' => $this->faker->boolean(80), // 80% chance of being active
            'display_order' => $this->faker->numberBetween(1, 20),
            'start_date' => $startDate,
            'end_date' => $isLimitedTime ? $endDate : null,
            'is_limited_time' => $isLimitedTime,
        ];
    }

    /**
     * Configure the model factory.
     *
     * @return $this
     */
    public function configure()
    {
        // This will be called after the model is created
        // We need to add the image separately because Spatie Media Library
        // needs an actual model instance to attach media to
        return $this->afterCreating(function (Offer $offer) {
            // Generate a placeholder image URL using Faker
            $imageUrl = 'https://picsum.photos/800/400?random='.rand(1, 1000);

            // Download the image and add it to the media collection
            try {
                $offer->addMediaFromUrl($imageUrl)
                    ->toMediaCollection('offer_image');
            } catch (\Exception $e) {
                // If downloading fails, we'll just log it and continue
                \Log::warning("Failed to download image for offer #{$offer->id}: ".$e->getMessage());
            }
        });
    }

    /**
     * Create an active offer.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function active()
    {
        return $this->state(function (array $attributes) {
            return [
                'is_active' => true,
            ];
        });
    }

    /**
     * Create a limited time offer.
     *
     * @return \Illuminate\Database\Eloquent\Factories\Factory
     */
    public function limitedTime()
    {
        return $this->state(function (array $attributes) {
            $startDate = Carbon::now();

            return [
                'is_limited_time' => true,
                'start_date' => $startDate,
                'end_date' => $startDate->copy()->addDays(rand(7, 30)),
            ];
        });
    }
}
