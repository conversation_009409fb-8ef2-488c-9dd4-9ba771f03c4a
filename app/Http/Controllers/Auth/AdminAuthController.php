<?php

namespace App\Http\Controllers\Auth;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\Admin\AdminLoginRequest;
use App\Services\Auth\AdminAuthService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class AdminAuthController extends Controller
{
    protected AdminAuthService $authService;

    public function __construct(AdminAuthService $authService)
    {
        $this->authService = $authService;
    }

    public function login(AdminLoginRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->login($request->email, $request->password);

            return ApiResponse::success($result, 'Admin login successful');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to login', $e);
        }
    }

    public function logout(Request $request): JsonResponse
    {
        try {
            $this->authService->logout($request->user());

            return ApiResponse::success([], 'Admin logged out successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Admin logout failed', $e);
        }
    }

    /**
     * Delete admin's own account
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            // Check if user is a super-admin (prevent super-admin deletion)
            if ($user->hasRole('super-admin')) {
                return ApiResponse::error('Super admin accounts cannot be deleted', 403);
            }

            // Revoke all tokens
            $user->tokens()->delete();

            // Soft delete the admin account
            $user->delete();

            return ApiResponse::success([], 'Your account has been deleted successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to delete your account', $e);
        }
    }
}
