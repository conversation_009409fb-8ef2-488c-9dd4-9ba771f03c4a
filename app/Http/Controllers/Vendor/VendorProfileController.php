<?php

namespace App\Http\Controllers\Vendor;

use App\Enums\Gender;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\VendorProfileUpdateRequest;
use App\Http\Resources\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class VendorProfileController extends Controller
{
    public function getProfile(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->load(['workshops.workingHours', 'workshops.services']);

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Vendor profile retrieved successfully');
    }

    public function updateProfile(VendorProfileUpdateRequest $request): JsonResponse
    {
        $user = $request->user();

        // Get validated data directly
        $data = $request->validated();

        // Update user with validated data
        if (! empty($data)) {
            $user->update($data);
        }

        if ($request->hasFile('avatar')) {
            $user->clearMediaCollection('avatar');

            $user->addMediaFromRequest('avatar')
                ->toMediaCollection('avatar');
        }

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Vendor profile updated successfully');
    }

    public function getGenderOptions(Request $request): JsonResponse
    {
        return ApiResponse::success([
            'genders' => Gender::options(),
            'locale' => App::getLocale(),
        ], 'Gender options retrieved successfully');
    }

    /**
     * Delete vendor's own account
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            // Revoke all tokens
            $user->tokens()->delete();

            // Soft delete the vendor account
            $user->delete();

            return ApiResponse::success([], 'Your account has been deleted successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to delete your account', $e);
        }
    }

    public function deleteAvatar(Request $request): JsonResponse
    {
        $user = $request->user();
        $user->clearMediaCollection('avatar');

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Avatar deleted successfully');
    }

    public function uploadAvatar(Request $request): JsonResponse
    {
        $request->validate([
            'avatar' => 'required|image|max:5120', // 5MB max size
        ]);

        $user = $request->user();

        // Remove old avatar if exists
        $user->clearMediaCollection('avatar');

        // Add new avatar
        $user->addMediaFromRequest('avatar')
            ->toMediaCollection('avatar');

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Avatar uploaded successfully');
    }
}
