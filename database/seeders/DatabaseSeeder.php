<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            SuperAdminSeeder::class, // This must run first to create permissions
            RolesAndPermissionsSeeder::class, // Then create additional roles
            VehicleSeeder::class,
            ContentSeeder::class,
            WorkshopSeeder::class,
            SupportRequestSeeder::class,
            OfferSeeder::class,
            SocialMediaLinkSeeder::class, // Social media links data
            DummyNotificationDataSeeder::class, // Dummy notification data for testing
            AdminNotificationSeeder::class, // Admin notification system data
        ]);
    }
}
