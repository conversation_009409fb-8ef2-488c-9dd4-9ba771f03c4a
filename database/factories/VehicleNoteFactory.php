<?php

namespace Database\Factories;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VehicleNote>
 */
class VehicleNoteFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'vehicle_id' => Vehicle::factory(),
            'title' => $this->faker->sentence(3),
            'note_date' => $this->faker->dateTimeBetween('-6 months', 'now'),
            'details' => json_encode([
                'en' => $this->faker->paragraph(3),
            ]),
        ];
    }
}
