<?php

namespace App\Http\Resources\OnboardingScreen;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OnboardingScreenResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        // Get requested locale or default to 'en'
        $locale = $request->header('Accept-Language', 'en');

        // Get translation for requested locale or fallback to first available
        $translation = $this->translate($locale) ?? $this->translations->first();

        return [
            'id' => $this->id,
            'order' => $this->order,
            'image' => $this->image ? url('storage/'.$this->image) : null,
            'is_active' => $this->is_active,
            'locale' => $translation?->locale ?? $locale,
            // Include all translations when viewed in admin panel
            'translations' => $this->translations->mapWithKeys(function ($item) {
                return [$item->locale => [
                    'title' => $item->title,
                    'description' => $item->description,
                ]];
            }),
        ];
    }
}
