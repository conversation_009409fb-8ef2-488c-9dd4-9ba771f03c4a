<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add deleted_at column to workshops table
        if (! Schema::hasColumn('workshops', 'deleted_at')) {
            Schema::table('workshops', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        // Add deleted_at column to working_hours table
        if (! Schema::hasColumn('working_hours', 'deleted_at')) {
            Schema::table('working_hours', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        // Add deleted_at column to notification_read_status table
        if (! Schema::hasColumn('notification_read_status', 'deleted_at')) {
            Schema::table('notification_read_status', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('remaining_tables', function (Blueprint $table) {
            //
        });
    }
};
