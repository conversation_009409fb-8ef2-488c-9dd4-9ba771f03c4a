<?php

namespace Database\Seeders;

use App\Enums\UserType;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleNote;
use App\Models\VehicleReminder;
use App\Models\VehicleService;
use Illuminate\Database\Seeder;

class VehicleSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Ensure we have some customer users
        $customerUsers = User::whereUserType(UserType::CUSTOMER->value)->get();

        if ($customerUsers->isEmpty()) {
            // Create 5 customers
            $customerUsers = User::factory(5)
                ->state(['user_type' => UserType::CUSTOMER->value])
                ->create();
        }

        // For each customer user, create 1-3 vehicles with varied relationships
        foreach ($customerUsers as $user) {

            $vehicleCount = rand(1, 3);

            Vehicle::factory($vehicleCount)
                ->for($user)
                ->afterCreating(function (Vehicle $vehicle) {
                    // Create 2-5 services with different types
                    VehicleService::factory(rand(2, 5))->create([
                        'vehicle_id' => $vehicle->id,
                        'service_type' => function () {
                            return fake()->randomElement([
                                'Oil Change',
                                'Tire Rotation',
                                'Brake Service',
                                'Engine Tune-Up',
                                'Battery Replacement',
                                'Air Filter Replacement',
                                'Fluid Check',
                                'Transmission Service',
                                'Cooling System Service',
                            ]);
                        },
                        'review' => function () {
                            // Create reviews in both English and Arabic
                            return [
                                'en' => fake()->realText(100),
                                'ar' => 'تعليق على الخدمة '.fake()->realText(50),
                            ];
                        },
                    ]);

                    // Create 1-3 date-based reminders
                    VehicleReminder::factory(rand(1, 3))->create([
                        'vehicle_id' => $vehicle->id,
                        'is_mileage_based' => false,
                        'reminder_date' => fake()->dateTimeBetween('now', '+6 months'),
                    ]);

                    // Create 1-2 mileage-based reminders
                    VehicleReminder::factory(rand(1, 2))->create([
                        'vehicle_id' => $vehicle->id,
                        'is_mileage_based' => true,
                        'mileage' => $vehicle->mileage ?? fake()->numberBetween(5000, 80000),
                    ]);

                    // Create 2-8 notes with realistic titles
                    VehicleNote::factory(rand(2, 8))->create([
                        'vehicle_id' => $vehicle->id,
                        'title' => function () {
                            return fake()->randomElement([
                                'Maintenance Record',
                                'Issue Observed',
                                'Repair Note',
                                'Purchase Details',
                                'Fuel Economy',
                                'Trip Log',
                                'Accident Report',
                                'Insurance Info',
                                'Warranty Details',
                                'Modification Note',
                                'Performance Observation',
                            ]);
                        },
                    ]);
                })
                ->create();
        }
    }
}
