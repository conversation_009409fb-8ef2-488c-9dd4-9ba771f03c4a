<?php

namespace App\Http\Requests;

use App\Helpers\ApiResponse;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Http\Exceptions\HttpResponseException;
use Illuminate\Support\Facades\Auth;

class BaseApiRequest extends FormRequest
{
    // protected $stopOnFirstFailure = true;

    // public function user()
    // {
    //     return Auth::user();
    // }

    // public function route($key = null, $default = null)
    // {
    //     return app('request')->route($key, $default);
    // }

    protected function failedValidation(\Illuminate\Contracts\Validation\Validator $validator)
    {
        throw new HttpResponseException(
            ApiResponse::validationError($validator->errors())
        );
    }

    protected function failedAuthorization()
    {
        throw new HttpResponseException(
            ApiResponse::error('You do not have permission to perform this access', 403)
        );
    }
}
