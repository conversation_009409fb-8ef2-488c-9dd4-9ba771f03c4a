<?php

namespace App\Http\Resources\Api;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MobileOfferResource extends JsonResource
{
    /**
     * Transform the resource into an array optimized for mobile display.
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title, // Already cast as array by model with both languages
            'description' => $this->description, // Already cast as array by model with both languages
            'image_url' => $this->hasMedia('offer_image') ? $this->getFirstMediaUrl('offer_image') : null,
            'discount_text' => $this->discount_text, // Already cast as array by model with both languages
            'button_text' => $this->button_text, // Already cast as array by model with both languages
            'button_link' => $this->button_link,
            'is_limited_time' => $this->is_limited_time,
        ];
    }
}
