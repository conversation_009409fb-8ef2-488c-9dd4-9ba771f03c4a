<?php

namespace App\Http\Requests\Vehicle;

use App\DTOs\VehicleNoteDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Vehicle;
use App\Models\VehicleNote;
use Illuminate\Validation\Rule;

class VehicleNoteRequest extends BaseApiRequest
{
    protected function prepareForValidation()
    {
        if ($noteId = $this->route('noteId')) {
            $this->merge(['noteId' => $noteId]);
        }
    }

    public function authorize(): bool
    {
        // Create operation
        if (! $this->route('noteId')) {
            // Find the vehicle (404 if not found)
            $vehicle = Vehicle::findOrFail($this->route('vehicleId'));

            return $this->user()->can('create', [VehicleNote::class, $vehicle]);
        }

        // Update/delete operation
        $note = VehicleNote::findOrFail($this->route('noteId'));

        return $this->user()->can('update', $note);
    }

    public function rules(): array
    {
        $rules = [
            'title' => ['required', 'string', 'max:255'],
            'note_date' => ['required', 'date'],
            'details' => ['nullable', 'string'],
        ];

        // For update operations, validate that the note exists and belongs to the vehicle
        if ($this->route('noteId')) {
            $rules['noteId'] = [
                'required',
                Rule::exists('vehicle_notes', 'id')->where(function ($query) {
                    $query->where('vehicle_id', $this->route('vehicleId'));
                }),
            ];
        }

        return $rules;
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'note_type.required' => 'نوع الملاحظة مطلوب',
                'note_type.string' => 'يجب أن يكون نوع الملاحظة نصًا',
                'note_type.max' => 'يجب ألا يتجاوز نوع الملاحظة 255 حرفًا',
                'details.string' => 'يجب أن تكون التفاصيل نصية',
            ];
        }

        return [
            'note_type.required' => 'Note type is required',
            'note_type.string' => 'Note type must be a string',
            'note_type.max' => 'Note type must not exceed 255 characters',
            'details.string' => 'Details must be a string',
        ];
    }

    public function toDTO(): VehicleNoteDTO
    {
        $validated = $this->validated();
        $vehicleId = $this->route('vehicleId');
        $currentLanguage = app()->getLocale();

        // Store the original text as-is in the source language
        $details = null;
        if (! empty($validated['details'])) {
            $details = [
                $currentLanguage => $validated['details'],
            ];
        }

        return new VehicleNoteDTO(
            vehicle_id: $vehicleId,
            title: $validated['title'],
            note_date: $validated['note_date'],
            details: $details
        );
    }
}
