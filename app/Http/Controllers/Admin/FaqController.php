<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Faq\ReorderFaqRequest;
use App\Http\Requests\Faq\StoreFaqRequest;
use App\Http\Requests\Faq\UpdateFaqRequest;
use App\Http\Resources\Faq\FaqResource;
use App\Models\Faq;
use App\Services\FaqService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class FaqController extends Controller
{
    protected $faqService;

    public function __construct(FaqService $faqService)
    {
        $this->faqService = $faqService;
    }

    public function index(): JsonResponse
    {
        try {
            $faqs = $this->faqService->getAllFaqsForAdmin();

            return ApiResponse::success([
                'faqs' => FaqResource::collection($faqs),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve FAQs', $e);
        }
    }

    public function store(StoreFaqRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $faq = $this->faqService->store($request->validated());

            DB::commit();

            return ApiResponse::success([
                'faq' => new FaqResource($faq),
            ],
                'FAQ created successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create FAQ', $e);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            $faq = Faq::findOrFail($id);

            return ApiResponse::success([
                'faq' => new FaqResource($faq->load('translations')),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve FAQ', $e);
        }
    }

    public function update(UpdateFaqRequest $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $faq = Faq::findOrFail($id);
            $faq = $this->faqService->update($faq, $request->validated());

            DB::commit();

            return ApiResponse::success([
                'faq' => new FaqResource($faq),
            ],
                'FAQ updated successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update FAQ', $e);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $faq = Faq::findOrFail($id);
            $this->faqService->delete($faq);

            DB::commit();

            return ApiResponse::success([], 'FAQ deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete FAQ', $e);
        }
    }

    public function reorder(ReorderFaqRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $this->faqService->reorder($request->user_type, $request->ids);

            DB::commit();

            return ApiResponse::success([], 'FAQs reordered successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to reorder FAQs', $e);
        }
    }
}
