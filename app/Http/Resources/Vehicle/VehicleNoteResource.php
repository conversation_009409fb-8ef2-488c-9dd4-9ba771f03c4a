<?php

namespace App\Http\Resources\Vehicle;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleNoteResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $details = $this->details;
        if (is_string($details)) {
            $details = json_decode($details, true);
        }

        return [
            'id' => $this->id,
            'vehicle_id' => $this->vehicle_id,
            'title' => $this->title,
            'note_date' => $this->note_date,
            'details' => $details,
        ];
    }
}
