<?php

namespace App\DTOs;

class VehicleReminderDTO
{
    public function __construct(
        public readonly int $vehicle_id,
        public readonly string $reminder_type,
        public readonly bool $is_mileage_based,
        public readonly ?string $reminder_date,
        public readonly ?int $mileage,
        public readonly ?array $review = null
    ) {}

    public function toArray(): array
    {
        return [
            'vehicle_id' => $this->vehicle_id,
            'reminder_type' => $this->reminder_type,
            'is_mileage_based' => $this->is_mileage_based,
            'reminder_date' => $this->reminder_date,
            'mileage' => $this->mileage,
            'review' => $this->review,
        ];
    }
}
