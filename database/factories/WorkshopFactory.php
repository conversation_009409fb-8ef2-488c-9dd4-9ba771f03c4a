<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Workshop;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkshopFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = Workshop::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $locale = config('app.locale');

        return [
            'user_id' => User::factory(), // This will be overridden in the seeder
            'name' => $this->faker->company().' Workshop',
            'description' => [
                $locale => $this->faker->paragraph(3),
            ],
            'phone_number' => $this->faker->phoneNumber(),
            'whatsapp' => $this->faker->phoneNumber(),
            'city' => $this->faker->city(),
            'address' => $this->faker->address(),
            'latitude' => $this->faker->latitude(),
            'longitude' => $this->faker->longitude(),
            'is_active' => true,
            'is_approved' => true,
            'rating' => $this->faker->randomFloat(2, 0, 5),
        ];
    }
}
