<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class VendorResendEmailOtpRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => [
                'required',
                'string',
                'email',
                'exists:users,email',
                function ($attribute, $value, $fail) {
                    $existingOtp = \App\Models\Otp::where('email', $value)
                        ->where('expires_at', '>', \Carbon\Carbon::now())
                        ->first();

                    if ($existingOtp && $existingOtp->next_resend_at && $existingOtp->next_resend_at > \Carbon\Carbon::now()) {
                        $remainingSeconds = abs((int) $existingOtp->next_resend_at->diffInSeconds(\Carbon\Carbon::now()));
                        $fail(app()->getLocale() === 'ar'
                            ? "الرجاء الانتظار {$remainingSeconds} ثوانٍ قبل طلب رمز تحقق آخر."
                            : "Please wait {$remainingSeconds} seconds before requesting another OTP.");
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح',
                'email.exists' => 'هذا البريد الإلكتروني غير مسجل',
            ];
        }

        return [
            'email.required' => 'Email is required',
            'email.email' => 'Please enter a valid email address',
            'email.exists' => 'This email is not registered',
        ];
    }

    protected function afterValidation()
    {
        // Verify that the email belongs to a vendor
        $user = User::where('email', $this->email)->first();

        if (! $user || $user->user_type !== UserType::VENDOR->value) {
            $message = app()->getLocale() === 'ar'
                ? 'هذا البريد الإلكتروني غير مسجل كبائع.'
                : 'This email is not registered as a vendor account';

            $validator = Validator::make($this->all(), []);
            $validator->errors()->add('email', $message);
            $this->failedValidation($validator);
        }
    }
}
