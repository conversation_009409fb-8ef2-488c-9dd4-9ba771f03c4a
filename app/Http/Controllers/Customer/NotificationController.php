<?php

namespace App\Http\Controllers\Customer;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customer\NotificationToggleRequest;
use App\Http\Resources\Customer\NotificationResource;
use App\Services\NotificationService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class NotificationController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function index(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();

            $filters = [
                'type' => $request->type,
                'read_status' => $request->read_status,
                'per_page' => $request->per_page ?? 15,
            ];

            $notifications = $this->notificationService->getUserNotifications($userId, $filters);

            return ApiResponse::success([
                'notifications' => NotificationResource::collection($notifications->items()),
                'unread_count' => $this->notificationService->getUnreadCountForUser($userId),
                'meta' => [
                    'current_page' => $notifications->currentPage(),
                    'last_page' => $notifications->lastPage(),
                    'per_page' => $notifications->perPage(),
                    'total' => $notifications->total(),
                    'next_page_url' => $notifications->nextPageUrl(),
                    'prev_page_url' => $notifications->previousPageUrl(),
                ],
            ], 'Notifications retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve notifications', $e);
        }
    }

    public function show(int $id): JsonResponse
    {
        try {
            $userId = Auth::id();
            $notification = $this->notificationService->getUserNotification($id, $userId);

            if (! $notification) {
                return ApiResponse::error('Notification not found', 404);
            }

            return ApiResponse::success(
                [
                    'notification' => new NotificationResource($notification),
                ],
                'Notification retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve notification', $e);
        }
    }

    public function markAllAsRead(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $updatedCount = $this->notificationService->markAllAsReadForUser($userId);

            return ApiResponse::success([
                'updated_count' => $updatedCount,
                'unread_count' => 0,
            ], 'All notifications marked as read successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to mark all notifications as read', $e);
        }
    }

    public function toggleReadStatus(NotificationToggleRequest $request): JsonResponse
    {
        try {
            $userId = Auth::id();
            $notificationId = $request->validated()['notification_id'];

            // Verify the notification belongs to the authenticated user
            $notification = $this->notificationService->getUserNotification($notificationId, $userId);

            if (! $notification) {
                return ApiResponse::error('Notification not found or does not belong to you', 404);
            }

            $success = $this->notificationService->toggleNotificationReadStatus($notificationId, $userId);

            if (! $success) {
                return ApiResponse::error('Failed to toggle notification status', 500);
            }

            // Get updated notification
            $updatedNotification = $this->notificationService->getUserNotification($notificationId, $userId);
            $unreadCount = $this->notificationService->getUnreadCountForUser($userId);

            return ApiResponse::success([
                'notification' => new NotificationResource($updatedNotification),
                'unread_count' => $unreadCount,
            ], 'Notification status toggled successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to toggle notification status', $e);
        }
    }

    public function getUnreadCount(): JsonResponse
    {
        try {
            $userId = Auth::id();
            $unreadCount = $this->notificationService->getUnreadCountForUser($userId);

            return ApiResponse::success([
                'unread_count' => $unreadCount,
            ], 'Unread count retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to get unread count', $e);
        }
    }
}
