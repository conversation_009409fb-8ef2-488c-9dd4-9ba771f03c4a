<?php

namespace App\Enums;

enum UserType: string
{
    case CUSTOMER = 'customer';
    case VENDOR = 'vendor';
    case ADMIN = 'admin';

    /**
     * Get all enum values as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get all enum values with labels for dropdown
     */
    public static function options(): array
    {
        return [
            ['value' => self::CUSTOMER->value, 'label' => 'Customer'],
            ['value' => self::VENDOR->value, 'label' => 'Vendor/Workshop Owner'],
            ['value' => self::ADMIN->value, 'label' => 'Admin'],
        ];
    }
}
