<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AssignPermissionsRequest;
use App\Http\Requests\Admin\AssignRolesRequest;
use App\Http\Requests\Admin\CreateAdminRequest;
use App\Http\Requests\Admin\UpdateAdminRequest;
use App\Http\Resources\Admin\AdminUserCollection;
use App\Http\Resources\Admin\AdminUserResource;
use App\Http\Resources\Admin\PermissionResource;
use App\Http\Resources\Admin\RoleResource;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class AdminManagementController extends Controller
{
    public function createAdmin(CreateAdminRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'name' => $request->first_name.' '.$request->last_name,
                'email' => $request->email,
                'password' => Hash::make($request->password),
                'user_type' => UserType::ADMIN->value,
                'is_verified' => true,
            ]);

            if ($request->has('roles')) {
                $admin->assignRole($request->roles);
            }

            if ($request->has('permissions')) {
                $admin->givePermissionTo($request->permissions);
            }

            DB::commit();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin),
            ], 'Admin user created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create admin user', $e);
        }
    }

    public function updateAdmin(UpdateAdminRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::where('id', $id)
                ->with('roles', 'permissions')
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            // Prevent updating super admin records
            if ($admin->hasRole('super-admin')) {
                DB::rollBack();

                return ApiResponse::error('Cannot update super admin user', 403);
            }

            $adminData = [];

            if ($request->has('first_name')) {
                $adminData['first_name'] = $request->first_name;
            }

            if ($request->has('last_name')) {
                $adminData['last_name'] = $request->last_name;
            }

            if ($request->has('first_name') || $request->has('last_name')) {
                $firstName = $request->has('first_name') ? $request->first_name : $admin->first_name;
                $lastName = $request->has('last_name') ? $request->last_name : $admin->last_name;
                $adminData['name'] = $firstName.' '.$lastName;
            }

            if ($request->has('email')) {
                $adminData['email'] = $request->email;
            }

            if ($request->has('password')) {
                $adminData['password'] = Hash::make($request->password);
            }

            $admin->update($adminData);

            DB::commit();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin),
            ], 'Admin user updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update admin user', $e);
        }
    }

    public function deleteAdmin(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            if ($admin->hasRole('super-admin')) {
                DB::rollBack();

                return ApiResponse::error('Cannot delete super admin user', 403);
            }

            $admin->delete();

            DB::commit();

            return ApiResponse::success([], 'Admin user deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete admin user', $e);
        }
    }

    public function listAdmins(Request $request): JsonResponse
    {
        try {
            $query = User::with('roles', 'permissions', 'roles.permissions')
                ->where('user_type', UserType::ADMIN->value);

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($query) use ($search) {
                    $query->where('email', 'like', "%{$search}%")
                        ->orWhere(DB::raw('CONCAT(first_name, " ", last_name)'), 'like', "%{$search}%")
                        ->orWhereHas('roles', function ($query) use ($search) {
                            $query->where('name', 'like', "%{$search}%");
                        });
                });
            }

            $admins = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new AdminUserCollection($admins),
                'Admin users retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve admin users', $e);
        }
    }

    public function getAdmin($id): JsonResponse
    {
        try {
            $id = (int) $id;
            $admin = User::with('roles', 'roles.permissions', 'permissions')
                ->where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin),
            ], 'Admin user details retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve admin user details', $e);
        }
    }

    public function assignRoles(AssignRolesRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            // Additional server-side check to prevent super-admin role assignment
            if (in_array('super-admin', $request->roles)) {
                DB::rollBack();

                return ApiResponse::error('The super-admin role cannot be assigned to users. It is a system reserved role.', 403);
            }

            $admin->syncRoles($request->roles);

            DB::commit();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin->load('roles', 'permissions')),
            ], 'Roles assigned successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to assign roles', $e);
        }
    }

    public function assignPermissions(AssignPermissionsRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            $admin->syncPermissions($request->permissions);

            DB::commit();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin->load('roles', 'permissions')),
            ], 'Permissions assigned successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to assign permissions', $e);
        }
    }

    public function listAdminRoles(int $id): JsonResponse
    {
        try {
            $admin = User::where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            $roles = $admin->roles()->with('permissions')->get();

            return ApiResponse::success([
                'roles' => RoleResource::collection($roles),
            ], 'Admin roles retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve admin roles', $e);
        }
    }

    public function listAdminPermissions(int $id): JsonResponse
    {
        try {
            $admin = User::where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            $allPermissions = $admin->getAllPermissions();

            return ApiResponse::success([
                'permissions' => PermissionResource::collection($allPermissions),
            ], 'Admin permissions retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve admin permissions', $e);
        }
    }

    public function getAdminPermissions(int $id): JsonResponse
    {
        try {
            $admin = User::with('roles.permissions', 'permissions')
                ->where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            $directPermissions = $admin->getDirectPermissions();
            $rolesWithPermissions = $admin->roles->map(function ($role) {
                return [
                    'id' => $role->id,
                    'name' => $role->name,
                    'permissions' => PermissionResource::collection($role->permissions),
                ];
            });
            $allPermissions = $admin->getAllPermissions();

            return ApiResponse::success([
                'roles_with_permissions' => $rolesWithPermissions,
                'direct_permissions' => PermissionResource::collection($directPermissions),
                'all_permissions' => PermissionResource::collection($allPermissions),
            ], 'Admin permissions information retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve admin permissions information', $e);
        }
    }

    /**
     * List all soft-deleted admin users
     */
    public function listDeletedAdmins(Request $request): JsonResponse
    {
        try {
            $query = User::onlyTrashed()
                ->where('user_type', UserType::ADMIN->value);

            // Apply search filter if provided
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%");
                });
            }

            $admins = $query->paginate($request->per_page ?? 15);

            return ApiResponse::success(
                new AdminUserCollection($admins),
                'Deleted admin users retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve deleted admin users', $e);
        }
    }

    /**
     * Restore a soft-deleted admin
     */
    public function restoreAdmin(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::onlyTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            $admin->restore();

            DB::commit();

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin),
            ], 'Admin user restored successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to restore admin user', $e);
        }
    }

    /**
     * Permanently delete a soft-deleted admin
     */
    public function permanentlyDeleteAdmin(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $admin = User::withTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::ADMIN->value)
                ->firstOrFail();

            if ($admin->hasRole('super-admin')) {
                DB::rollBack();

                return ApiResponse::error('Cannot permanently delete super admin user', 403);
            }

            // Force delete the admin
            $admin->forceDelete();

            DB::commit();

            return ApiResponse::success([], 'Admin user permanently deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to permanently delete admin user', $e);
        }
    }

    /**
     * Get the currently authenticated admin's profile
     */
    public function getCurrentAdmin(): JsonResponse
    {
        try {
            $admin = Auth::user();

            if (! $admin || $admin->user_type !== UserType::ADMIN) {
                return ApiResponse::error('Not authenticated as admin', 403);
            }

            // Load relationships for complete profile information
            $admin->load('roles', 'roles.permissions', 'permissions');

            return ApiResponse::success([
                'admin' => new AdminUserResource($admin),
            ], 'Current admin profile retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve current admin profile', $e);
        }
    }
}
