name: Laravel Pint Format

on:
  pull_request:
    branches: [ '*' ]
    
  workflow_dispatch:

jobs:
  pint-format:
    name: Checking code style and fixing
    runs-on: ubuntu-latest
    permissions:
      contents: write
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}
          
      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: '8.2'
          extensions: mbstring, dom, fileinfo, mysql, gd, zip, exif, pcntl, bcmath, grpc
          tools: composer:v2
          
      - name: Install Composer dependencies
        run: |
          # Try normal install first
          composer install --prefer-dist --no-progress || \
          # If normal install fails, try with ignore-platform-req for ext-grpc
          composer install --prefer-dist --no-progress --ignore-platform-req=ext-grpc
        
      - name: Run Laravel Pint
        run: |
          if [ -f ./vendor/bin/pint ]; then
            ./vendor/bin/pint
          else
            echo "Laravel Pint not found, installing..."
            composer require laravel/pint --dev
            ./vendor/bin/pint
          fi
        
      - name: Commit changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Apply Laravel Pint formatting changes
          commit_user_name: GitHub Action Bot
          commit_user_email: <EMAIL>
