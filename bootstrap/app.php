<?php

use App\Helpers\ApiResponse;
use App\Http\Middleware\EnsureCustomerUser;
use App\Http\Middleware\EnsureVendorUser;
use App\Http\Middleware\SetLocaleFromHeaderMiddleware;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Foundation\Application;
use Illuminate\Foundation\Configuration\Exceptions;
use Illuminate\Foundation\Configuration\Middleware;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\HttpException;

return Application::configure(basePath: dirname(__DIR__))
    ->withRouting(
        web: __DIR__.'/../routes/web.php',
        api: __DIR__.'/../routes/api.php',
        commands: __DIR__.'/../routes/console.php',
        health: '/up',
    )
    ->withMiddleware(function (Middleware $middleware): void {
        // Trust ALB proxies for HTTPS detection
        $middleware->trustProxies(at: '*');

        // API middleware group
        $middleware->api([
            SetLocaleFromHeaderMiddleware::class,
        ]);

        // Named middleware
        $middleware->alias([
            'customer' => EnsureCustomerUser::class,
            'vendor' => EnsureVendorUser::class,
            'role' => \Spatie\Permission\Middleware\RoleMiddleware::class,
            'permission' => \Spatie\Permission\Middleware\PermissionMiddleware::class,
            'role_or_permission' => \Spatie\Permission\Middleware\RoleOrPermissionMiddleware::class,
        ]);
    })
    ->withExceptions(function (Exceptions $exceptions): void {
        // Handle authentication exceptions
        $exceptions->renderable(function (AuthenticationException $e, $request) {
            return ApiResponse::error('Unauthenticated.', 401);
        });

        // Handle validation exceptions
        $exceptions->renderable(function (ValidationException $e, $request) {
            return ApiResponse::validationError($e->errors());
        });

        // Handle HTTP exceptions
        $exceptions->renderable(function (HttpException $e, $request) {
            try {
                return ApiResponse::errorWithLog($e->getMessage(), $e, $e->getStatusCode());
            } catch (\Exception $fallbackException) {
                return ApiResponse::safeError('HTTP Error: '.$e->getMessage(), $e->getStatusCode());
            }
        });

        // Handle all other exceptions
        $exceptions->renderable(function (\Throwable $e, $request) {
            try {
                return ApiResponse::errorWithLog('Internal Server Error', $e, 500);
            } catch (\Exception $fallbackException) {
                return ApiResponse::safeError('Internal Server Error: '.$e->getMessage(), 500);
            }
        });
    })->create();
