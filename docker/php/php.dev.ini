; Development PHP configuration
upload_max_filesize = 100M
post_max_size = 100M
memory_limit = 512M
max_execution_time = 600

; Development: OpCache settings optimized for development
opcache.enable = 1
opcache.memory_consumption = 128
opcache.interned_strings_buffer = 8
opcache.max_accelerated_files = 10000
opcache.validate_timestamps = 1
opcache.revalidate_freq = 0
opcache.enable_cli = 1

; Development: Error handling for debugging
display_errors = On
display_startup_errors = On
error_reporting = E_ALL
log_errors = On
error_log = /dev/stderr

; Development: Xdebug settings
xdebug.mode = debug
xdebug.start_with_request = yes
xdebug.client_host = host.docker.internal
xdebug.client_port = 9003
xdebug.idekey = VSCODE

; Session
session.save_handler = redis
session.save_path = "tcp://redis:6379"

; Redis
redis.session.locking_enabled = 1
redis.session.lock_retries = -1
redis.session.lock_wait_time = 10000
