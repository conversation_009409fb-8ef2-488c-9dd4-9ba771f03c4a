<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CustomerResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'phone_number' => $this->phone_number,
            'gender' => $this->gender?->value,
            'is_verified' => $this->is_verified,
            'vehicle_count' => $this->vehicles_count ?? 0,
            'avatar_url' => $this->getFirstMediaUrl('avatar') ?? null,
        ];
    }
}
