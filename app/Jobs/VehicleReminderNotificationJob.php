<?php

namespace App\Jobs;

use App\Models\User;
use App\Models\VehicleReminder;
use App\Services\FcmTokenService;
use App\Services\FirebaseService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class VehicleReminderNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected VehicleReminder $reminder;

    protected User $user;

    public function __construct(VehicleReminder $reminder, User $user)
    {
        $this->reminder = $reminder;
        $this->user = $user;
    }

    public function handle(FcmTokenService $fcmTokenService, FirebaseService $firebaseService): void
    {
        try {
            Log::info('Starting vehicle reminder notification job', [
                'reminder_id' => $this->reminder->id,
                'user_id' => $this->user->id,
                'vehicle_name' => $this->reminder->vehicle->name ?? 'Unknown',
                'reminder_type' => $this->reminder->reminder_type,
            ]);

            // Get all active FCM tokens for this specific user
            $fcmTokens = $fcmTokenService->getUserActiveTokens($this->user->id);

            if (empty($fcmTokens)) {
                Log::info('No active FCM tokens found for vehicle reminder notification', [
                    'reminder_id' => $this->reminder->id,
                    'user_id' => $this->user->id,
                ]);

                return;
            }

            Log::info('Found FCM tokens for vehicle reminder notification', [
                'reminder_id' => $this->reminder->id,
                'user_id' => $this->user->id,
                'token_count' => count($fcmTokens),
            ]);

            // Prepare multilingual notification data
            $titleAr = 'تذكير المركبة 🚗';
            $titleEn = 'Vehicle Reminder 🚗';

            $vehicleName = $this->reminder->vehicle->name ?? 'Your Vehicle';
            $reminderType = $this->reminder->reminder_type;
            $reminderDate = $this->reminder->reminder_date->format('Y-m-d');

            // Create body messages
            $bodyAr = "مركبتك {$vehicleName} مستحقة للصيانة ({$reminderType})";
            $bodyEn = "Your vehicle {$vehicleName} is due for maintenance ({$reminderType})";

            $data = [
                'type' => 'vehicle_reminder',
                'reminder_id' => (string) $this->reminder->id,
                'vehicle_id' => (string) $this->reminder->vehicle_id,
                'vehicle_name' => $vehicleName,
                'reminder_type' => $reminderType,
                'reminder_date' => $reminderDate,
                'title_ar' => $titleAr,
                'title_en' => $titleEn,
                'body_ar' => $bodyAr,
                'body_en' => $bodyEn,
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            ];

            // Send push notification to all user's tokens
            $successCount = 0;
            $failureCount = 0;

            foreach ($fcmTokens as $token) {
                try {
                    // Use English as default for push notification display
                    $result = $firebaseService->sendToDevice($token, $titleEn, $bodyEn, $data);
                    Log::info('FCM vehicle reminder notification sent', [
                        'reminder_id' => $this->reminder->id,
                        'user_id' => $this->user->id,
                        'token' => substr($token, 0, 20).'...',
                        'result' => $result,
                    ]);

                    if ($result['success']) {
                        $successCount++;
                        // Mark token as used
                        $fcmTokenService->markTokenAsUsed($token);
                    } else {
                        $failureCount++;
                        Log::warning('FCM vehicle reminder notification failed', [
                            'reminder_id' => $this->reminder->id,
                            'user_id' => $this->user->id,
                            'token' => substr($token, 0, 20).'...',
                            'error' => $result['error'] ?? 'Unknown error',
                        ]);
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    Log::warning('Failed to send vehicle reminder notification to FCM token', [
                        'reminder_id' => $this->reminder->id,
                        'user_id' => $this->user->id,
                        'token' => substr($token, 0, 20).'...',
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('Completed vehicle reminder notification job', [
                'reminder_id' => $this->reminder->id,
                'user_id' => $this->user->id,
                'total_tokens' => count($fcmTokens),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
            ]);

        } catch (\Exception $e) {
            Log::error('Vehicle reminder notification job failed', [
                'reminder_id' => $this->reminder->id,
                'user_id' => $this->user->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to mark job as failed
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Vehicle reminder notification job permanently failed', [
            'reminder_id' => $this->reminder->id,
            'user_id' => $this->user->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
