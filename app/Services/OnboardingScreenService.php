<?php

namespace App\Services;

use App\Models\OnboardingScreen;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;

class OnboardingScreenService
{
    public function getAllScreens(string $locale = 'en'): Collection
    {
        return OnboardingScreen::with(['translations' => function ($query) use ($locale) {
            $query->where('locale', $locale);
        }])
            ->where('is_active', true)
            ->orderBy('order')
            ->get();
    }

    public function getAllScreensForAdmin(): Collection
    {
        return OnboardingScreen::with('translations')
            ->orderBy('order')
            ->get();
    }

    public function getScreenById(int $id): ?OnboardingScreen
    {
        return OnboardingScreen::with('translations')->find($id);
    }

    public function store(array $data, ?UploadedFile $image = null): OnboardingScreen
    {
        return DB::transaction(function () use ($data, $image) {
            // Handle image upload if provided
            $imagePath = null;
            if ($image) {
                $imagePath = $image->store('onboarding', 'public');
            }

            // Create onboarding screen
            $screen = OnboardingScreen::create([
                'order' => $data['order'] ?? OnboardingScreen::max('order') + 1,
                'image' => $imagePath,
                'is_active' => $data['is_active'] ?? true,
            ]);

            // Create translations
            foreach ($data['translations'] as $locale => $translation) {
                $screen->translations()->create([
                    'locale' => $locale,
                    'title' => $translation['title'],
                    'description' => $translation['description'],
                ]);
            }

            return $screen->load('translations');
        });
    }

    public function update(OnboardingScreen $screen, array $data, ?UploadedFile $image = null): OnboardingScreen
    {
        return DB::transaction(function () use ($screen, $data, $image) {
            // Handle image upload if provided
            if ($image) {
                // Delete old image if exists
                if ($screen->image) {
                    Storage::disk('public')->delete($screen->image);
                }
                $imagePath = $image->store('onboarding', 'public');
                $screen->image = $imagePath;
            }

            // Update onboarding screen
            $screen->order = $data['order'] ?? $screen->order;
            $screen->is_active = $data['is_active'] ?? $screen->is_active;
            $screen->save();

            // Update translations
            if (isset($data['translations'])) {
                foreach ($data['translations'] as $locale => $translation) {
                    $screen->translations()->updateOrCreate(
                        ['locale' => $locale],
                        [
                            'title' => $translation['title'],
                            'description' => $translation['description'],
                        ]
                    );
                }
            }

            return $screen->load('translations');
        });
    }

    public function delete(OnboardingScreen $screen): bool
    {
        return DB::transaction(function () use ($screen) {
            // Delete image if exists
            if ($screen->image) {
                Storage::disk('public')->delete($screen->image);
            }

            // Delete translations (will cascade due to foreign key constraint)
            return $screen->delete();
        });
    }

    public function reorder(array $screenIds): bool
    {
        return DB::transaction(function () use ($screenIds) {
            foreach ($screenIds as $order => $id) {
                OnboardingScreen::where('id', $id)->update(['order' => $order + 1]);
            }

            return true;
        });
    }
}
