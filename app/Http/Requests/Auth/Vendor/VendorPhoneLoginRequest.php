<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\User;

class VendorPhoneLoginRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone_number' => 'required|string|regex:/^\+[1-9]\d{1,14}$/|exists:users,phone_number',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.exists' => 'المستخدم غير مسجل',
            ];
        }

        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.exists' => 'This phone number is not registered. Please sign up first',
        ];
    }

    protected function afterValidation()
    {
        $user = User::where('phone_number', $this->phone_number)->first();

        if (! $user || $user->user_type !== UserType::VENDOR) {
            $validator = \Illuminate\Support\Facades\Validator::make($this->all(), []);
            $validator->errors()->add('phone_number', 'The User is not a vendor');
            $this->failedValidation($validator);
        }
    }
}
