<?php

namespace App\Observers;

use App\Models\Offer;
use App\Services\NotificationService;
use Illuminate\Support\Facades\Log;

class OfferObserver
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function updated(Offer $offer): void
    {
        // Check if offer was just activated
        if ($offer->isDirty('is_active') && $offer->is_active) {
            $this->createNotificationsForActivatedOffer($offer);
        }
    }

    private function createNotificationsForActivatedOffer(Offer $offer): void
    {
        try {
            $notification = $this->notificationService->createOfferNotification($offer);
            Log::info('Created common notification for activated offer', [
                'offer_id' => $offer->id,
                'notification_id' => $notification->id,
                'offer_title' => $offer->title,
            ]);
        } catch (\Exception $e) {
            Log::error('Failed to create notification for activated offer: '.$e->getMessage(), [
                'offer_id' => $offer->id,
                'exception' => $e,
            ]);
        }
    }
}
