<?php

namespace App\Enums;

enum SupportRequestStatus: string
{
    case PENDING = 'pending';
    case OPEN = 'open';
    case CLOSED = 'closed';

    /**
     * Get all available statuses as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Check if a status is valid
     */
    public static function isValid(string $status): bool
    {
        return in_array($status, self::values());
    }

    /**
     * Get options for form select fields
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[] = [
                'label' => __('support_request_statuses.'.$case->value),
                'value' => $case->value,
            ];
        }

        return $options;
    }
}
