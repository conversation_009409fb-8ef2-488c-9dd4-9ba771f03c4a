<?php

namespace App\Http\Resources\Vehicle;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleDetailsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_id' => $this->user_id,
            'name' => $this->name,
            'make_brand' => $this->make_brand,
            'model_year' => $this->model_year,
            'vin_number' => $this->vin_number,
            'chassis_number' => $this->chassis_number,
            'mileage' => $this->mileage,
            'vehicle_type' => $this->vehicle_type,
            'plate_number' => $this->plate_number,
            'services' => VehicleServiceResource::collection(
                $this->whenLoaded('services')
            ),
            'reminders' => VehicleReminderResource::collection(
                $this->whenLoaded('reminders')
            ),
            'notes' => VehicleNoteResource::collection(
                $this->whenLoaded('notes')
            ),
        ];
    }
}
