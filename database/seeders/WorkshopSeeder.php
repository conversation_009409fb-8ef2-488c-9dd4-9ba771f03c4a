<?php

namespace Database\Seeders;

use App\Enums\UserType;
use App\Models\User;
use App\Models\Workshop;
use App\Models\WorkshopService;
use Illuminate\Database\Seeder;

class WorkshopSeeder extends Seeder
{
    public function run()
    {
        $vendors = User::where('user_type', UserType::VENDOR->value)->get();

        if ($vendors->isEmpty()) {
            $vendors = User::factory()->vendor()->count(5)->create([
                'is_verified' => true,
            ]);
        }

        foreach ($vendors as $vendor) {
            if ($vendor->workshops()->count() > 0) {
                continue;
            }

            $workshop = Workshop::factory()->create([
                'user_id' => $vendor->id,
            ]);

            // Create 3-6 services for the workshop
            $servicesCount = rand(3, 6);
            WorkshopService::factory()->count($servicesCount)->create([
                'workshop_id' => $workshop->id,
            ]);
        }
    }
}
