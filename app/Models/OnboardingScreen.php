<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class OnboardingScreen extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'order',
        'image',
        'is_active',
    ];

    protected $casts = [
        'is_active' => 'boolean',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // Load relationships to avoid N+1 queries
            $model->load(['translations']);

            // Cascade delete to related models
            $model->translations->each(function ($item) {
                $item->delete();
            });
        });
    }

    public function translations(): HasMany
    {
        return $this->hasMany(OnboardingScreenTranslation::class);
    }

    public function translate(string $locale)
    {
        return $this->translations->firstWhere('locale', $locale);
    }
}
