<?php

namespace App\Http\Controllers\Customer;

use App\Enums\SupportRequestStatus;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customer\CreateSupportRequestRequest;
use App\Http\Resources\Admin\SupportRequestCollection;
use App\Http\Resources\Customer\SupportRequestResource;
use App\Models\SupportRequest;
use App\Services\NotificationService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

class SupportRequestController extends Controller
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Display a listing of the user's support requests.
     */
    public function index(Request $request): JsonResponse
    {
        try {
            $userId = Auth::id();

            $supportRequests = SupportRequest::where('user_id', $userId)
                ->orderBy('created_at', 'desc')
                ->paginate($request->per_page ?? 10);

            return ApiResponse::success([
                new SupportRequestCollection($supportRequests),
            ], 'Support requests retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve support requests', $e);
        }
    }

    /**
     * Store a newly created support request in storage.
     */
    public function store(CreateSupportRequestRequest $request): JsonResponse
    {
        try {
            $user = Auth::user();

            $supportRequest = new SupportRequest([
                'request_id' => SupportRequest::generateRequestId(),
                'user_id' => $user->id,
                'subject' => $request->subject,
                'issue_type' => $request->issue_type,
                'details' => $request->details,
                'status' => SupportRequestStatus::PENDING,
            ]);

            $supportRequest->save();

            // Trigger admin notification for new support request
            $this->notificationService->createSupportRequestNotification($supportRequest);

            return ApiResponse::success(
                new SupportRequestResource($supportRequest),
                'Message sent. We\'ll get back to you soon',
                201
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to create support request', $e);
        }
    }

    /**
     * Display the specified support request.
     */
    public function show(int $id): JsonResponse
    {
        try {
            $userId = Auth::id();

            $supportRequest = SupportRequest::where('id', $id)
                ->where('user_id', $userId)
                ->firstOrFail();

            return ApiResponse::success(
                new SupportRequestResource($supportRequest),
                'Support request retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve support request', $e);
        }
    }
}
