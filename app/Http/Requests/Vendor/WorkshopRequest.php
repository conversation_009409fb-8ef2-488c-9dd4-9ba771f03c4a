<?php

namespace App\Http\Requests\Vendor;

use App\DTOs\Workshop\WorkshopDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Workshop;

class WorkshopRequest extends BaseApiRequest
{
    public function authorize()
    {
        $workshop = $this->route('workshop');

        // For create requests, just check if user is a vendor
        if (! $workshop) {
            return $this->user() && $this->user()->isVendor();
        }

        // For update requests, check if the workshop belongs to this vendor
        return $this->user() &&
               $this->user()->isVendor() &&
               $workshop->user_id === $this->user()->id;
    }

    public function rules()
    {
        return [
            'name' => 'required|string|max:255',
            'description' => 'nullable|string',
            'phone_number' => 'nullable|string|max:20',
            'whatsapp' => 'nullable|string|max:20',
            'city' => 'nullable|string|max:100',
            'address' => 'nullable|string|max:255',
            'latitude' => 'nullable|string|max:30',
            'longitude' => 'nullable|string|max:30',
            'is_active' => 'nullable|boolean',
            'photo' => 'nullable|image|max:5120', // 5MB max
        ];
    }

    /**
     * Convert request to DTO
     *
     * @param  int|null  $user_id  Optional user ID to override the authenticated user
     */
    public function toDTO(?int $user_id = null): WorkshopDTO
    {
        return WorkshopDTO::fromRequest($this, $user_id);
    }
}
