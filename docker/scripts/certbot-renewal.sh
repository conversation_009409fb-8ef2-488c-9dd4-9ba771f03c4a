#!/bin/bash

# Certbot renewal script for Supervisor
# This script runs continuously and checks for certificate renewal every 12 hours

set -e

# Configuration
RENEWAL_INTERVAL=43200  # 12 hours in seconds
LOG_FILE="/var/log/certbot-renewal.log"

# Function to log messages
log_message() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - $1" | tee -a "$LOG_FILE"
}

# Function to renew certificates
renew_certificates() {
    log_message "Starting certificate renewal check..."
    
    # Change to the working directory
    cd /var/www
    
    # Try to renew certificates using certbot renew command
    if /usr/bin/certbot renew --webroot -w /var/www/certbot --quiet --no-self-upgrade; then
        log_message "Certificate renewal check completed successfully"
        
        # Send reload signal to nginx using shared volume approach
        # Create a reload trigger file that nginx can monitor
        touch /var/www/certbot/reload-nginx 2>/dev/null || true
        log_message "Certificate renewal completed - NGINX will reload automatically"
    else
        log_message "Certificate renewal check failed or no renewal needed"
    fi
}

# Function to check if certificates exist and create them if they don't
check_and_create_certificates() {
    local domain="${NGINX_HOST:-localhost}"
    local cert_path="/etc/letsencrypt/live/$domain/fullchain.pem"
    
    if [ ! -f "$cert_path" ]; then
        log_message "No certificates found for $domain. Certificates should be obtained during deployment."
    else
        log_message "Certificates already exist for $domain"
        
        # Check certificate expiry
        if command -v openssl > /dev/null; then
            expiry_date=$(openssl x509 -enddate -noout -in "$cert_path" | cut -d= -f2)
            log_message "Certificate expires: $expiry_date"
        fi
    fi
}

# Main loop
log_message "Certbot renewal service started"

# Initial certificate check
check_and_create_certificates

# Continuous renewal loop
while true; do
    renew_certificates
    log_message "Sleeping for $RENEWAL_INTERVAL seconds..."
    sleep $RENEWAL_INTERVAL
done
