.git
.gitignore
.dockerignore
.env
Dockerfile
docker-compose.yml
# docker/ - commented out as we need docker config files for build
node_modules/
vendor/
storage/*.key
*.log
.phpunit.result.cache
.phpunit.cache
.phpunit.cache/*
.idea
.vscode
*.sublime-*
*.swp
*.swo
.DS_Store
Thumbs.db
*.sqlite
*.sqlite-journal
.php_cs.cache
.phpcs.cache
.phpcs.xml
.php_cs.dist
.php_cs
.phpunit.result.cache
.php_cs.cache
.php-cs-fixer.cache
.php-cs-fixer.php
.phpcs.xml.dist
.php_cs.dist
.php_cs.cache
.php_cs.dist
.php_cs.cache
.php-cs-fixer.cache
.php-cs-fixer.php
.phpcs.xml.dist
.php_cs.dist
.php_cs.cache
.php_cs.dist
.php_cs.cache
.php-cs-fixer.cache
.php-cs-fixer.php
.phpcs.xml.dist
.php_cs.dist
.php_cs.cache
.php_cs.dist
.php_cs.cache
.php-cs-fixer.cache
.php-cs-fixer.php
.phpcs.xml.dist
.php_cs.dist
.php_cs.cache
.php_cs.dist
