<?php

namespace App\Http\Resources\Admin;

use App\Models\VehicleReminder;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class MaintenanceLogResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Get the next upcoming reminder for this vehicle
        $nextReminder = VehicleReminder::where('vehicle_id', $this->vehicle_id)
            ->whereDate('reminder_date', '>=', Carbon::now())
            ->orderBy('reminder_date', 'asc')
            ->first();

        return [
            'id' => $this->id,
            'vehicle_name' => $this->vehicle->name ?? 'Unknown',
            'service_type' => $this->service_type,
            'service_date' => $this->service_date,
            'next_service_type' => $nextReminder ? $nextReminder->reminder_type : null,
            'next_service_date' => $nextReminder ? $nextReminder->reminder_date : null,
        ];
    }
}
