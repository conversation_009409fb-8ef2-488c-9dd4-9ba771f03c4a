<?php

namespace App\Services;

use Exception;
use Google\Cloud\Firestore\FirestoreClient;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class FirebaseService
{
    private $projectId;

    private $fcmUrl;

    public function __construct()
    {
        $this->projectId = config('firebase.project_id');
        $this->fcmUrl = "https://fcm.googleapis.com/v1/projects/{$this->projectId}/messages:send";
        // We now use credentials array directly from config instead of file
    }

    /**
     * Send FCM notification to a single device
     */
    public function sendToDevice(string $token, string $title, string $body, array $data = [])
    {
        try {
            $accessToken = $this->getAccessToken();

            $message = [
                'message' => [
                    'token' => $token,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                ],
            ];

            Log::info('FCM message', $message);

            if (! empty($data)) {
                $message['message']['data'] = $data;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->fcmUrl, $message);

            return [
                'success' => $response->successful(),
                'response' => $response->json(),
                'status_code' => $response->status(),
            ];
        } catch (Exception $e) {
            Log::error('Firebase Service Error: '.$e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Send FCM notification to multiple devices
     */
    public function sendToMultipleDevices(array $tokens, string $title, string $body, array $data = [])
    {
        $results = [];
        $accessToken = $this->getAccessToken();

        foreach ($tokens as $token) {
            $result = $this->sendToDevice($token, $title, $body, $data);
            $results[] = array_merge($result, ['token' => $token]);
        }

        return $results;
    }

    /**
     * Send notification to a topic
     */
    public function sendToTopic(string $topic, string $title, string $body, array $data = [])
    {
        try {
            $accessToken = $this->getAccessToken();

            $message = [
                'message' => [
                    'topic' => $topic,
                    'notification' => [
                        'title' => $title,
                        'body' => $body,
                    ],
                ],
            ];

            if (! empty($data)) {
                $message['message']['data'] = $data;
            }

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->fcmUrl, $message);

            return [
                'success' => $response->successful(),
                'response' => $response->json(),
                'status_code' => $response->status(),
            ];
        } catch (Exception $e) {
            Log::error('Firebase Service Error: '.$e->getMessage());

            return [
                'success' => false,
                'error' => $e->getMessage(),
            ];
        }
    }

    /**
     * Get OAuth 2.0 access token using service account
     */
    private function getAccessToken()
    {
        // Get service account credentials from config
        $serviceAccount = config('firebase.credentials');

        $now = time();
        $payload = [
            'iss' => $serviceAccount['client_email'],
            'scope' => 'https://www.googleapis.com/auth/firebase.messaging',
            'aud' => 'https://oauth2.googleapis.com/token',
            'iat' => $now,
            'exp' => $now + 3600,
        ];

        // Create JWT
        $header = json_encode(['typ' => 'JWT', 'alg' => 'RS256']);
        $payload = json_encode($payload);

        $base64Header = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($header));
        $base64Payload = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($payload));

        $signature = '';
        openssl_sign($base64Header.'.'.$base64Payload, $signature, $serviceAccount['private_key'], 'SHA256');
        $base64Signature = str_replace(['+', '/', '='], ['-', '_', ''], base64_encode($signature));

        $jwt = $base64Header.'.'.$base64Payload.'.'.$base64Signature;

        // Exchange JWT for access token
        $response = Http::asForm()->post('https://oauth2.googleapis.com/token', [
            'grant_type' => 'urn:ietf:params:oauth:grant-type:jwt-bearer',
            'assertion' => $jwt,
        ]);

        if ($response->successful()) {
            return $response->json()['access_token'];
        }

        throw new Exception('Failed to get access token: '.$response->body());
    }

    /**
     * Initialize Firestore client (if needed for other operations)
     */
    public function getFirestoreClient()
    {
        return new FirestoreClient([
            'projectId' => $this->projectId,
            'keyFile' => config('firebase.credentials'),
        ]);
    }
}
