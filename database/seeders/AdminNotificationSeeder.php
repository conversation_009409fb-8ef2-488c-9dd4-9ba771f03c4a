<?php

namespace Database\Seeders;

use App\Enums\SupportRequestIssueType;
use App\Enums\SupportRequestStatus;
use App\Enums\UserType;
use App\Models\SupportRequest;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Database\Seeder;

class AdminNotificationSeeder extends Seeder
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('🔔 Seeding Admin Notifications...');

        // Check if we have admin users
        $adminCount = User::where('user_type', UserType::ADMIN)->count();
        if ($adminCount === 0) {
            $this->command->warn('⚠️  No admin users found. Creating a test admin user...');
            $this->createTestAdmin();
        }

        $this->command->info("📊 Found {$adminCount} admin user(s)");

        // Seed different types of notifications
        $this->seedVendorNotifications();
        $this->seedSupportRequestNotifications();
        $this->seedSystemAlertNotifications();

        $this->command->info('✅ Admin notifications seeded successfully!');
    }

    /**
     * Create a test admin user if none exists
     */
    private function createTestAdmin(): void
    {
        User::create([
            'name' => 'Test Admin',
            'first_name' => 'Test',
            'last_name' => 'Admin',
            'phone_number' => '+966500000001',
            'email' => '<EMAIL>',
            'password' => 'password123',
            'is_verified' => true,
            'user_type' => UserType::ADMIN,
        ]);

        $this->command->info('✅ Test admin user created: <EMAIL>');
    }

    /**
     * Seed vendor-related notifications
     */
    private function seedVendorNotifications(): void
    {
        $this->command->info('👥 Seeding vendor notifications...');

        $vendorData = [
            [
                'name' => 'Ahmed Al-Rashid Workshop',
                'first_name' => 'Ahmed',
                'last_name' => 'Al-Rashid',
                'phone_number' => '+966501234567',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Fahad Auto Service',
                'first_name' => 'Fahad',
                'last_name' => 'Al-Otaibi',
                'phone_number' => '+966502345678',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Riyadh Car Center',
                'first_name' => 'Mohammed',
                'last_name' => 'Al-Saud',
                'phone_number' => '+966503456789',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Jeddah Motors',
                'first_name' => 'Khalid',
                'last_name' => 'Al-Ghamdi',
                'phone_number' => '+966504567890',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Dammam Auto Repair',
                'first_name' => 'Saad',
                'last_name' => 'Al-Dosari',
                'phone_number' => '+966505678901',
                'email' => '<EMAIL>',
            ],
        ];

        foreach ($vendorData as $index => $data) {
            // Create vendor user
            $vendor = User::create([
                'name' => $data['name'],
                'first_name' => $data['first_name'],
                'last_name' => $data['last_name'],
                'phone_number' => $data['phone_number'],
                'email' => $data['email'],
                'is_verified' => $index < 3, // First 3 are verified
                'user_type' => UserType::VENDOR,
                'is_vendor_account_approved' => $index < 2, // First 2 are approved
                'created_at' => now()->subDays(rand(1, 30)),
            ]);

            // Create vendor registration notification
            $this->notificationService->createVendorRegistrationNotification($vendor);

            // Create vendor approval request notification for verified vendors
            if ($vendor->is_verified && ! $vendor->is_vendor_account_approved) {
                $this->notificationService->createVendorApprovalRequestNotification($vendor);
            }
        }

        $this->command->info('✅ Created 5 vendor users with registration and approval notifications');
    }

    /**
     * Seed support request notifications
     */
    private function seedSupportRequestNotifications(): void
    {
        $this->command->info('🎧 Seeding support request notifications...');

        $customerData = [
            [
                'name' => 'Sara Al-Zahra',
                'first_name' => 'Sara',
                'last_name' => 'Al-Zahra',
                'phone_number' => '+************',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Omar Al-Mutairi',
                'first_name' => 'Omar',
                'last_name' => 'Al-Mutairi',
                'phone_number' => '+************',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Fatima Al-Harbi',
                'first_name' => 'Fatima',
                'last_name' => 'Al-Harbi',
                'phone_number' => '+966508901234',
                'email' => '<EMAIL>',
            ],
            [
                'name' => 'Abdullah Al-Shehri',
                'first_name' => 'Abdullah',
                'last_name' => 'Al-Shehri',
                'phone_number' => '+966509012345',
                'email' => '<EMAIL>',
            ],
        ];

        $supportRequests = [
            [
                'subject' => 'Unable to book appointment',
                'issue_type' => SupportRequestIssueType::BUG_REPORT,
                'details' => 'I am trying to book an appointment but the app keeps crashing when I select a date.',
            ],
            [
                'subject' => 'Payment not processed',
                'issue_type' => SupportRequestIssueType::COMPLAINT,
                'details' => 'I made a payment for my service but it shows as pending. Please help resolve this issue.',
            ],
            [
                'subject' => 'Add new service type',
                'issue_type' => SupportRequestIssueType::FEATURE_REQUEST,
                'details' => 'Can you please add motorcycle maintenance services to the platform?',
            ],
            [
                'subject' => 'Account verification issue',
                'issue_type' => SupportRequestIssueType::INQUIRY,
                'details' => 'I have been waiting for account verification for 3 days. When will it be completed?',
            ],
            [
                'subject' => 'Workshop location incorrect',
                'issue_type' => SupportRequestIssueType::COMPLAINT,
                'details' => 'The workshop location shown on the map is incorrect. The actual location is different.',
            ],
            [
                'subject' => 'App performance issues',
                'issue_type' => SupportRequestIssueType::BUG_REPORT,
                'details' => 'The app is very slow and takes a long time to load. Please optimize the performance.',
            ],
        ];

        foreach ($customerData as $index => $customerInfo) {
            // Create customer user
            $customer = User::create([
                'name' => $customerInfo['name'],
                'first_name' => $customerInfo['first_name'],
                'last_name' => $customerInfo['last_name'],
                'phone_number' => $customerInfo['phone_number'],
                'email' => $customerInfo['email'],
                'is_verified' => true,
                'user_type' => UserType::CUSTOMER,
                'created_at' => now()->subDays(rand(5, 60)),
            ]);

            // Create multiple support requests for some customers
            $requestCount = $index < 2 ? 2 : 1; // First 2 customers have 2 requests each

            for ($i = 0; $i < $requestCount; $i++) {
                $requestIndex = ($index * 2) + $i;
                if ($requestIndex >= count($supportRequests)) {
                    break;
                }

                $requestData = $supportRequests[$requestIndex];

                $supportRequest = SupportRequest::create([
                    'request_id' => 'SUP-'.str_pad($requestIndex + 1001, 5, '0', STR_PAD_LEFT),
                    'user_id' => $customer->id,
                    'subject' => $requestData['subject'],
                    'issue_type' => $requestData['issue_type'],
                    'details' => $requestData['details'],
                    'status' => SupportRequestStatus::PENDING,
                    'created_at' => now()->subDays(rand(1, 15)),
                ]);

                // Create support request notification
                $this->notificationService->createSupportRequestNotification($supportRequest);
            }
        }

        $this->command->info('✅ Created 4 customers with 6 support requests and notifications');
    }

    /**
     * Seed system alert notifications
     */
    private function seedSystemAlertNotifications(): void
    {
        $this->command->info('⚠️  Seeding system alert notifications...');

        $systemAlerts = [
            [
                'title_en' => 'Scheduled Maintenance',
                'title_ar' => 'صيانة مجدولة',
                'message_en' => 'The system will undergo scheduled maintenance on '.now()->addDays(2)->format('Y-m-d').' from 02:00 to 04:00 AM.',
                'message_ar' => 'سيخضع النظام لصيانة مجدولة في '.now()->addDays(2)->format('Y-m-d').' من الساعة 02:00 إلى 04:00 صباحاً.',
            ],
            [
                'title_en' => 'High Server Load Alert',
                'title_ar' => 'تنبيه حمولة الخادم العالية',
                'message_en' => 'Server experiencing high load. Response times may be slower than usual.',
                'message_ar' => 'يواجه الخادم حمولة عالية. قد تكون أوقات الاستجابة أبطأ من المعتاد.',
            ],
            [
                'title_en' => 'New Feature Release',
                'title_ar' => 'إطلاق ميزة جديدة',
                'message_en' => 'New vehicle tracking feature has been released. Please review the admin documentation.',
                'message_ar' => 'تم إطلاق ميزة تتبع المركبات الجديدة. يرجى مراجعة وثائق المشرف.',
            ],
            [
                'title_en' => 'Database Backup Completed',
                'title_ar' => 'اكتملت النسخة الاحتياطية لقاعدة البيانات',
                'message_en' => 'Weekly database backup completed successfully at '.now()->subHours(2)->format('Y-m-d H:i'),
                'message_ar' => 'اكتملت النسخة الاحتياطية الأسبوعية لقاعدة البيانات بنجاح في '.now()->subHours(2)->format('Y-m-d H:i'),
            ],
            [
                'title_en' => 'Security Update Required',
                'title_ar' => 'مطلوب تحديث أمني',
                'message_en' => 'Critical security update available. Please schedule update during maintenance window.',
                'message_ar' => 'يتوفر تحديث أمني مهم. يرجى جدولة التحديث خلال نافزة الصيانة.',
            ],
        ];

        foreach ($systemAlerts as $alert) {
            $this->notificationService->createSystemAlertNotification(
                $alert['title_en'],
                $alert['message_en'],
                $alert['title_ar'],
                $alert['message_ar']
            );

            // Add some delay to create different timestamps
            sleep(1);
        }

        $this->command->info('✅ Created 5 system alert notifications');
    }
}
