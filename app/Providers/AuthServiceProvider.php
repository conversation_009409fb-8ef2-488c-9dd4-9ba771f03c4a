<?php

namespace App\Providers;

use App\Models\Vehicle;
use App\Models\VehicleNote;
use App\Models\VehicleReminder;
use App\Models\VehicleService;
use App\Models\Workshop;
use App\Models\WorkshopService;
use App\Policies\VehicleNotePolicy;
use App\Policies\VehiclePolicy;
use App\Policies\VehicleReminderPolicy;
use App\Policies\VehicleServicePolicy;
use App\Policies\WorkshopPolicy;
use App\Policies\WorkshopServicePolicy;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        Vehicle::class => VehiclePolicy::class,
        VehicleService::class => VehicleServicePolicy::class,
        VehicleReminder::class => VehicleReminderPolicy::class,
        VehicleNote::class => VehicleNotePolicy::class,
        Workshop::class => WorkshopPolicy::class,
        WorkshopService::class => WorkshopServicePolicy::class,
    ];

    /**
     * Register any authentication / authorization services.
     *
     * @return void
     */
    public function boot()
    {
        $this->registerPolicies();

        //
    }
}
