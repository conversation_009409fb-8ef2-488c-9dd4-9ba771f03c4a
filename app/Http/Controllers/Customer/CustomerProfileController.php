<?php

namespace App\Http\Controllers\Customer;

use App\Enums\Gender;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customer\CustomerProfileUpdateRequest;
use App\Http\Resources\UserResource;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;

class CustomerProfileController extends Controller
{
    public function getProfile(Request $request): JsonResponse
    {
        $user = Auth::user();

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Customer profile retrieved successfully');
    }

    public function updateProfile(CustomerProfileUpdateRequest $request): JsonResponse
    {
        $user = Auth::user();
        $validatedData = $request->validated();

        $user->update($validatedData);

        if ($request->hasFile('avatar')) {
            $user->clearMediaCollection('avatar');

            $user->addMediaFromRequest('avatar')
                ->toMediaCollection('avatar');
        }

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Your profile has been updated successfully');
    }

    public function getGenderOptions(Request $request): JsonResponse
    {
        return ApiResponse::success([
            'genders' => Gender::options(),
            'locale' => App::getLocale(),
        ], 'Gender options retrieved successfully');
    }

    /**
     * Delete customer's own account
     */
    public function deleteAccount(Request $request): JsonResponse
    {
        try {
            $user = $request->user();

            // Revoke all tokens
            $user->tokens()->delete();

            // Soft delete the user account
            $user->delete();

            return ApiResponse::success([], 'Your account has been deleted successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to delete your account', $e);
        }
    }

    public function deleteAvatar(Request $request): JsonResponse
    {
        $user = Auth::user();
        $user->clearMediaCollection('avatar');

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Avatar deleted successfully');
    }

    public function uploadAvatar(Request $request): JsonResponse
    {
        $request->validate([
            'avatar' => 'required|image|max:5120', // 5MB max size
        ]);

        $user = Auth::user();

        // Remove old avatar if exists
        $user->clearMediaCollection('avatar');

        // Add new avatar
        $user->addMediaFromRequest('avatar')
            ->toMediaCollection('avatar');

        return ApiResponse::success([
            'profile' => new UserResource($user),
        ], 'Avatar uploaded successfully');
    }
}
