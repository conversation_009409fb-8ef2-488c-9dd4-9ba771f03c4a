<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Http\Requests\BaseApiRequest;

class VendorRegisterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'phone_number' => 'required|regex:/^\+[1-9]\d{1,14}$/|unique:users,phone_number,NULL,id,deleted_at,NULL',
            'email' => 'required|email|unique:users,email,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages(): array
    {
        if (app()->getLocale() === 'ar') {
            return [
                'first_name.required' => 'الاسم الأول مطلوب',
                'last_name.required' => 'اسم العائلة مطلوب',
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.unique' => 'رقم الهاتف مسجل بالفعل',
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح',
                'email.unique' => 'البريد الإلكتروني مسجل بالفعل',
            ];
        }

        return [
            'first_name.required' => 'First name is required',
            'last_name.required' => 'Last name is required',
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.unique' => 'Phone number is already registered',
            'email.required' => 'Email is required',
            'email.email' => 'Please enter a valid email address',
            'email.unique' => 'Email is already registered',
        ];
    }
}
