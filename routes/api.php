<?php

use App\Http\Controllers\Api\Customer\WorkshopController as CustomerWorkshopController;
use App\Http\Controllers\Api\FCMController;
use App\Http\Controllers\Api\Vendor\WorkingHourController;
use App\Http\Controllers\Api\Vendor\WorkshopController;
use App\Http\Controllers\Api\Vendor\WorkshopServiceController;
use App\Http\Controllers\Auth\AdminAuthController;
use App\Http\Controllers\Auth\CustomerAuthController;
use App\Http\Controllers\Auth\VendorAuthController;
use App\Http\Controllers\Customer\CustomerProfileController;
use App\Http\Controllers\Customer\NotificationController;
use App\Http\Controllers\Customer\SupportRequestController;
use App\Http\Controllers\EnumOptionsController;
use App\Http\Controllers\Vehicle\VehicleController;
use App\Http\Controllers\Vehicle\VehicleNoteController;
use App\Http\Controllers\Vehicle\VehicleReminderController;
use App\Http\Controllers\Vehicle\VehicleServiceController;
use App\Http\Controllers\Vendor\VendorProfileController;
use Illuminate\Support\Facades\Route;

// Routes for enum options (available without auth)
Route::prefix('options')->group(function () {
    Route::get('/support-request-statuses', [EnumOptionsController::class, 'getSupportRequestStatusOptions']);
    Route::get('/support-request-issue-types', [EnumOptionsController::class, 'getSupportRequestIssueTypeOptions']);
    Route::get('/support-request', [EnumOptionsController::class, 'getSupportRequestOptions']);
});

Route::prefix('auth')->group(function () {
    // Customer routes
    Route::prefix('customer')->group(function () {
        // Registration is open to all
        Route::post('/register', [CustomerAuthController::class, 'register']);

        // These routes need customer user type validation
        Route::middleware('customer')->group(function () {
            Route::post('/login', [CustomerAuthController::class, 'loginThroughOTP']);
            Route::post('/verify-otp', [CustomerAuthController::class, 'verifyOtp']);
            Route::post('/resend-otp', [CustomerAuthController::class, 'resendOtp']);
        });
    });

    // Vendor routes
    Route::prefix('vendor')->group(function () {
        // Registration is open to all
        Route::post('/register', [VendorAuthController::class, 'register']);

        // These routes need vendor user type validation
        Route::middleware('vendor')->group(function () {
            Route::post('/login-phone', [VendorAuthController::class, 'loginWithPhone']);
            Route::post('/login-email', [VendorAuthController::class, 'loginWithEmailOtp']);
            Route::post('/verify-phone-otp', [VendorAuthController::class, 'verifyPhoneOtp']);
            Route::post('/verify-email-otp', [VendorAuthController::class, 'verifyEmailOtp']);
            Route::post('/resend-phone-otp', [VendorAuthController::class, 'resendPhoneOtp']);
            Route::post('/resend-email-otp', [VendorAuthController::class, 'resendEmailOtp']);
        });
    });

    // Admin routes
    Route::prefix('admin')->group(function () {
        Route::post('/login', [AdminAuthController::class, 'login']);
    });
});

Route::middleware('auth:sanctum')->group(function () {
    // Logout routes based on user type
    Route::prefix('auth')->group(function () {
        Route::post('/customer/logout', [CustomerAuthController::class, 'logout']);
        Route::post('/vendor/logout', [VendorAuthController::class, 'logout']);
        Route::post('/admin/logout', [AdminAuthController::class, 'logout']);
    });

    // Admin self-management route
    Route::prefix('admin')->middleware('auth:admin')->group(function () {
        Route::delete('/account', [AdminAuthController::class, 'deleteAccount']);
    });

    // Customer Routes
    Route::prefix('customer')->group(function () {
        // Support Requests
        Route::prefix('support-requests')->group(function () {
            Route::get('/', [SupportRequestController::class, 'index']);
            Route::post('/', [SupportRequestController::class, 'store']);
            Route::get('/{id}', [SupportRequestController::class, 'show'])->where('id', '[0-9]+');
        });

        // Notifications
        Route::prefix('notifications')->group(function () {
            Route::get('/', [NotificationController::class, 'index']);
            Route::get('/{id}', [NotificationController::class, 'show'])->where('id', '[0-9]+');
            Route::post('/mark-all-read', [NotificationController::class, 'markAllAsRead']);
            Route::post('/toggle-read-status', [NotificationController::class, 'toggleReadStatus']);
            Route::get('/unread/count', [NotificationController::class, 'getUnreadCount']);
        });

        Route::prefix('profile')->group(function () {
            Route::get('/', [CustomerProfileController::class, 'getProfile']);
            Route::post('/', [CustomerProfileController::class, 'updateProfile']);
            Route::delete('/account', [CustomerProfileController::class, 'deleteAccount']);

            Route::post('/avatar', [CustomerProfileController::class, 'uploadAvatar']);
            Route::delete('/avatar', [CustomerProfileController::class, 'deleteAvatar']);
        });
    });

    // Vendor Profile routes
    Route::prefix('vendor/profile')->middleware('vendor')->group(function () {
        Route::get('/', [VendorProfileController::class, 'getProfile']);
        Route::post('/', [VendorProfileController::class, 'updateProfile']);
        Route::delete('/account', [VendorProfileController::class, 'deleteAccount']);

        Route::post('/avatar', [VendorProfileController::class, 'uploadAvatar']);
        Route::delete('/avatar', [VendorProfileController::class, 'deleteAvatar']);
    });

    // Vendor Workshops routes
    Route::prefix('vendor/workshops')->middleware('vendor')->group(function () {
        // Workshop main routes
        Route::get('/', [WorkshopController::class, 'index']);
        Route::post('/', [WorkshopController::class, 'store']);
        Route::get('/{workshop}', [WorkshopController::class, 'show'])->where('workshop', '[0-9]+');
        Route::post('/update/{workshop}', [WorkshopController::class, 'update'])->where('workshop', '[0-9]+');

        // Workshop image routes
        Route::post('/{workshop}/image', [WorkshopController::class, 'uploadImage'])->where('workshop', '[0-9]+');
        Route::delete('/{workshop}/image', [WorkshopController::class, 'deleteImage'])->where('workshop', '[0-9]+');

        // Working hours for a specific workshop
        Route::get('/{workshop}/working-hours', [WorkingHourController::class, 'index'])->where('workshop', '[0-9]+');
        Route::put('/{workshop}/working-hours', [WorkingHourController::class, 'update'])->where('workshop', '[0-9]+');

        // Services for a specific workshop
        Route::get('/{workshop}/services', [WorkshopServiceController::class, 'index'])->where('workshop', '[0-9]+');
        Route::post('/{workshop}/services', [WorkshopServiceController::class, 'update'])->where('workshop', '[0-9]+');
        // Route::delete('/{workshop}/services/{workshopService}', [WorkshopServiceController::class, 'destroy'])->where(['workshop' => '[0-9]+', 'workshopService' => '[0-9]+']);
    });

    // Vehicle routes
    Route::prefix('vehicles')->group(function () {
        Route::get('/', [VehicleController::class, 'index']);
        Route::post('/', [VehicleController::class, 'store']);
        Route::put('/{id}', [VehicleController::class, 'update'])->where('id', '[0-9]+');
        Route::delete('/{id}', [VehicleController::class, 'destroy'])->where('id', '[0-9]+');
        Route::get('/{id}', [VehicleController::class, 'show'])->where('id', '[0-9]+');

        // Vehicle Services routes
        Route::prefix('/{vehicleId}/services')->where(['vehicleId' => '[0-9]+'])->group(function () {
            Route::get('/', [VehicleServiceController::class, 'index'])->name('vehicle.services.index');
            Route::post('/', [VehicleServiceController::class, 'store'])->name('vehicle.services.store');
            Route::get('/{serviceId}', [VehicleServiceController::class, 'show'])->where('serviceId', '[0-9]+')->name('vehicle.services.show');
            Route::put('/{serviceId}', [VehicleServiceController::class, 'update'])->where('serviceId', '[0-9]+')->name('vehicle.services.update');
            Route::delete('/{serviceId}', [VehicleServiceController::class, 'destroy'])->where('serviceId', '[0-9]+')->name('vehicle.services.destroy');
        });

        // Vehicle Reminders routes
        Route::prefix('/{vehicleId}/reminders')->where(['vehicleId' => '[0-9]+'])->group(function () {
            Route::get('/', [VehicleReminderController::class, 'index'])->name('vehicle.reminders.index');
            Route::post('/', [VehicleReminderController::class, 'store'])->name('vehicle.reminders.store');
            Route::get('/{reminderId}', [VehicleReminderController::class, 'show'])->where('reminderId', '[0-9]+')->name('vehicle.reminders.show');
            Route::put('/{reminderId}', [VehicleReminderController::class, 'update'])->where('reminderId', '[0-9]+')->name('vehicle.reminders.update');
            Route::delete('/{reminderId}', [VehicleReminderController::class, 'destroy'])->where('reminderId', '[0-9]+')->name('vehicle.reminders.destroy');
        });

        // Vehicle Notes routes
        Route::prefix('/{vehicleId}/notes')->where(['vehicleId' => '[0-9]+'])->group(function () {
            Route::get('/', [VehicleNoteController::class, 'index'])->name('vehicle.notes.index');
            Route::post('/', [VehicleNoteController::class, 'store'])->name('vehicle.notes.store');
            Route::get('/{noteId}', [VehicleNoteController::class, 'show'])->where('noteId', '[0-9]+')->name('vehicle.notes.show');
            Route::put('/{noteId}', [VehicleNoteController::class, 'update'])->where('noteId', '[0-9]+')->name('vehicle.notes.update');
            Route::delete('/{noteId}', [VehicleNoteController::class, 'destroy'])->where('noteId', '[0-9]+')->name('vehicle.notes.destroy');
        });
    });

    // Customer-facing workshop routes (publicly accessible)
    Route::prefix('customer/workshops')->group(function () {
        Route::get('/', [CustomerWorkshopController::class, 'index']);
        Route::get('/{workshop}', [CustomerWorkshopController::class, 'show'])->where('workshop', '[0-9]+');
        Route::post('/{workshop}/count/{countType}', [CustomerWorkshopController::class, 'incrementCount'])
            ->where(['workshop' => '[0-9]+', 'countType' => '(show_count|location_count)']);
        Route::middleware('customer')->group(function () {
            Route::post('/bookmark', [CustomerWorkshopController::class, 'toggleBookmark']);
            Route::get('/bookmarks/list', [CustomerWorkshopController::class, 'bookmarks']);
        });
    });
});

require base_path('routes/admin.php');

// Public routes
Route::get('gender-options', [CustomerProfileController::class, 'getGenderOptions']);

// TODO: Temporarily disabled FCM routes
// Route::prefix('fcm')->group(function () {
//     // Immediate sending
//     Route::post('/send-notification', [FCMController::class, 'sendNotification']);
//     Route::post('/send-bulk-notification', [FCMController::class, 'sendBulkNotification']);
//     Route::post('/send-to-all-users', [FCMController::class, 'sendToAllUsers']);
//     Route::post('/send-to-user', [FCMController::class, 'sendToUser']);
//     Route::post('/send-to-users', [FCMController::class, 'sendToUsers']);

//     // Queue-based sending (recommended for production)
//     Route::post('/queue-notification', [FCMController::class, 'queueNotification']);
//     Route::post('/queue-bulk-notification', [FCMController::class, 'queueBulkNotification']);
//     Route::post('/queue-to-all-users', [FCMController::class, 'queueNotificationToAllUsers']);

//     // Token management
//     Route::post('/update-token', [FCMController::class, 'updateToken']);
//     Route::delete('/remove-token', [FCMController::class, 'removeToken']);
//     Route::get('/user-tokens', [FCMController::class, 'getUserTokens']);
//     Route::post('/cleanup-tokens', [FCMController::class, 'cleanupTokens']);

//     // Statistics and monitoring
//     Route::get('/stats', [FCMController::class, 'getStats']);

//     // Testing
//     Route::post('/test', [FCMController::class, 'testFCM']);
//     Route::get('/test-config', [FCMController::class, 'testFCMConfig']);
// });

// Offer Routes
Route::prefix('offers')->group(function () {
    Route::get('/', [\App\Http\Controllers\Api\OfferController::class, 'getActiveOffers']);
});
