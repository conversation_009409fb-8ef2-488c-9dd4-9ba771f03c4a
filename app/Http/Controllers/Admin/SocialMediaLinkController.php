<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\SocialMediaLink\StoreSocialMediaLinkRequest;
use App\Http\Requests\Admin\SocialMediaLink\UpdateSocialMediaLinkRequest;
use App\Http\Resources\SocialMediaLinkCollection;
use App\Http\Resources\SocialMediaLinkResource;
use App\Services\SocialMediaLinkService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class SocialMediaLinkController extends Controller
{
    protected SocialMediaLinkService $socialMediaLinkService;

    public function __construct(SocialMediaLinkService $socialMediaLinkService)
    {
        $this->socialMediaLinkService = $socialMediaLinkService;
    }

    public function index(Request $request): JsonResponse
    {
        $perPage = $request->input('per_page', 15);
        $filters = $request->only(['type', 'is_active', 'search']);

        $socialMediaLinks = $this->socialMediaLinkService->getPaginatedList($perPage, $filters);

        return ApiResponse::success(
            new SocialMediaLinkCollection($socialMediaLinks),
            'Social media links retrieved successfully'
        );
    }

    public function store(StoreSocialMediaLinkRequest $request): JsonResponse
    {
        $data = $request->validated();
        $socialMediaLink = $this->socialMediaLinkService->store($data);

        return ApiResponse::success(
            new SocialMediaLinkResource($socialMediaLink),
            'Social media link created successfully'
        );
    }

    public function show(int $id): JsonResponse
    {
        $socialMediaLink = $this->socialMediaLinkService->findById($id);

        if (! $socialMediaLink) {
            return ApiResponse::error('Social media link not found', Response::HTTP_NOT_FOUND);
        }

        return ApiResponse::success(
            new SocialMediaLinkResource($socialMediaLink),
            'Social media link retrieved successfully'
        );
    }

    public function update(UpdateSocialMediaLinkRequest $request, int $id): JsonResponse
    {
        $data = $request->validated();
        $socialMediaLink = $this->socialMediaLinkService->update($id, $data);

        if (! $socialMediaLink) {
            return ApiResponse::error('Social media link not found', Response::HTTP_NOT_FOUND);
        }

        return ApiResponse::success(
            new SocialMediaLinkResource($socialMediaLink),
            'Social media link updated successfully'
        );
    }

    public function destroy(int $id): JsonResponse
    {
        $result = $this->socialMediaLinkService->delete($id);

        if (! $result) {
            return ApiResponse::error('Social media link not found', Response::HTTP_NOT_FOUND);
        }

        return ApiResponse::success(
            null,
            'Social media link deleted successfully'
        );
    }

    public function getSocialMediaTypes(): JsonResponse
    {
        $types = $this->socialMediaLinkService->getSocialMediaTypeOptions();

        return ApiResponse::success(
            $types,
            'Social media types retrieved successfully'
        );
    }
}
