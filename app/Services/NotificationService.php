<?php

namespace App\Services;

use App\Enums\NotificationType;
use App\Enums\UserType;
use App\Models\Notification;
use App\Models\NotificationReadStatus;
use App\Models\Offer;
use App\Models\SupportRequest;
use App\Models\User;
use App\Models\VehicleReminder;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\DB;

class NotificationService
{
    public function createReminderNotification(VehicleReminder $reminder, User $user): Notification
    {
        $vehicle = $reminder->vehicle;
        $vehicleName = $vehicle->name ?? $vehicle->make_brand.' '.$vehicle->model_year;

        $titleData = [
            'en' => "Vehicle reminder: {$vehicleName}",
            'ar' => "تذكير المركبة: {$vehicleName}",
        ];

        $descriptionData = [
            'en' => "Your vehicle {$vehicleName} is due for maintenance on ".$reminder->reminder_date->format('Y-m-d H:i'),
            'ar' => "مركبتك {$vehicleName} مستحقة للصيانة في ".$reminder->reminder_date->format('Y-m-d H:i'),
        ];

        return Notification::create([
            'user_id' => $user->id,
            'title' => $titleData,
            'description' => $descriptionData,
            'notification_type' => NotificationType::REMINDER,
            'notifiable_type' => VehicleReminder::class,
            'notifiable_id' => $reminder->id,
        ]);
    }

    public function createOfferNotification(Offer $offer): Notification
    {
        $offerTitle = is_array($offer->title) ? ($offer->title['en'] ?? 'New Offer') : $offer->title;
        $offerDescription = is_array($offer->description) ? ($offer->description['en'] ?? 'Check out this offer') : $offer->description;

        $titleData = [
            'en' => "New offer: {$offerTitle}",
            'ar' => 'عرض جديد: '.(is_array($offer->title) ? ($offer->title['ar'] ?? $offerTitle) : $offerTitle),
        ];

        $descriptionData = [
            'en' => $offerDescription,
            'ar' => is_array($offer->description) ? ($offer->description['ar'] ?? $offerDescription) : $offerDescription,
        ];

        return Notification::create([
            'user_id' => null, // null for common notifications
            'title' => $titleData,
            'description' => $descriptionData,
            'notification_type' => NotificationType::OFFER,
            'notifiable_type' => Offer::class,
            'notifiable_id' => $offer->id,
        ]);
    }

    public function getUserNotification(int $notificationId, int $userId): ?Notification
    {
        return Notification::forUser($userId)
            ->with(['notifiable', 'user'])
            ->select('notifications.*')
            ->selectRaw('COALESCE(nrs.is_read, false) as computed_is_read')
            ->leftJoin('notification_read_status as nrs', function ($join) use ($userId) {
                $join->on('notifications.id', '=', 'nrs.notification_id')
                    ->where('nrs.user_id', '=', $userId);
            })
            ->where('notifications.id', $notificationId)
            ->first();
    }

    public function getUserNotifications(int $userId, array $filters = []): LengthAwarePaginator
    {
        $query = Notification::forUser($userId)
            ->with(['notifiable' => function ($query) {
                $query->withTrashed();
            }, 'user'])
            ->select('notifications.*')
            ->selectRaw('COALESCE(nrs.is_read, false) as computed_is_read')
            ->leftJoin('notification_read_status as nrs', function ($join) use ($userId) {
                $join->on('notifications.id', '=', 'nrs.notification_id')
                    ->where('nrs.user_id', '=', $userId);
            });

        // Apply filters
        if (isset($filters['is_read'])) {
            $query->havingRaw('computed_is_read = ?', [$filters['is_read']]);
        }

        if (isset($filters['type'])) {
            $query->byType($filters['type']);
        }

        $query->orderBy('created_at', 'desc');

        $perPage = $filters['per_page'] ?? 15;

        return $query->paginate($perPage);
    }

    public function markAllAsReadForUser(int $userId): int
    {
        $count = 0;

        // Mark all notifications as read by creating/updating read status records
        $notifications = Notification::forUser($userId)
            ->whereNotExists(function ($query) use ($userId) {
                $query->select(DB::raw(1))
                    ->from('notification_read_status')
                    ->whereRaw('notification_read_status.notification_id = notifications.id')
                    ->where('notification_read_status.user_id', $userId)
                    ->where('notification_read_status.is_read', true);
            })
            ->get();

        foreach ($notifications as $notification) {
            NotificationReadStatus::updateOrCreate(
                [
                    'notification_id' => $notification->id,
                    'user_id' => $userId,
                ],
                [
                    'is_read' => true,
                    'read_at' => now(),
                ]
            );
            $count++;
        }

        return $count;
    }

    public function toggleNotificationReadStatus(int $notificationId, int $userId): bool
    {
        $notification = Notification::findOrFail($notificationId);

        // Handle all notifications via NotificationReadStatus
        $readStatus = NotificationReadStatus::where('notification_id', $notificationId)
            ->where('user_id', $userId)
            ->first();

        if ($readStatus) {
            // Toggle existing read status
            $readStatus->is_read = ! $readStatus->is_read;
            $readStatus->read_at = $readStatus->is_read ? now() : null;

            return $readStatus->save();
        } else {
            // Create new read status record (mark as read)
            $readStatus = NotificationReadStatus::create([
                'notification_id' => $notificationId,
                'user_id' => $userId,
                'is_read' => true,
                'read_at' => now(),
            ]);

            return $readStatus !== null;
        }
    }

    public function getUnreadCountForUser(int $userId): int
    {
        // Count all notifications that are not marked as read by this user
        return Notification::forUser($userId)
            ->whereNotExists(function ($query) use ($userId) {
                $query->select(DB::raw(1))
                    ->from('notification_read_status')
                    ->whereRaw('notification_read_status.notification_id = notifications.id')
                    ->where('notification_read_status.user_id', $userId)
                    ->where('notification_read_status.is_read', true);
            })
            ->count();
    }

    public function deleteOldNotifications(int $daysOld = 30): int
    {
        return Notification::where('created_at', '<', now()->subDays($daysOld))->delete();
    }

    /**
     * Create vendor registration notification for admins
     */
    public function createVendorRegistrationNotification(User $vendor): void
    {
        $vendorName = $vendor->name ?? ($vendor->first_name.' '.$vendor->last_name);

        $titleData = [
            'en' => 'New Vendor Registration',
            'ar' => 'تسجيل مورد جديد',
        ];

        $descriptionData = [
            'en' => "A new vendor '{$vendorName}' has registered and is awaiting approval.",
            'ar' => "مورد جديد '{$vendorName}' قام بالتسجيل وينتظر الموافقة.",
        ];

        // Create notification for all admin users
        $adminUsers = User::where('user_type', UserType::ADMIN)->get();

        foreach ($adminUsers as $admin) {
            Notification::create([
                'user_id' => $admin->id,
                'title' => $titleData,
                'description' => $descriptionData,
                'notification_type' => NotificationType::VENDOR_REGISTRATION,
                'notifiable_type' => User::class,
                'notifiable_id' => $vendor->id,
            ]);
        }
    }

    /**
     * Create vendor approval request notification for admins
     */
    public function createVendorApprovalRequestNotification(User $vendor): void
    {
        $vendorName = $vendor->name ?? ($vendor->first_name.' '.$vendor->last_name);

        $titleData = [
            'en' => 'Vendor Approval Required',
            'ar' => 'مطلوب موافقة المورد',
        ];

        $descriptionData = [
            'en' => "Vendor '{$vendorName}' requires approval to access the platform.",
            'ar' => "المورد '{$vendorName}' يحتاج موافقة للوصول إلى المنصة.",
        ];

        // Create notification for admin users with vendor management permissions
        $adminUsers = User::where('user_type', UserType::ADMIN)
            ->whereHas('roles', function ($query) {
                $query->whereHas('permissions', function ($permQuery) {
                    $permQuery->where('name', 'like', '%vendor%');
                });
            })
            ->get();

        // If no specific vendor managers, notify all admins
        if ($adminUsers->isEmpty()) {
            $adminUsers = User::where('user_type', UserType::ADMIN)->get();
        }

        foreach ($adminUsers as $admin) {
            Notification::create([
                'user_id' => $admin->id,
                'title' => $titleData,
                'description' => $descriptionData,
                'notification_type' => NotificationType::VENDOR_APPROVAL_REQUEST,
                'notifiable_type' => User::class,
                'notifiable_id' => $vendor->id,
            ]);
        }
    }

    /**
     * Create system alert notification for admins
     */
    public function createSystemAlertNotification(string $alertTitle, string $alertMessage, ?string $alertTitleAr = null, ?string $alertMessageAr = null): void
    {
        $titleData = [
            'en' => $alertTitle,
            'ar' => $alertTitleAr ?? $alertTitle,
        ];

        $descriptionData = [
            'en' => $alertMessage,
            'ar' => $alertMessageAr ?? $alertMessage,
        ];

        // Create single notification for all admin users (user_id = null means for all admins)
        Notification::create([
            'user_id' => null,
            'title' => $titleData,
            'description' => $descriptionData,
            'notification_type' => NotificationType::SYSTEM_ALERT,
            'notifiable_type' => null,
            'notifiable_id' => null,
        ]);
    }

    /**
     * Create support request notification for admins
     */
    public function createSupportRequestNotification(SupportRequest $supportRequest): void
    {
        $user = $supportRequest->user;
        $userName = $user->name ?? ($user->first_name.' '.$user->last_name);
        $requestId = $supportRequest->request_id;

        $titleData = [
            'en' => 'New Support Request',
            'ar' => 'طلب دعم جديد',
        ];

        $descriptionData = [
            'en' => "New support request #{$requestId} from {$userName}: {$supportRequest->subject}",
            'ar' => "طلب دعم جديد #{$requestId} من {$userName}: {$supportRequest->subject}",
        ];

        // Create notification for admin users with customer support permissions
        $adminUsers = User::where('user_type', UserType::ADMIN)
            ->whereHas('roles', function ($query) {
                $query->whereHas('permissions', function ($permQuery) {
                    $permQuery->where('name', 'like', '%support%')
                        ->orWhere('name', 'like', '%customer%');
                });
            })
            ->get();

        // If no specific support managers, notify all admins
        if ($adminUsers->isEmpty()) {
            $adminUsers = User::where('user_type', UserType::ADMIN)->get();
        }

        foreach ($adminUsers as $admin) {
            Notification::create([
                'user_id' => $admin->id,
                'title' => $titleData,
                'description' => $descriptionData,
                'notification_type' => NotificationType::CUSTOMER_SUPPORT_REQUEST,
                'notifiable_type' => SupportRequest::class,
                'notifiable_id' => $supportRequest->id,
            ]);
        }
    }

    /**
     * Get admin notifications with filtering
     */
    public function getAdminNotifications(int $adminUserId, array $filters = []): LengthAwarePaginator
    {
        $query = Notification::where(function ($q) use ($adminUserId) {
            $q->where('notifications.user_id', $adminUserId)
                ->orWhereNull('notifications.user_id'); // Include notifications for all admins
        })
            ->with(['notifiable' => function ($query) {
                $query->withTrashed();
            }, 'user'])
            ->select('notifications.*')
            ->selectRaw('COALESCE(nrs.is_read, false) as computed_is_read')
            ->leftJoin('notification_read_status as nrs', function ($join) use ($adminUserId) {
                $join->on('notifications.id', '=', 'nrs.notification_id')
                    ->where('nrs.user_id', '=', $adminUserId);
            });

        // Filter by admin notification types
        $adminNotificationTypes = [
            NotificationType::VENDOR_REGISTRATION->value,
            NotificationType::VENDOR_APPROVAL_REQUEST->value,
            NotificationType::CUSTOMER_SUPPORT_REQUEST->value,
            NotificationType::SYSTEM_ALERT->value,
        ];

        $query->whereIn('notification_type', $adminNotificationTypes);

        // Apply filters
        if (isset($filters['is_read'])) {
            $query->havingRaw('computed_is_read = ?', [$filters['is_read']]);
        }

        if (isset($filters['type'])) {
            $query->where('notification_type', $filters['type']);
        }

        $query->orderBy('created_at', 'desc');

        $perPage = $filters['per_page'] ?? 15;

        return $query->paginate($perPage);
    }

    /**
     * Get unread count for admin notifications
     */
    public function getUnreadAdminNotificationsCount(int $adminUserId): int
    {
        $adminNotificationTypes = [
            NotificationType::VENDOR_REGISTRATION->value,
            NotificationType::VENDOR_APPROVAL_REQUEST->value,
            NotificationType::CUSTOMER_SUPPORT_REQUEST->value,
            NotificationType::SYSTEM_ALERT->value,
        ];

        return Notification::where(function ($q) use ($adminUserId) {
            $q->where('notifications.user_id', $adminUserId)
                ->orWhereNull('notifications.user_id'); // Include notifications for all admins
        })
            ->whereIn('notification_type', $adminNotificationTypes)
            ->whereNotExists(function ($query) use ($adminUserId) {
                $query->select(DB::raw(1))
                    ->from('notification_read_status')
                    ->whereRaw('notification_read_status.notification_id = notifications.id')
                    ->where('notification_read_status.user_id', $adminUserId)
                    ->where('notification_read_status.is_read', true);
            })
            ->count();
    }
}
