<?php

namespace App\Services\Vehicle;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Database\Eloquent\Model;

class VehicleService
{
    public function getAllVehicles(int $userId): Collection
    {
        return Vehicle::where('user_id', $userId)->get();
    }

    public function createVehicle(int $userId, array $data): Model
    {
        $vehicleData = array_merge(['user_id' => $userId], $data);

        return Vehicle::create($vehicleData);
    }

    public function getVehicleByIdAndUserId(int $vehicleId, int $userId): ?Vehicle
    {
        return Vehicle::where('id', $vehicleId)
            ->where('user_id', $userId)
            ->first();
    }

    public function updateVehicle(int $vehicleId, int $userId, array $data): ?Vehicle
    {
        $vehicle = $this->getVehicleByIdAndUserId($vehicleId, $userId);

        if ($vehicle) {
            $vehicle->update($data);
        }

        return $vehicle;
    }

    public function deleteVehicle(int $vehicleId, int $userId): bool
    {
        $vehicle = $this->getVehicleByIdAndUserId($vehicleId, $userId);

        if ($vehicle) {
            return $vehicle->delete();
        }

        return false;
    }

    public function getVehicleWithRelationships(int $vehicleId, int $userId): ?Vehicle
    {
        return Vehicle::where('id', $vehicleId)
            ->where('user_id', $userId)
            ->with(['services', 'reminders', 'notes'])
            ->first();
    }
}
