<?php

namespace App\Http\Middleware;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureVendorUser
{
    public function handle(Request $request, Closure $next): Response
    {
        // First check if user is authenticated
        if ($request->user()) {
            // If user is authenticated, verify it's a vendor
            if (! $request->user()->isVendor()) {
                return ApiResponse::error('This account is not registered as a vendor. Please log in with a vendor account or contact support.', 403);
            }

            // If user is a vendor, allow access
            return $next($request);
        }

        // For non-authenticated routes, check phone_number or email
        if ($request->has('phone_number')) {
            $user = User::where('phone_number', $request->phone_number)->first();

            if ($user && $user->user_type !== UserType::VENDOR) {
                return ApiResponse::error('This account is not registered as a vendor. Please log in with a vendor account or contact support.', 403);
            }
        } elseif ($request->has('email')) {
            $user = User::where('email', $request->email)->first();

            if ($user && $user->user_type !== UserType::VENDOR) {
                return ApiResponse::error('This account is not registered as a vendor. Please log in with a vendor account or contact support.', 403);
            }
        }

        return $next($request);
    }
}
