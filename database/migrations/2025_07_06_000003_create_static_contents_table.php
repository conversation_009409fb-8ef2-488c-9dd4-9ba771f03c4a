<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('static_contents', function (Blueprint $table) {
            $table->id();
            $table->string('type'); // terms_conditions, privacy_policy, about_us, etc.
            $table->enum('user_type', ['customer', 'vendor', 'both'])->default('both');
            $table->boolean('is_active')->default(true);
            $table->timestamps();

            $table->unique(['type', 'user_type'], 'sc_type_user_unique');
        });

        Schema::create('static_content_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('static_content_id')->constrained()->cascadeOnDelete();
            $table->string('locale', 5);
            $table->string('title');
            $table->longText('content'); // Will store HTML content
            $table->timestamps();

            $table->unique(['static_content_id', 'locale'], 'sc_trans_content_locale_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('static_content_translations');
        Schema::dropIfExists('static_contents');
    }
};
