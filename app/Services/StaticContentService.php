<?php

namespace App\Services;

use App\Models\StaticContent;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class StaticContentService
{
    /**
     * Get static content by type and user type.
     *
     * @param  string  $type  terms_conditions, privacy_policy, about_us, etc.
     * @param  string  $userType  customer, vendor, or both
     */
    public function getContentByTypeAndUserType(string $type, string $userType, string $locale = 'en'): ?StaticContent
    {
        return StaticContent::with(['translations'])
            ->where('type', $type)
            ->where('user_type', $userType)
            ->where('is_active', true)
            ->first();
    }

    /**
     * Get all static contents for admin.
     */
    public function getAllContentsForAdmin(): Collection
    {
        return StaticContent::with('translations')
            ->orderBy('type')
            ->orderBy('user_type')
            ->get();
    }

    /**
     * Get static content by ID.
     */
    public function getContentById(int $id): ?StaticContent
    {
        return StaticContent::with('translations')->find($id);
    }

    /**
     * Store or update static content.
     */
    public function storeOrUpdate(array $data): StaticContent
    {
        return DB::transaction(function () use ($data) {
            // Find or create static content
            $content = StaticContent::firstOrNew([
                'type' => $data['type'],
                'user_type' => $data['user_type'],
            ]);

            $content->is_active = $data['is_active'] ?? true;
            $content->save();

            // Create or update translations
            foreach ($data['translations'] as $locale => $translation) {
                $content->translations()->updateOrCreate(
                    ['locale' => $locale],
                    [
                        'title' => $translation['title'],
                        'content' => $translation['content'],
                    ]
                );
            }

            return $content->load('translations');
        });
    }

    /**
     * Delete a static content.
     */
    public function delete(StaticContent $content): bool
    {
        return DB::transaction(function () use ($content) {
            // Delete translations (will cascade due to foreign key constraint)
            return $content->delete();
        });
    }
}
