<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\StaticContent\StoreStaticContentRequest;
use App\Http\Resources\StaticContent\StaticContentResource;
use App\Models\StaticContent;
use App\Services\StaticContentService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class StaticContentController extends Controller
{
    protected $staticContentService;

    public function __construct(StaticContentService $staticContentService)
    {
        $this->staticContentService = $staticContentService;
    }

    public function index(): JsonResponse
    {
        try {
            $contents = $this->staticContentService->getAllContentsForAdmin();

            return ApiResponse::success([
                'contents' => StaticContentResource::collection($contents),
            ], 'Static contents retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve static contents', $e);
        }
    }

    public function store(StoreStaticContentRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $content = $this->staticContentService->storeOrUpdate($request->validated());

            DB::commit();

            return ApiResponse::success(
                new StaticContentResource($content),
                'Static content created/updated successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create/update static content', $e);
        }
    }

    public function show(Request $request, $id): JsonResponse
    {
        try {
            return ApiResponse::success([
                'content' => new StaticContentResource(StaticContent::findOrFail($id)->load('translations')),
            ], 'Static content retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve static content', $e);
        }
    }

    public function destroy(Request $request, $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $staticContent = StaticContent::findOrFail($id);
            $this->staticContentService->delete($staticContent);

            DB::commit();

            return ApiResponse::success([
                'content' => new StaticContentResource($staticContent),
            ], 'Static content deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete static content', $e);
        }
    }

    public function getContentByType(string $type, string $userType): JsonResponse
    {
        try {
            // Validate parameters
            if (! in_array($type, ['terms', 'privacy', 'about'])) {
                return ApiResponse::error('Invalid content type', 400);
            }

            if (! in_array($userType, ['customer', 'vendor', 'both'])) {
                return ApiResponse::error('Invalid user type', 400);
            }

            // Get content from service
            $content = $this->staticContentService->getContentByTypeAndUserType($type, $userType);

            if (! $content) {
                return ApiResponse::error('Content not found', 404);
            }

            return ApiResponse::success([
                'content' => new StaticContentResource($content),
            ], 'Content retrieved successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve static content', $e);
        }
    }
}
