<?php

namespace App\Http\Requests\StaticContent;

use Illuminate\Contracts\Validation\Validator;
use Illuminate\Foundation\Http\FormRequest;

class StoreStaticContentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => 'required|string',
            'user_type' => 'required|string|in:customer,vendor,both',
            'is_active' => 'nullable|boolean',
            'translations' => 'required|array',
            'translations.en' => 'required|array',
            'translations.*.title' => 'required|string|max:255',
            'translations.*.content' => 'required|string', // Can contain HTML content
        ];
    }

    public function withValidator(Validator $validator): void
    {
        $validator->after(function (Validator $validator) {
            $type = $this->input('type');
            $userType = $this->input('user_type');

            // Validate parameters
            if (! in_array($type, ['terms', 'privacy', 'about'])) {
                $validator->errors()->add('type', 'Invalid content type. Must be one of: terms, privacy, about');
            }

            if (! in_array($userType, ['customer', 'vendor', 'both'])) {
                $validator->errors()->add('user_type', 'Invalid user type. Must be one of: customer, vendor, both');
            }
        });
    }
}
