<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Workshop;
use Illuminate\Auth\Access\HandlesAuthorization;

class WorkshopPolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can view the workshop.
     */
    public function view(User $user, Workshop $workshop): bool
    {
        // Vendors can view their own workshops
        if ($user->isVendor() && $workshop->user_id === $user->id) {
            return true;
        }

        // Any user can view an approved workshop
        return $workshop->is_approved;
    }

    /**
     * Determine whether the user can update the workshop.
     */
    public function update(User $user, Workshop $workshop): bool
    {
        // Only the owner vendor can update their workshop
        return $user->isVendor() && $workshop->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the workshop.
     */
    public function delete(User $user, Workshop $workshop): bool
    {
        // Only the owner vendor can delete their workshop
        return $user->isVendor() && $workshop->user_id === $user->id;
    }
}
