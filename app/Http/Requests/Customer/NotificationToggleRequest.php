<?php

namespace App\Http\Requests\Customer;

use App\Http\Requests\BaseApiRequest;

class NotificationToggleRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'notification_id' => 'required|exists:notifications,id',
        ];
    }

    public function messages(): array
    {
        return [
            'notification_id.required' => 'Notification ID is required.',
            'notification_id.exists' => 'The selected notification does not exist.',
        ];
    }
}
