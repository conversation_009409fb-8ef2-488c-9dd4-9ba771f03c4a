<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Jobs\SendFCMNotificationJob;
use App\Models\User;
use App\Models\UserFcmToken;
use App\Services\FcmTokenService;
use App\Services\FirebaseService;
use Illuminate\Http\Request;

class FCMController extends Controller
{
    private $firebaseService;

    private $fcmTokenService;

    public function __construct(FirebaseService $firebaseService, FcmTokenService $fcmTokenService)
    {
        $this->firebaseService = $firebaseService;
        $this->fcmTokenService = $fcmTokenService;
    }

    /**
     * Send push notification to a specific device
     */
    public function sendNotification(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $result = $this->firebaseService->sendToDevice(
            $request->token,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'Notification sent successfully',
                'response' => $result['response'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => 'Failed to send notification',
                'error' => $result['error'] ?? $result['response'],
            ], 400);
        }
    }

    /**
     * Send notification to multiple devices
     */
    public function sendBulkNotification(Request $request)
    {
        $request->validate([
            'tokens' => 'required|array',
            'tokens.*' => 'string',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $results = $this->firebaseService->sendToMultipleDevices(
            $request->tokens,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => true,
            'message' => 'Bulk notification process completed',
            'results' => $results,
        ]);
    }

    /**
     * Send notification to all users
     */
    public function sendToAllUsers(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $tokens = $this->fcmTokenService->getAllActiveTokens();

        if (empty($tokens)) {
            return response()->json([
                'success' => false,
                'message' => 'No FCM tokens found',
            ], 404);
        }

        $request->merge(['tokens' => $tokens]);

        return $this->sendBulkNotification($request);
    }

    /**
     * Add/Update user's FCM token (supports multiple devices)
     */
    public function updateToken(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'fcm_token' => 'required|string',
            'device_type' => 'nullable|string|in:android,ios,web',
            'device_name' => 'nullable|string|max:255',
            'app_version' => 'nullable|string|max:50',
        ]);

        $token = $this->fcmTokenService->addOrUpdateToken(
            $request->user_id,
            $request->fcm_token,
            $request->device_type,
            $request->device_name,
            $request->app_version
        );

        return response()->json([
            'success' => true,
            'message' => 'FCM token updated successfully',
            'token_id' => $token->id,
            'total_devices' => count($this->fcmTokenService->getUserActiveTokens($request->user_id)),
        ]);
    }

    /**
     * Test FCM configuration
     */
    public function testFCM(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
        ]);

        $result = $this->firebaseService->sendToDevice(
            $request->token,
            'Test Notification',
            'This is a test notification from your Laravel backend!',
            ['test' => 'true', 'timestamp' => now()->toISOString()]
        );

        return response()->json([
            'success' => $result['success'],
            'message' => $result['success'] ? 'Test notification sent successfully!' : 'Failed to send test notification',
            'result' => $result,
        ]);
    }

    /**
     * Test FCM configuration without requiring a real device token
     */
    public function testFCMConfig()
    {
        try {
            // Get service account credentials from config array
            $serviceAccount = config('firebase.credentials');

            if (! $serviceAccount) {
                return response()->json([
                    'success' => false,
                    'message' => 'Firebase service account credentials not found in config',
                    'debug_info' => [
                        'config_exists' => config('firebase.credentials') !== null,
                        'project_id' => config('firebase.project_id'),
                    ],
                ]);
            }

            // Check required fields
            $requiredFields = ['client_email', 'private_key', 'project_id'];
            $missingFields = [];

            foreach ($requiredFields as $field) {
                if (! isset($serviceAccount[$field]) || empty($serviceAccount[$field])) {
                    $missingFields[] = $field;
                }
            }

            if (! empty($missingFields)) {
                return response()->json([
                    'success' => false,
                    'message' => 'Missing required fields in service account',
                    'missing_fields' => $missingFields,
                ]);
            }

            // Try to generate access token (this tests OAuth flow)
            $reflection = new \ReflectionClass($this->firebaseService);
            $method = $reflection->getMethod('getAccessToken');
            $method->setAccessible(true);
            $accessToken = $method->invoke($this->firebaseService);

            return response()->json([
                'success' => true,
                'message' => 'FCM configuration is valid!',
                'config' => [
                    'project_id' => $serviceAccount['project_id'],
                    'client_email' => $serviceAccount['client_email'],
                    'private_key_id' => $serviceAccount['private_key_id'],
                    'fcm_url' => "https://fcm.googleapis.com/v1/projects/{$serviceAccount['project_id']}/messages:send",
                    'access_token_generated' => ! empty($accessToken),
                    'http_v1_api' => true,
                    'config_source' => 'direct_array',
                    'total_active_tokens' => UserFcmToken::active()->count(),
                ],
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'FCM configuration test failed',
                'error' => $e->getMessage(),
                'debug_info' => [
                    'config_exists' => config('firebase.credentials') !== null,
                    'project_id' => config('firebase.project_id'),
                    'credentials_type' => config('firebase.credentials.type'),
                    'client_email' => config('firebase.credentials.client_email'),
                    'has_private_key' => ! empty(config('firebase.credentials.private_key')),
                ],
            ], 500);
        }
    }

    /**
     * Queue a notification to be sent asynchronously
     */
    public function queueNotification(Request $request)
    {
        $request->validate([
            'token' => 'required|string',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
            'delay' => 'nullable|integer|min:0', // delay in seconds
        ]);

        $job = new SendFCMNotificationJob(
            $request->token,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        if ($request->has('delay') && $request->delay > 0) {
            $job->delay(now()->addSeconds($request->delay));
        }

        dispatch($job);

        return response()->json([
            'success' => true,
            'message' => 'Notification queued successfully',
        ]);
    }

    /**
     * Queue bulk notifications to be sent asynchronously
     */
    public function queueBulkNotification(Request $request)
    {
        $request->validate([
            'tokens' => 'required|array',
            'tokens.*' => 'string',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
            'delay' => 'nullable|integer|min:0',
        ]);

        $job = new SendFCMNotificationJob(
            $request->tokens,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        if ($request->has('delay') && $request->delay > 0) {
            $job->delay(now()->addSeconds($request->delay));
        }

        dispatch($job);

        return response()->json([
            'success' => true,
            'message' => 'Bulk notifications queued successfully',
            'total_tokens' => count($request->tokens),
        ]);
    }

    /**
     * Send notification to all users with FCM tokens (queued)
     */
    public function queueNotificationToAllUsers(Request $request)
    {
        $request->validate([
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
            'delay' => 'nullable|integer|min:0',
        ]);

        $tokens = $this->fcmTokenService->getAllActiveTokens();

        if (empty($tokens)) {
            return response()->json([
                'success' => false,
                'message' => 'No users with FCM tokens found',
            ], 404);
        }

        $job = new SendFCMNotificationJob(
            $tokens,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        if ($request->has('delay') && $request->delay > 0) {
            $job->delay(now()->addSeconds($request->delay));
        }

        dispatch($job);

        return response()->json([
            'success' => true,
            'message' => 'Notifications queued for all users',
            'total_users' => count($tokens),
        ]);
    }

    /**
     * Remove FCM token for a user
     */
    public function removeToken(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'fcm_token' => 'required|string',
        ]);

        $removed = $this->fcmTokenService->removeToken($request->user_id, $request->fcm_token);

        return response()->json([
            'success' => $removed,
            'message' => $removed ? 'FCM token removed successfully' : 'FCM token not found',
        ]);
    }

    /**
     * Get all tokens for a user
     */
    public function getUserTokens(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
        ]);

        $tokens = UserFcmToken::where('user_id', $request->user_id)
            ->active()
            ->select('id', 'fcm_token', 'device_type', 'device_name', 'app_version', 'last_used_at', 'created_at')
            ->get();

        return response()->json([
            'success' => true,
            'user_id' => $request->user_id,
            'total_devices' => $tokens->count(),
            'tokens' => $tokens,
        ]);
    }

    /**
     * Send notification to specific user (all their devices)
     */
    public function sendToUser(Request $request)
    {
        $request->validate([
            'user_id' => 'required|exists:users,id',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $tokens = $this->fcmTokenService->getUserActiveTokens($request->user_id);

        if (empty($tokens)) {
            return response()->json([
                'success' => false,
                'message' => 'No active FCM tokens found for this user',
            ], 404);
        }

        $results = $this->firebaseService->sendToMultipleDevices(
            $tokens,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => true,
            'message' => 'Notifications sent to user devices',
            'user_id' => $request->user_id,
            'devices_count' => count($tokens),
            'results' => $results,
        ]);
    }

    /**
     * Send notification to multiple users (all their devices)
     */
    public function sendToUsers(Request $request)
    {
        $request->validate([
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
            'title' => 'required|string',
            'body' => 'required|string',
            'data' => 'nullable|array',
        ]);

        $tokens = $this->fcmTokenService->getMultipleUsersActiveTokens($request->user_ids);

        if (empty($tokens)) {
            return response()->json([
                'success' => false,
                'message' => 'No active FCM tokens found for the specified users',
            ], 404);
        }

        $results = $this->firebaseService->sendToMultipleDevices(
            $tokens,
            $request->title,
            $request->body,
            $request->data ?? []
        );

        return response()->json([
            'success' => true,
            'message' => 'Notifications sent to users',
            'target_users' => $request->user_ids,
            'total_devices' => count($tokens),
            'results' => $results,
        ]);
    }

    /**
     * Get FCM system statistics
     */
    public function getStats()
    {
        $totalTokens = UserFcmToken::active()->count();
        $totalUsers = UserFcmToken::active()->distinct('user_id')->count('user_id');
        $tokensPerUser = $this->fcmTokenService->getTokensCountPerUser();

        $deviceTypes = UserFcmToken::active()
            ->selectRaw('device_type, COUNT(*) as count')
            ->whereNotNull('device_type')
            ->groupBy('device_type')
            ->pluck('count', 'device_type')
            ->toArray();

        return response()->json([
            'success' => true,
            'stats' => [
                'total_active_tokens' => $totalTokens,
                'total_users_with_tokens' => $totalUsers,
                'average_devices_per_user' => $totalUsers > 0 ? round($totalTokens / $totalUsers, 2) : 0,
                'device_types' => $deviceTypes,
                'top_users_by_devices' => collect($tokensPerUser)->sortDesc()->take(5)->toArray(),
            ],
        ]);
    }

    /**
     * Cleanup old/inactive tokens
     */
    public function cleanupTokens(Request $request)
    {
        $request->validate([
            'days_old' => 'nullable|integer|min:1|max:365',
        ]);

        $daysOld = $request->days_old ?? 30;
        $deletedCount = $this->fcmTokenService->cleanupOldTokens($daysOld);

        return response()->json([
            'success' => true,
            'message' => "Cleaned up {$deletedCount} old FCM tokens",
            'deleted_count' => $deletedCount,
            'cutoff_days' => $daysOld,
        ]);
    }
}
