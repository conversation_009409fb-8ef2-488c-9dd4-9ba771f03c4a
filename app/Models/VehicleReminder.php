<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleReminder extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // VehicleReminder model is a standalone entity
        });
    }

    protected $fillable = [
        'vehicle_id',
        'reminder_type',
        'is_mileage_based',
        'reminder_date',
        'mileage',
        'review',
    ];

    protected $casts = [
        'reminder_date' => 'date',
        'is_mileage_based' => 'boolean',
        'mileage' => 'integer',
        'review' => 'json',
    ];

    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    public function notifications(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(Notification::class, 'notifiable');
    }
}
