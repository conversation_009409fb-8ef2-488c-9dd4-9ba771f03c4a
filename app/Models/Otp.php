<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class Otp extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // Otp model is a standalone entity
        });
    }

    protected $fillable = [
        'phone_number',
        'email',
        'otp',
        'expires_at',
        'resend_count',
        'next_resend_at',
    ];

    protected $casts = [
        'expires_at' => 'datetime',
        'next_resend_at' => 'datetime',
    ];
}
