<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\VendorResource;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VendorApprovalController extends Controller
{
    /**
     * Get list of unapproved vendors
     */
    public function getUnapprovedVendors(): JsonResponse
    {
        $vendors = User::where('user_type', UserType::VENDOR)
            ->where('is_vendor_account_approved', false)
            ->get();

        return ApiResponse::success([
            'vendors' => VendorResource::collection($vendors),
        ], 'Unapproved vendors retrieved successfully');
    }

    /**
     * Get list of approved vendors
     */
    public function getApprovedVendors(): JsonResponse
    {
        $vendors = User::where('user_type', UserType::VENDOR)
            ->where('is_vendor_account_approved', true)
            ->get();

        return ApiResponse::success([
            'vendors' => VendorResource::collection($vendors),
        ], 'Approved vendors retrieved successfully');
    }

    /**
     * Approve a vendor
     */
    public function approveVendor(Request $request, User $vendor): JsonResponse
    {
        // Validate that the user is a vendor
        if ($vendor->user_type !== UserType::VENDOR) {
            return ApiResponse::error('User is not a vendor', 400);
        }

        $vendor->update([
            'is_vendor_account_approved' => true,
        ]);

        return ApiResponse::success([
            'vendor' => new VendorResource($vendor),
        ], 'Vendor approved successfully');
    }

    /**
     * Disapprove a vendor
     */
    public function disapproveVendor(Request $request, User $vendor): JsonResponse
    {
        // Validate that the user is a vendor
        if ($vendor->user_type !== UserType::VENDOR) {
            return ApiResponse::error('User is not a vendor', 400);
        }

        $vendor->update([
            'is_vendor_account_approved' => false,
        ]);

        return ApiResponse::success([
            'vendor' => new VendorResource($vendor),
        ], 'Vendor disapproved successfully');
    }
}
