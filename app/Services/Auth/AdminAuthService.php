<?php

namespace App\Services\Auth;

use App\Enums\UserType;
use App\Http\Resources\UserResource;
use App\Models\User;
use Exception;
use Illuminate\Support\Facades\Hash;

class AdminAuthService
{
    public function createAdmin(string $name, string $email, string $password): User
    {
        // First check if there's a soft deleted admin with this email
        $existingAdmin = User::withTrashed()
            ->where('email', $email)
            ->where('user_type', UserType::ADMIN->value)
            ->first();

        if ($existingAdmin) {
            if ($existingAdmin->trashed()) {
                // Restore the admin
                $existingAdmin->restore();
                $existingAdmin->update([
                    'name' => $name,
                    'password' => Hash::make($password),
                    'is_verified' => true,
                ]);

                return $existingAdmin;
            }
        }

        // Create new admin if no existing admin (including soft deleted) found
        return User::create([
            'name' => $name,
            'email' => $email,
            'password' => Hash::make($password),
            'is_verified' => true, // Admins are automatically verified
            'user_type' => UserType::ADMIN,
        ]);
    }

    public function login(string $email, string $password): array
    {
        // Check if there's a soft deleted admin with this email and restore if found
        $this->restoreUserIfDeleted(['email' => $email, 'user_type' => UserType::ADMIN->value]);

        $admin = User::where('email', $email)
            ->where('user_type', UserType::ADMIN->value)
            ->first();

        if (! $admin || ! Hash::check($password, $admin->password)) {
            throw new Exception('Invalid credentials');
        }

        return [
            'user' => new UserResource($admin),
            'token' => $this->createToken($admin),
        ];
    }

    private function createToken(User $user): string
    {
        $user->tokens()->delete();

        return $user->createToken('admin_token')->plainTextToken;
    }

    private function restoreUserIfDeleted(array $criteria): ?User
    {
        // Find soft-deleted user by criteria
        $deletedUser = User::onlyTrashed()
            ->where($criteria)
            ->first();

        if ($deletedUser) {
            // Restore the user if found
            $deletedUser->restore();

            return $deletedUser;
        }

        return null;
    }

    public function logout(User $user): bool
    {
        $user->tokens()->delete();

        return true;
    }
}
