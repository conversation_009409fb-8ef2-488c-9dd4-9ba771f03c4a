<?php

namespace App\Http\Controllers;

use App\Enums\SupportRequestIssueType;
use App\Enums\SupportRequestStatus;
use App\Helpers\ApiResponse;
use Illuminate\Http\JsonResponse;

class EnumOptionsController extends Controller
{
    /**
     * Get all support request status options.
     */
    public function getSupportRequestStatusOptions(): JsonResponse
    {
        return ApiResponse::success(
            SupportRequestStatus::options(),
            'Support request status options retrieved successfully'
        );
    }

    /**
     * Get all support request issue type options.
     */
    public function getSupportRequestIssueTypeOptions(): JsonResponse
    {
        return ApiResponse::success(
            SupportRequestIssueType::options(),
            'Support request issue type options retrieved successfully'
        );
    }

    /**
     * Get all enum options for support requests (status and issue types).
     */
    public function getSupportRequestOptions(): JsonResponse
    {
        return ApiResponse::success([
            'statuses' => SupportRequestStatus::options(),
            'issue_types' => SupportRequestIssueType::options(),
        ], 'Support request options retrieved successfully');
    }
}
