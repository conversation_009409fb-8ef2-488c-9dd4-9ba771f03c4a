<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleService;
use Illuminate\Auth\Access\HandlesAuthorization;

class VehicleServicePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function view(User $user, VehicleService $service)
    {
        return $user->id === $service->vehicle->user_id;
    }

    public function create(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function update(User $user, VehicleService $service)
    {
        return $user->id === $service->vehicle->user_id;
    }

    public function delete(User $user, VehicleService $service)
    {
        return $user->id === $service->vehicle->user_id;
    }
}
