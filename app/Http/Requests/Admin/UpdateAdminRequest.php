<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class UpdateAdminRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('admin.update');
    }

    public function rules(): array
    {
        $adminId = $this->route('id');

        return [
            'first_name' => 'sometimes|string|max:255',
            'last_name' => 'sometimes|string|max:255',
            'email' => 'sometimes|email|unique:users,email,'.$adminId,
            'password' => 'nullable|string|min:8',
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => 'admin email',
        ];
    }
}
