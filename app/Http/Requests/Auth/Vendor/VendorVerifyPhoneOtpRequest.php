<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Http\Requests\BaseApiRequest;
use App\Models\Otp;

class VendorVerifyPhoneOtpRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone_number' => 'required|string|regex:/^\+[1-9]\d{1,14}$/|exists:otps,phone_number',
            'otp' => [
                'required',
                'string',
                'size:4',
                function ($attribute, $value, $fail) {
                    if (! Otp::where('phone_number', $this->phone_number)
                        ->where('otp', $value)
                        ->where('expires_at', '>', now())
                        ->exists()) {
                        $fail('OTP has expired or does not exist');
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.exists' => 'لم يتم إرسال رمز OTP إلى هذا الرقم',
                'otp.required' => 'رمز التحقق مطلوب',
                'otp.size' => 'يجب أن يتكون رمز التحقق من 4 أرقام',
                'otp.numeric' => 'يجب أن يكون رمز التحقق أرقامًا فقط',
            ];
        }

        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.exists' => 'OTP has not been sent to this phone number',
            'otp.required' => 'OTP is required',
            'otp.size' => 'OTP must be 4 digits',
            'otp.numeric' => 'OTP must be numeric',
        ];
    }
}
