# HTTP server - handle ACME challenges and redirect to HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name ${NGINX_HOST};
    
    # Allow Let's Encrypt ACME challenge
    location /.well-known/acme-challenge/ {
        root /var/www/certbot;
    }
    
    # Health check endpoint for load balancers
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
    
    # Redirect all other HTTP requests to HTTPS
    location / {
        return 301 https://$host$request_uri;
    }
}

# HTTPS server
server {
    listen 443 ssl;
    listen [::]:443 ssl;
    http2 on;
    server_name ${NGINX_HOST};

    # Let's Encrypt SSL certificates
    ssl_certificate /etc/letsencrypt/live/${NGINX_HOST}/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/${NGINX_HOST}/privkey.pem;
    
    # Include Let's Encrypt recommended SSL configuration
    include /etc/letsencrypt/options-ssl-nginx.conf;
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem;

    # HSTS (optional - uncomment if you want strict HTTPS)
    # add_header Strict-Transport-Security "max-age=63072000; includeSubDomains; preload";
    
    # Root directory and index files
    root /var/www/public;
    index index.php;

    # Logs
    access_log /var/log/nginx/access.log;
    error_log /var/log/nginx/error.log;

    # Compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/xml application/json application/javascript application/rss+xml application/atom+xml image/svg+xml;

    # Handle PHP files
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        fastcgi_buffering on;
        fastcgi_buffer_size 16k;
        fastcgi_buffers 16 16k;
        fastcgi_read_timeout 300;
    }

    # Handle Laravel front controller
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        gzip_static on;
    }

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|webp)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # Deny access to hidden files
    location ~ /\.(?!well-known) {
        deny all;
    }
}
