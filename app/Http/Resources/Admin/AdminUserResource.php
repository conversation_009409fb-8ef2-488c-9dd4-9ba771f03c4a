<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Resources\Json\JsonResource;

class AdminUserResource extends JsonResource
{
    public function toArray($request): array
    {

        $directPermissions = $this->getDirectPermissions();

        $roles = $this->roles->map(function ($role) {
            return [
                'id' => $role->id,
                'name' => $role->name,
                'permissions' => PermissionResource::collection($role->permissions),
            ];
        });

        $allPermissions = $this->getAllPermissions();

        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'email' => $this->email,
            'roles' => $roles,
            'direct_permissions' => PermissionResource::collection($directPermissions),
            'all_permissions' => PermissionResource::collection($allPermissions),
        ];
    }
}
