<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\DashboardStatisticsResource;
use App\Http\Resources\Admin\MaintenanceLogCollection;
use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleReminder;
use App\Models\VehicleService;
use App\Models\Workshop;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class DashboardController extends Controller
{
    private function calculatePercentChange(int $current, int $previous): ?float
    {
        if ($previous === 0) {
            return $current > 0 ? 100 : 0;
        }

        return round((($current - $previous) / $previous) * 100, 2);
    }

    public function getStatistics()
    {
        // Current date and one week ago date
        $now = Carbon::now();
        $oneWeekAgo = Carbon::now()->subWeek();
        $twoWeeksAgo = Carbon::now()->subWeeks(2);

        // Count total customers and calculate percentage change
        $totalCustomers = User::where('user_type', UserType::CUSTOMER->value)->count();
        $lastWeekCustomers = User::where('user_type', UserType::CUSTOMER->value)
            ->whereDate('created_at', '<', $oneWeekAgo)
            ->count();
        $customersPercentChange = $this->calculatePercentChange($totalCustomers, $lastWeekCustomers);

        // Count total vendors and calculate percentage change
        $totalVendors = User::where('user_type', UserType::VENDOR->value)->count();
        $lastWeekVendors = User::where('user_type', UserType::VENDOR->value)
            ->whereDate('created_at', '<', $oneWeekAgo)
            ->count();
        $vendorsPercentChange = $this->calculatePercentChange($totalVendors, $lastWeekVendors);

        // Count total workshops and calculate percentage change
        $totalWorkshops = Workshop::count();
        $lastWeekWorkshops = Workshop::whereDate('created_at', '<', $oneWeekAgo)->count();
        $workshopsPercentChange = $this->calculatePercentChange($totalWorkshops, $lastWeekWorkshops);

        // Count active workshops and calculate percentage change
        $activeWorkshops = Workshop::where('is_active', true)
            ->where('is_approved', true)
            ->count();
        $lastWeekActiveWorkshops = Workshop::whereDate('created_at', '<', $oneWeekAgo)
            ->where('is_active', true)
            ->where('is_approved', true)
            ->count();
        $activeWorkshopsPercentChange = $this->calculatePercentChange($activeWorkshops, $lastWeekActiveWorkshops);

        // Count total vehicles and calculate percentage change
        $totalVehicles = Vehicle::count();
        $lastWeekVehicles = Vehicle::whereDate('created_at', '<', $oneWeekAgo)->count();
        $vehiclesPercentChange = $this->calculatePercentChange($totalVehicles, $lastWeekVehicles);

        // Count upcoming reminders (due in next 7 days)
        $upcomingReminders = VehicleReminder::whereDate('reminder_date', '>=', $now)
            ->whereDate('reminder_date', '<=', $now->copy()->addDays(7))
            ->count();
        // Last week's upcoming reminders (that were due in the 7 days after one week ago)
        $lastWeekUpcomingReminders = VehicleReminder::whereDate('reminder_date', '>=', $oneWeekAgo)
            ->whereDate('reminder_date', '<=', $oneWeekAgo->copy()->addDays(7))
            ->count();
        $upcomingRemindersPercentChange = $this->calculatePercentChange($upcomingReminders, $lastWeekUpcomingReminders);

        // Count upcoming services (scheduled in next 7 days)
        $upcomingServices = VehicleService::whereDate('service_date', '>=', $now)
            ->whereDate('service_date', '<=', $now->copy()->addDays(7))
            ->count();
        // Last week's upcoming services
        $lastWeekUpcomingServices = VehicleService::whereDate('service_date', '>=', $oneWeekAgo)
            ->whereDate('service_date', '<=', $oneWeekAgo->copy()->addDays(7))
            ->count();
        $upcomingServicesPercentChange = $this->calculatePercentChange($upcomingServices, $lastWeekUpcomingServices);

        // Monthly statistics - registration counts for the last 12 months
        $lastYear = Carbon::now()->subYear();
        $monthlyRegistrations = User::select(
            DB::raw('DATE_FORMAT(created_at, "%Y-%m") as month'),
            DB::raw('COUNT(CASE WHEN user_type = "'.UserType::CUSTOMER->value.'" THEN 1 END) as customers'),
            DB::raw('COUNT(CASE WHEN user_type = "'.UserType::VENDOR->value.'" THEN 1 END) as vendors')
        )
            ->where('created_at', '>=', $lastYear)
            ->groupBy('month')
            ->orderBy('month')
            ->get();

        // Recent maintenance services (last 10)
        $recentServices = VehicleService::with(['vehicle', 'vehicle.user'])
            ->orderBy('service_date', 'desc')
            ->take(10)
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'vehicle_id' => $service->vehicle_id,
                    'vehicle_name' => $service->vehicle->name ?? 'Unknown',
                    'user_name' => $service->vehicle->user->name ?? 'Unknown',
                    'service_type' => $service->service_type,
                    'service_date' => $service->service_date,
                    'maintenance_time' => $service->maintenance_time,
                    'notes' => $service->notes,
                ];
            });

        // Workshop statistics - by city
        $workshopsByCity = Workshop::select('city', DB::raw('count(*) as total'))
            ->groupBy('city')
            ->orderBy('total', 'desc')
            ->get();

        $statistics = [
            'customers' => [
                'count' => $totalCustomers,
                'percent_change' => $customersPercentChange,
            ],
            'vendors' => [
                'count' => $totalVendors,
                'percent_change' => $vendorsPercentChange,
            ],
            'workshops' => [
                'count' => $totalWorkshops,
                'percent_change' => $workshopsPercentChange,
            ],
            'active_workshops' => [
                'count' => $activeWorkshops,
                'percent_change' => $activeWorkshopsPercentChange,
            ],
            'vehicles' => [
                'count' => $totalVehicles,
                'percent_change' => $vehiclesPercentChange,
            ],
            'upcoming_reminders' => [
                'count' => $upcomingReminders,
                'percent_change' => $upcomingRemindersPercentChange,
            ],
            'upcoming_services' => [
                'count' => $upcomingServices,
                'percent_change' => $upcomingServicesPercentChange,
            ],
            'monthly_registrations' => $monthlyRegistrations,
            'recent_services' => $recentServices,
            'workshops_by_city' => $workshopsByCity,
        ];

        return ApiResponse::success(
            new DashboardStatisticsResource($statistics),
            'Dashboard statistics retrieved successfully'
        );
    }

    public function getMaintenanceLogs(Request $request)
    {
        $query = VehicleService::with(['vehicle', 'vehicle.user']);

        // Apply filters if provided
        if ($request->has('vehicle_name')) {
            $query->whereHas('vehicle', function ($q) use ($request) {
                $q->where('name', 'like', "%{$request->vehicle_name}%");
            });
        }

        if ($request->has('service_type')) {
            $query->where('service_type', 'like', "%{$request->service_type}%");
        }

        // Sort
        $sortField = $request->input('sort_field', 'service_date');
        $sortDirection = $request->input('sort_direction', 'desc');
        $query->orderBy($sortField, $sortDirection);

        // Paginate
        $perPage = $request->input('per_page', 10);
        $logs = $query->paginate($perPage);

        return ApiResponse::success(
            new MaintenanceLogCollection($logs),
            'Vehicle maintenance logs retrieved successfully'
        );
    }

    public function getMonthlyMaintenanceCount(Request $request)
    {
        // Get number of months to look back (default 12)
        $months = $request->input('months', 12);

        // Calculate start date
        $startDate = Carbon::now()->subMonths($months)->startOfMonth();
        $endDate = Carbon::now()->endOfMonth();

        // Get monthly maintenance counts
        $monthlyCounts = VehicleService::select(
            DB::raw('YEAR(service_date) as year'),
            DB::raw('MONTH(service_date) as month'),
            DB::raw('MONTHNAME(service_date) as month_name'),
            DB::raw('COUNT(*) as count')
        )
            ->whereDate('service_date', '>=', $startDate)
            ->whereDate('service_date', '<=', $endDate)
            ->groupBy('year', 'month', 'month_name')
            ->orderBy('year')
            ->orderBy('month')
            ->get();

        // Fill in missing months with zero counts
        $result = [];
        $currentDate = Carbon::parse($startDate);

        while ($currentDate <= $endDate) {
            $year = $currentDate->year;
            $month = $currentDate->month;
            $monthName = $currentDate->format('M');

            // Check if we have data for this month
            $found = false;
            foreach ($monthlyCounts as $count) {
                if ($count->year == $year && $count->month == $month) {
                    $result[] = [
                        'year' => $year,
                        'month' => $month,
                        'month_name' => $monthName,
                        'count' => $count->count,
                    ];
                    $found = true;
                    break;
                }
            }

            // If no data found, add zero count
            if (! $found) {
                $result[] = [
                    'year' => $year,
                    'month' => $month,
                    'month_name' => $monthName,
                    'count' => 0,
                ];
            }

            // Move to next month
            $currentDate->addMonth();
        }

        return ApiResponse::success(
            $result,
            'Monthly maintenance counts retrieved successfully'
        );
    }
}
