<?php

namespace App\Http\Resources\Vehicle;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'make_brand' => $this->make_brand,
            'model_year' => $this->model_year,
            'mileage' => $this->mileage,
            'vin_number' => $this->vin_number,
            'chassis_number' => $this->chassis_number,
            'vehicle_type' => $this->vehicle_type,
            'plate_number' => $this->plate_number,
        ];
    }
}
