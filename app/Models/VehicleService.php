<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleService extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'vehicle_id',
        'service_type',
        'service_date',
        'maintenance_time',
        'mileage',
        'cost',
        'review',
    ];

    protected $casts = [
        'service_date' => 'date',
        'maintenance_time' => 'datetime:H:i',
        'mileage' => 'integer',
        'cost' => 'decimal:2',
        'review' => 'json',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // VehicleService model is a standalone entity
        });
    }

    /**
     * Get the vehicle that owns this service
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }

    /**
     * Get the workshop that performed this service
     */
    public function workshop(): BelongsTo
    {
        return $this->belongsTo(Workshop::class);
    }
}
