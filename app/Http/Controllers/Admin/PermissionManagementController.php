<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\PermissionResource;
use App\Http\Resources\Admin\RoleResource;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Spatie\Permission\Models\Permission;

class PermissionManagementController extends Controller
{
    public function listPermissions(Request $request): JsonResponse
    {
        try {
            $permissions = Permission::all();

            return ApiResponse::success([
                'permissions' => PermissionResource::collection($permissions),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve permissions', $e);
        }
    }

    public function getPermission(int $id): JsonResponse
    {
        try {
            $permission = Permission::findOrFail($id);

            return ApiResponse::success([
                'permission' => new PermissionResource($permission),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve permission', $e);
        }
    }

    public function createPermission(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'name' => 'required|string|unique:permissions,name',
            ]);

            if ($validator->fails()) {
                return ApiResponse::validationError($validator->errors());
            }

            DB::beginTransaction();

            $permission = Permission::create([
                'name' => $request->name,
                'guard_name' => 'web',
            ]);

            DB::commit();

            return ApiResponse::success([
                'permission' => new PermissionResource($permission),
            ], 'Permission created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create permission', $e);
        }
    }

    public function updatePermission(Request $request, int $id): JsonResponse
    {
        try {
            $permission = Permission::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'name' => 'sometimes|string|unique:permissions,name,'.$id,
            ]);

            if ($validator->fails()) {
                return ApiResponse::validationError($validator->errors());
            }

            DB::beginTransaction();

            if ($request->has('name')) {
                $permission->name = $request->name;
                $permission->save();
            }

            DB::commit();

            return ApiResponse::success([
                'permission' => new PermissionResource($permission),
            ], 'Permission updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update permission', $e);
        }
    }

    public function deletePermission(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $permission = Permission::findOrFail($id);
            $permission->delete();

            DB::commit();

            return ApiResponse::success([], 'Permission deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete permission', $e);
        }
    }

    public function getRolesForPermission(int $id): JsonResponse
    {
        try {
            $permission = Permission::findOrFail($id);
            $roles = $permission->roles;

            return ApiResponse::success([
                'roles' => RoleResource::collection($roles),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve permission roles', $e);
        }
    }
}
