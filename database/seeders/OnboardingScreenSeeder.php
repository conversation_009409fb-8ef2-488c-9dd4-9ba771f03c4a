<?php

namespace Database\Seeders;

use App\Models\OnboardingScreen;
use App\Models\OnboardingScreenTranslation;
use Illuminate\Database\Seeder;

class OnboardingScreenSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create onboarding screens with translations for both English and Arabic

        // Screen 1 - Welcome
        $screen1 = OnboardingScreen::create([
            'order' => 1,
            'image' => 'onboarding/welcome.png', // Will need to be uploaded separately
            'is_active' => true,
        ]);

        // English translation for Screen 1
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen1->id,
            'locale' => 'en',
            'title' => 'Welcome to Nabih',
            'description' => 'Your trusted platform for vehicle services and maintenance.',
        ]);

        // Arabic translation for Screen 1
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen1->id,
            'locale' => 'ar',
            'title' => 'مرحباً بك في نبيه',
            'description' => 'منصتك الموثوقة لخدمات وصيانة المركبات.',
        ]);

        // Screen 2 - Services
        $screen2 = OnboardingScreen::create([
            'order' => 2,
            'image' => 'onboarding/services.png', // Will need to be uploaded separately
            'is_active' => true,
        ]);

        // English translation for Screen 2
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen2->id,
            'locale' => 'en',
            'title' => 'Comprehensive Services',
            'description' => 'Find all your vehicle needs in one place - maintenance, repairs, and more.',
        ]);

        // Arabic translation for Screen 2
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen2->id,
            'locale' => 'ar',
            'title' => 'خدمات شاملة',
            'description' => 'اعثر على كل احتياجات مركبتك في مكان واحد - الصيانة والإصلاح والمزيد.',
        ]);

        // Screen 3 - Vendors
        $screen3 = OnboardingScreen::create([
            'order' => 3,
            'image' => 'onboarding/vendors.png', // Will need to be uploaded separately
            'is_active' => true,
        ]);

        // English translation for Screen 3
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen3->id,
            'locale' => 'en',
            'title' => 'Professional Vendors',
            'description' => 'Connect with trusted, qualified service providers for your vehicle needs.',
        ]);

        // Arabic translation for Screen 3
        OnboardingScreenTranslation::create([
            'onboarding_screen_id' => $screen3->id,
            'locale' => 'ar',
            'title' => 'مقدمي خدمات محترفين',
            'description' => 'تواصل مع مزودي خدمة موثوقين ومؤهلين لتلبية احتياجات مركبتك.',
        ]);
    }
}
