<?php

namespace App\Services\Auth;

use App\Enums\UserType;
use App\Mail\VendorEmailOtpMail;
use App\Models\Otp;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class OtpService
{
    public function generateOtp(string $identifier, string $type = 'phone')
    {
        $otpCode = $this->generateSecureOtp();
        $otp = $this->storeOtp($identifier, $otpCode, $type);
        $this->sendOtp($identifier, $otpCode, $type);

        return $otp;
    }

    private function generateSecureOtp(): string
    {
        if (app()->environment('local') || config('app.nabih.test_otp_enabled')) {
            return '1234';
        }

        return str_pad(strval(random_int(0, 9999)), 4, '0', STR_PAD_LEFT);
    }

    public function resendOtp(string $identifier, string $type = 'phone')
    {
        $query = Otp::where('expires_at', '>', Carbon::now());

        if ($type === 'phone') {
            $query->where('phone_number', $identifier);
        } else {
            $query->where('email', $identifier);
        }

        $existingOtp = $query->first();

        if (! $existingOtp) {
            $otpObject = $this->generateOtp($identifier, $type);
            $this->sendOtp($identifier, $otpObject->otp, $type);

            return $otpObject;
        }

        $cooldownSeconds = $this->calculateCooldownPeriod($existingOtp->resend_count);

        $existingOtp->resend_count += 1;
        $existingOtp->next_resend_at = Carbon::now()->addSeconds($cooldownSeconds);
        $existingOtp->expires_at = Carbon::now()->addMinutes(15);
        $existingOtp->save();

        $this->sendOtp($identifier, $existingOtp->otp, $type);

        return $existingOtp;
    }

    private function calculateCooldownPeriod(int $resendCount): int
    {
        switch ($resendCount) {
            case 0:
                return 30; // First resend after 30 seconds
            case 1:
                return 60; // Second resend after 1 minute
            case 2:
                return 120; // Third resend after 2 minutes
            default:
                return 900; // Subsequent resends after 15 minutes
        }
    }

    private function storeOtp(string $identifier, string $otpCode, string $type = 'phone'): Otp
    {
        if ($type === 'phone') {
            Otp::where('phone_number', $identifier)->delete();

            return Otp::create([
                'phone_number' => $identifier,
                'otp' => $otpCode,
                'expires_at' => Carbon::now()->addMinutes(15),
                'resend_count' => 0,
                'next_resend_at' => Carbon::now()->addSeconds(30), // Allow first resend after 30 seconds
            ]);
        } else {
            Otp::where('email', $identifier)->delete();

            return Otp::create([
                'email' => $identifier,
                'otp' => $otpCode,
                'expires_at' => Carbon::now()->addMinutes(15),
                'resend_count' => 0,
                'next_resend_at' => Carbon::now()->addSeconds(30), // Allow first resend after 30 seconds
            ]);
        }
    }

    public function sendOtp(string $identifier, string $otp, string $type = 'phone'): bool
    {
        if ($type === 'phone') {
            return $this->sendOtpSms($identifier, $otp);
        } else {
            return $this->sendOtpEmail($identifier, $otp);
        }
    }

    private function sendOtpSms(string $phoneNumber, string $otp): bool
    {
        try {
            $whatsappService = new WhatsAppService;
            $success = $whatsappService->sendOtp($phoneNumber, $otp);

            if ($success) {
                return true;
            }

            Log::info("WhatsApp OTP failed, falling back to SMS for {$phoneNumber}");
        } catch (\Exception $e) {
            Log::error("WhatsApp OTP error: {$e->getMessage()}");
        }

        return false;
    }

    private function sendOtpEmail(string $email, string $otp): bool
    {
        try {
            $user = User::where('email', $email)->first();

            if ($user) {
                if ($user->user_type === UserType::VENDOR) {
                    // Use the vendor-specific email template for vendors
                    Mail::to($email)->send(new VendorEmailOtpMail($otp, $user->name, 15));
                }
            }

            if (app()->environment('local')) {
                Log::info("Email OTP for {$email}: {$otp}");
            }

            return true;
        } catch (\Exception $e) {
            Log::error('Error sending OTP email: '.$e->getMessage());

            return false;
        }
    }

    public function verifyOtp(string $identifier, string $otpCode, string $type = 'phone'): bool
    {
        $query = Otp::where('expires_at', '>', Carbon::now());

        if ($type === 'phone') {
            $query->where('phone_number', $identifier);
        } else {
            $query->where('email', $identifier);
        }

        $otp = $query->first();

        if (! $otp) {
            return false;
        }

        if ($otp->otp === $otpCode) {
            $otp->delete();

            return true;
        }

        return false;
    }
}
