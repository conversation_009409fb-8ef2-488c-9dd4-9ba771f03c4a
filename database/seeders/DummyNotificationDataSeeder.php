<?php

namespace Database\Seeders;

use App\Models\Offer;
use App\Models\Vehicle;
use App\Models\VehicleReminder;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Database\Seeder;

class DummyNotificationDataSeeder extends Seeder
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $this->command->info('Creating dummy notification data...');

        // Create dummy offers and their notifications manually
        $this->createDummyOffers();

        // Create dummy vehicle reminders for all users/vehicles
        $this->createDummyVehicleReminders();

        $this->command->info('Dummy notification data created successfully!');
    }

    /**
     * Create dummy offers and their notifications using NotificationService
     */
    private function createDummyOffers(): void
    {
        $this->command->info('Creating dummy offers...');

        $offers = [
            [
                'title' => [
                    'en' => 'Special Discount on Oil Change',
                    'ar' => 'خصم خاص على تغيير الزيت',
                ],
                'description' => [
                    'en' => 'Get 25% off on your next oil change service. Limited time offer!',
                    'ar' => 'احصل على خصم 25% على خدمة تغيير الزيت التالية. عرض لفترة محدودة!',
                ],
                'discount_text' => [
                    'en' => '25% OFF',
                    'ar' => 'خصم 25%',
                ],
                'button_text' => [
                    'en' => 'Get Discount',
                    'ar' => 'احصل على الخصم',
                ],
                'button_link' => '#oil-change-offer',
                'is_active' => true,
                'is_limited_time' => true,
                'display_order' => 1,
                'start_date' => Carbon::today(),
                'end_date' => Carbon::today()->addDays(30),
            ],
            [
                'title' => [
                    'en' => 'Free Vehicle Inspection',
                    'ar' => 'فحص مجاني للمركبة',
                ],
                'description' => [
                    'en' => 'Complete vehicle inspection at no cost. Valid for all customers.',
                    'ar' => 'فحص شامل للمركبة بدون تكلفة. صالح لجميع العملاء.',
                ],
                'discount_text' => [
                    'en' => 'FREE',
                    'ar' => 'مجاني',
                ],
                'button_text' => [
                    'en' => 'Book Now',
                    'ar' => 'احجز الآن',
                ],
                'button_link' => '#free-inspection',
                'is_active' => true,
                'is_limited_time' => true,
                'display_order' => 2,
                'start_date' => Carbon::today(),
                'end_date' => Carbon::today()->addDays(15),
            ],
            [
                'title' => [
                    'en' => 'Winter Tire Special',
                    'ar' => 'عرض خاص على إطارات الشتاء',
                ],
                'description' => [
                    'en' => 'Prepare for winter with our special tire deals. Up to 40% off!',
                    'ar' => 'استعد للشتاء مع عروضنا الخاصة على الإطارات. خصم يصل إلى 40%!',
                ],
                'discount_text' => [
                    'en' => 'UP TO 40% OFF',
                    'ar' => 'خصم يصل إلى 40%',
                ],
                'button_text' => [
                    'en' => 'Shop Tires',
                    'ar' => 'تسوق الإطارات',
                ],
                'button_link' => '#winter-tires',
                'is_active' => true,
                'is_limited_time' => false,
                'display_order' => 3,
                'start_date' => Carbon::today()->addDays(1),
                'end_date' => Carbon::today()->addDays(45),
            ],
        ];

        $createdOffers = 0;
        $createdNotifications = 0;

        foreach ($offers as $offerData) {
            // Create the offer
            $offer = Offer::create($offerData);
            $createdOffers++;

            // Since OfferObserver only triggers on 'updated', we need to manually create notifications
            try {
                $notification = $this->notificationService->createOfferNotification($offer);
                $createdNotifications++;
                $this->command->info("Created notification for offer: {$offer->title['en']}");
            } catch (\Exception $e) {
                $this->command->error("Failed to create notification for offer {$offer->id}: {$e->getMessage()}");
            }
        }

        $this->command->info("Created {$createdOffers} dummy offers and {$createdNotifications} notifications");
    }

    /**
     * Create dummy vehicle reminders for all users/vehicles
     */
    private function createDummyVehicleReminders(): void
    {
        $this->command->info('Creating dummy vehicle reminders...');

        $vehicles = Vehicle::with('user')->get();

        if ($vehicles->isEmpty()) {
            $this->command->warn('No vehicles found. Please ensure you have vehicles in your database.');

            return;
        }

        $reminderTypes = ['oil_change', 'tire_rotation', 'brake_inspection', 'battery_check'];
        $dates = [
            Carbon::today(),           // Today
            Carbon::tomorrow(),        // Tomorrow
            Carbon::today()->addDays(55), // 55 days from today
        ];

        $createdCount = 0;

        foreach ($vehicles as $vehicle) {
            foreach ($dates as $date) {
                // Create 2 different reminder types per vehicle per date
                $selectedTypes = array_slice($reminderTypes, 0, 2);

                foreach ($selectedTypes as $index => $reminderType) {
                    // Check if reminder already exists to avoid duplicates
                    $exists = VehicleReminder::where('vehicle_id', $vehicle->id)
                        ->where('reminder_type', $reminderType)
                        ->where('reminder_date', $date->format('Y-m-d'))
                        ->exists();

                    if (! $exists) {
                        VehicleReminder::create([
                            'vehicle_id' => $vehicle->id,
                            'reminder_type' => $reminderType,
                            'reminder_date' => $date->format('Y-m-d'),
                            'is_mileage_based' => false, // Date-based reminders
                            'mileage' => null, // No mileage for date-based reminders
                            'review' => [
                                'en' => 'Time for your '.str_replace('_', ' ', $reminderType).' for '.$vehicle->name,
                                'ar' => 'حان وقت '.$this->getArabicReminderType($reminderType).' لـ '.$vehicle->name,
                            ],
                        ]);

                        $createdCount++;
                    }
                }
            }
        }

        $this->command->info("Created {$createdCount} dummy vehicle reminders for {$vehicles->count()} vehicles");
    }

    /**
     * Get Arabic translation for reminder types
     */
    private function getArabicReminderType(string $type): string
    {
        $translations = [
            'oil_change' => 'تغيير الزيت',
            'tire_rotation' => 'تدوير الإطارات',
            'brake_inspection' => 'فحص المكابح',
            'battery_check' => 'فحص البطارية',
        ];

        return $translations[$type] ?? $type;
    }
}
