<?php

namespace App\Http\Requests\Vehicle;

use App\Http\Requests\BaseApiRequest;
use App\Models\Vehicle;

class DeleteVehicleRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        $vehicleId = $this->route('id');
        $vehicle = Vehicle::findOrFail($vehicleId);

        return $this->user()->can('delete', $vehicle);
    }

    public function rules(): array
    {
        return [];
    }

    public function messages(): array
    {
        return [];
    }
}
