<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class OnboardingScreenTranslation extends Model
{
    use SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // OnboardingScreenTranslation model is a standalone entity
        });
    }

    protected $fillable = [
        'onboarding_screen_id',
        'locale',
        'title',
        'description',
    ];

    public function onboardingScreen(): BelongsTo
    {
        return $this->belongsTo(OnboardingScreen::class);
    }
}
