<?php

namespace App\Http\Requests\Auth\Customer;

use App\Http\Requests\BaseApiRequest;

class CustomerRegisterRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'phone_number' => 'required|string|regex:/^\+[1-9]\d{1,14}$/|unique:users,phone_number,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        $messages = [
            'en' => [
                'name.required' => 'Name is required',
                'name.string' => 'Name must be a string',
                'name.max' => 'Name must be at most 255 characters',
                'phone_number.required' => 'Phone number is required',
                'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
                'phone_number.unique' => 'This phone number is already registered. Please log in instead',
            ],
            'ar' => [
                'name.required' => 'الاسم مطلوب',
                'name.string' => 'يجب أن يكون الاسم نص',
                'name.max' => 'يجب أن يكون الاسم أقل من 255 حرف',
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صحيح بصيغة E.164 (مثال: +966501234567)',
                'phone_number.unique' => 'رقم الهاتف غير مسجل. الرجاء التسجيل أولاً.',
            ],
        ];

        return $messages[$locale] ?? $messages['en'];
    }
}
