<?php

namespace Database\Factories;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VehicleReminder>
 */
class VehicleReminderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $isMileageBased = $this->faker->boolean;

        $reminderData = [
            'vehicle_id' => Vehicle::factory(),
            'reminder_type' => $this->faker->randomElement(['Oil Change', 'Tire Rotation', 'Insurance Renewal', 'Registration Renewal', 'Scheduled Maintenance']),
            'is_mileage_based' => $isMileageBased,
            'review' => json_encode([
                'en' => $this->faker->paragraph(3),
            ]),
        ];

        if ($isMileageBased) {
            $reminderData['mileage'] = $this->faker->numberBetween(5000, 100000);
            $reminderData['reminder_date'] = null;
        } else {
            $reminderData['reminder_date'] = $this->faker->dateTimeBetween('now', '+1 year');
            $reminderData['mileage'] = null;
        }

        return $reminderData;
    }
}
