<?php

namespace App\Http\Requests\OnboardingScreen;

use Illuminate\Foundation\Http\FormRequest;

class StoreOnboardingScreenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            'order' => 'nullable|integer|min:1',
            'image' => 'nullable|image|max:2048', // 2MB max
            'is_active' => 'nullable|boolean',
            'translations' => 'required|array',
            'translations.en' => 'required|array',
            'translations.*.title' => 'required|string|max:255',
            'translations.*.description' => 'required|string',
        ];
    }
}
