<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleNote;
use Illuminate\Auth\Access\HandlesAuthorization;

class VehicleNotePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function view(User $user, VehicleNote $note)
    {
        return $user->id === $note->vehicle->user_id;
    }

    public function create(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function update(User $user, VehicleNote $note)
    {
        return $user->id === $note->vehicle->user_id;
    }

    public function delete(User $user, VehicleNote $note)
    {
        return $user->id === $note->vehicle->user_id;
    }
}
