<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\Vendor\WorkshopResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VendorResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'first_name' => $this->first_name,
            'last_name' => $this->last_name,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'address' => $this->address,
            'city' => $this->city,
            'is_vendor_account_approved' => $this->is_vendor_account_approved,
            'avatar_url' => $this->getFirstMediaUrl('avatar') ?? null,
            'workshops' => WorkshopResource::collection($this->whenLoaded('workshops')),
        ];
    }
}
