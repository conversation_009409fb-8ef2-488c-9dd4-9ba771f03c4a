<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('social_media_links', function (Blueprint $table) {
            $table->id();
            $table->string('type')->comment('Type of social media (from SocialMediaType enum)');
            $table->string('title')->comment('Display title for the social media link');
            $table->string('url')->comment('Full URL of the social media profile');
            $table->string('username')->nullable()->comment('Username or handle without URL');
            $table->text('description')->nullable()->comment('Optional description');
            $table->boolean('is_active')->default(true)->comment('Whether this social media link is active');
            $table->timestamps();
            $table->softDeletes();

            // Add index for common queries
            $table->index('type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('social_media_links');
    }
};
