<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vehicle;
use App\Models\VehicleReminder;
use Illuminate\Auth\Access\HandlesAuthorization;

class VehicleReminderPolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function view(User $user, VehicleReminder $reminder)
    {
        return $user->id === $reminder->vehicle->user_id;
    }

    public function create(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function update(User $user, VehicleReminder $reminder)
    {
        return $user->id === $reminder->vehicle->user_id;
    }

    public function delete(User $user, VehicleReminder $reminder)
    {
        return $user->id === $reminder->vehicle->user_id;
    }
}
