APP_NAME="Nibah"
APP_ENV=local
APP_KEY=
APP_DEBUG=true
APP_URL=https://dev.nabih.sa

# NGINX and SSL Configuration
NGINX_HOST=dev.nabih.sa
LETSENCRYPT_EMAIL=<EMAIL>

APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US

APP_MAINTENANCE_DRIVER=file
# APP_MAINTENANCE_STORE=database

PHP_CLI_SERVER_WORKERS=4

BCRYPT_ROUNDS=12

LOG_CHANNEL=stack
LOG_STACK=single
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=mysql
DB_PORT=3306
DB_DATABASE=${DB_DATABASE:-laravel}
DB_USERNAME=${DB_USERNAME:-laravel}
DB_PASSWORD=${DB_PASSWORD:-secret}

SESSION_DRIVER=redis
SESSION_LIFETIME=120
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

BROADCAST_CONNECTION=log
FILESYSTEM_DISK=local
QUEUE_CONNECTION=redis

CACHE_DRIVER=redis
CACHE_STORE=redis
# CACHE_PREFIX=

MEMCACHED_HOST=127.0.0.1

REDIS_CLIENT=phpredis
REDIS_HOST=redis
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=log
MAIL_SCHEME=null
MAIL_HOST=127.0.0.1
MAIL_PORT=2525
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

VITE_APP_NAME="${APP_NAME}"

# Domain and SSL Configuration
APP_DOMAIN=dev.nabih.sa
SSL_ENABLED=true
SSL_PROVIDER=aws_alb
NGINX_HOST=${APP_DOMAIN}

# AWS ALB Configuration
# Set to true when using AWS Application Load Balancer
USE_AWS_ALB=true
# ALB forwards HTTP traffic to containers (SSL termination at ALB)
ALB_HTTP_PORT=80

# Firebase Configuration
FIREBASE_PROJECT_ID=nabih-59244
FIREBASE_CREDENTIALS_FILE=config/firebase.json
FIREBASE_SERVER_KEY=
FIREBASE_SENDER_ID=
