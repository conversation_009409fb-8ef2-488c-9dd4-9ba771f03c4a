<?php

namespace App\Http\Requests\Admin;

use App\Enums\SupportRequestStatus;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateSupportRequestRequest extends BaseApiRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('support.update');
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'admin_response' => 'required|string|max:5000',
            'status' => ['required', 'string', new Enum(SupportRequestStatus::class)],
        ];
    }
}
