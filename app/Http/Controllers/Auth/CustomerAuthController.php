<?php

namespace App\Http\Controllers\Auth;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\Customer\CustomerLoginRequest;
use App\Http\Requests\Auth\Customer\CustomerRegisterRequest;
use App\Http\Requests\Auth\Customer\CustomerResendOtpRequest;
use App\Http\Requests\Auth\Customer\CustomerVerifyOtpRequest;
use App\Http\Resources\OtpResource;
use App\Models\User;
use App\Services\Auth\CustomerAuthService;
use App\Services\FcmTokenService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class CustomerAuthController extends Controller
{
    protected CustomerAuthService $authService;

    protected FcmTokenService $fcmTokenService;

    public function __construct(CustomerAuthService $authService, FcmTokenService $fcmTokenService)
    {
        $this->authService = $authService;
        $this->fcmTokenService = $fcmTokenService;
    }

    public function register(CustomerRegisterRequest $request): JsonResponse
    {
        try {
            $this->authService->createUnVerifiedUser($request->name, $request->phone_number);
            $otp = $this->authService->sendOtp($request->phone_number);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP sent successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to send OTP for registration', $e);
        }
    }

    public function loginThroughOTP(CustomerLoginRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->sendOtp($request->phone_number);

            // Store FCM token if provided
            if ($request->fcm_token) {
                $user = User::where('phone_number', $request->phone_number)->first();
                if ($user) {
                    $this->fcmTokenService->addOrUpdateToken($user->id, $request->fcm_token, $request->device_type, $request->device_name);
                }
            }

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP sent successfully for login');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to send OTP', $e);
        }
    }

    public function verifyOtp(CustomerVerifyOtpRequest $request): JsonResponse
    {
        try {
            $data = $this->authService->processOtpVerification($request->phone_number);

            return ApiResponse::success($data, 'User Login successful');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to verify OTP', $e);
        }
    }

    public function logout(Request $request): JsonResponse
    {
        try {
            $this->authService->logout($request->user(), $request->fcm_token);

            return ApiResponse::success([], 'Logged out successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Logout failed', $e);
        }
    }

    public function resendOtp(CustomerResendOtpRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->resendOtp($request->phone_number);

            return ApiResponse::success(new OtpResource($otp), 'OTP resent successfully');
        } catch (\Exception $e) {
            return ApiResponse::errorWithLog('Failed to resend OTP', $e);
        }
    }
}
