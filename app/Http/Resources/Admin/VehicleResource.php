<?php

namespace App\Http\Resources\Admin;

use App\Http\Resources\Vehicle\VehicleServiceResource;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'phone_number' => $this->phone_number,
            'make_brand' => $this->make_brand,
            'model_year' => $this->model_year,
            'mileage' => $this->mileage,
            'vehicle_type' => $this->vehicle_type,
            'plate_number' => $this->plate_number,
            'vin_number' => $this->vin_number,
            'chassis_number' => $this->chassis_number,
            'owner' => [
                'id' => $this->user->id,
                'name' => $this->user->name,
                'email' => $this->user->email,
                'phone' => $this->user->phone_number,
            ],
            'services' => $this->whenLoaded('services', function () {
                return VehicleServiceResource::collection($this->services);
            }),
        ];
    }
}
