<?php

namespace App\Http\Requests\Customer;

use App\Enums\Gender;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class CustomerProfileUpdateRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->isCustomer();
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'gender' => ['required', new Enum(Gender::class)],
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    public function messages()
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            if (app()->getLocale() === 'ar') {
                return [
                    'name.required' => 'الاسم مطلوب',
                    'name.string' => 'يجب أن يكون الاسم نصًا',
                    'name.max' => 'يجب ألا يتجاوز الاسم 255 حرفًا',
                    'gender.required' => 'الجنس مطلوب',
                    'gender.enum' => 'تم اختيار جنس غير صالح',
                    'avatar.image' => 'يجب أن تكون الصورة الرمزية صورة',
                    'avatar.mimes' => 'يجب أن تكون الصورة الرمزية بتنسيق jpeg أو png أو jpg أو gif',
                    'avatar.max' => 'يجب ألا يتجاوز حجم الصورة الرمزية 2 ميجابايت',
                ];
            }
        }

        return [
            'name.required' => 'Name is required',
            'name.string' => 'Name must be a string',
            'name.max' => 'Name must not exceed 255 characters',
            'gender.required' => 'Gender is required',
            'gender.enum' => 'Invalid gender selected',
            'avatar.image' => 'Avatar must be an image',
            'avatar.mimes' => 'Avatar must be a jpeg, png, jpg, or gif file',
            'avatar.max' => 'Avatar size must not exceed 2MB',
        ];
    }
}
