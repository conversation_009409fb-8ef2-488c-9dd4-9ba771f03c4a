<?php

namespace App\Observers;

use App\Models\WorkingHour;
use App\Models\Workshop;

class WorkshopObserver
{
    /**
     * Handle the Workshop "created" event.
     *
     * @return void
     */
    public function created(Workshop $workshop)
    {
        // Create working hours for all days of the week with null start and end times
        $days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];

        foreach ($days as $day) {
            WorkingHour::create([
                'workshop_id' => $workshop->id,
                'day_of_week' => $day,
                'start_time' => null,
                'end_time' => null,
                'is_day_off' => false,
            ]);
        }
    }
}
