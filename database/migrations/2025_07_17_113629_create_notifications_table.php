<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->nullable()->constrained()->onDelete('cascade'); // Nullable for common notifications
            $table->json('title'); // Multilingual support
            $table->json('description'); // Multilingual support
            $table->string('notification_type'); // Changed to string for extensibility
            $table->morphs('notifiable'); // Adds notifiable_type and notifiable_id
            $table->timestamps();
            $table->softDeletes();

            // Indexes for performance
            $table->index('user_id');
            $table->index('notification_type');
            // Note: morphs() already creates index for notifiable_type and notifiable_id
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('notifications');
    }
};
