<?php

namespace App\Console\Commands;

use App\Jobs\VehicleReminderNotificationJob;
use App\Models\VehicleReminder;
use App\Services\NotificationService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class SendVehicleReminderNotificationCommand extends Command
{
    protected $signature = 'reminders:send-notifications';

    protected $description = 'Send notifications for vehicle reminders that are due';

    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    public function handle()
    {
        $this->info('Starting vehicle reminder notifications...');

        try {
            // Get all reminders that are due today
            $dueReminders = VehicleReminder::where('reminder_date', '<=', Carbon::today())
                ->whereDoesntHave('notifications') // Avoid duplicate notifications
                ->with(['vehicle.user'])
                ->get();

            if ($dueReminders->isEmpty()) {
                $this->info('No due reminders found.');

                return 0;
            }

            $successCount = 0;
            $failureCount = 0;

            foreach ($dueReminders as $reminder) {
                try {
                    // Get the user for this reminder
                    $user = $reminder->vehicle->user;

                    if (! $user) {
                        $this->error("✗ No user found for reminder ID: {$reminder->id}");
                        $failureCount++;

                        continue;
                    }

                    // Create notification for this reminder
                    $notification = $this->notificationService->createReminderNotification($reminder, $user);

                    if ($notification) {
                        $successCount++;
                        $this->info("✓ Notification created for reminder ID: {$reminder->id}");

                        // Dispatch FCM push notification job
                        VehicleReminderNotificationJob::dispatch($reminder, $user);
                        $this->info("✓ FCM notification job dispatched for reminder ID: {$reminder->id}");
                    } else {
                        $failureCount++;
                        $this->error("✗ Failed to create notification for reminder ID: {$reminder->id}");
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    $this->error("✗ Error creating notification for reminder ID: {$reminder->id} - {$e->getMessage()}");
                    Log::error("Reminder notification failed for reminder {$reminder->id}: ".$e->getMessage());
                }
            }

            $this->info("\n=== Summary ===");
            $this->info('Total reminders processed: '.$dueReminders->count());
            $this->info("Successful notifications: {$successCount}");
            $this->info("Failed notifications: {$failureCount}");

            Log::info('Vehicle reminder notifications processed', [
                'total' => $dueReminders->count(),
                'success' => $successCount,
                'failures' => $failureCount,
            ]);

            return 0;
        } catch (\Exception $e) {
            $this->error('Command failed: '.$e->getMessage());
            Log::error('Vehicle reminder notifications command failed: '.$e->getMessage());

            return 1;
        }
    }
}
