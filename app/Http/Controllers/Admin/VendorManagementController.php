<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateVendorRequest;
use App\Http\Requests\Admin\UpdateVendorRequest;
use App\Http\Resources\Admin\VendorCollection;
use App\Http\Resources\UserResource;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VendorManagementController extends Controller
{
    public function createVendor(CreateVendorRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor = User::create([
                'first_name' => $request->first_name,
                'last_name' => $request->last_name,
                'email' => $request->email,
                'phone_number' => $request->phone_number,
                'user_type' => UserType::VENDOR->value,
                'is_verified' => false,
            ]);

            DB::commit();

            return ApiResponse::success([
                'vendor' => $vendor,
            ], 'Vendor user created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create vendor user', $e);
        }
    }

    public function updateVendor(UpdateVendorRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor = User::where('id', $id)
                ->where('user_type', UserType::VENDOR->value)
                ->firstOrFail();

            // Get validated data directly
            $vendorData = $request->validated();

            // Update vendor with validated data
            if (! empty($vendorData)) {
                $vendor->update($vendorData);
            }

            // Handle avatar upload if provided
            if ($request->hasFile('avatar')) {
                $vendor->clearMediaCollection('avatar');
                $vendor->addMediaFromRequest('avatar')
                    ->toMediaCollection('avatar');
            }

            DB::commit();

            return ApiResponse::success([
                'vendor' => $vendor,
            ], 'Vendor user updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update vendor user', $e);
        }
    }

    public function deleteVendor(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor = User::where('id', $id)
                ->where('user_type', UserType::VENDOR->value)
                ->firstOrFail();

            $vendor->delete();

            DB::commit();

            return ApiResponse::success([], 'Vendor user deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete vendor user', $e);
        }
    }

    public function listVendors(Request $request): JsonResponse
    {
        try {
            $query = User::where('user_type', UserType::VENDOR->value);

            if ($request->has('approved')) {
                $query->where('is_vendor_account_approved', $request->approved == 'true');
            }

            if ($request->has('verified')) {
                $query->where('is_verified', $request->verified == 'true');
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone_number', 'like', "%{$search}%");
                });
            }

            $vendors = $query->paginate($request->per_page ?? 15);

            return ApiResponse::success(
                new VendorCollection($vendors),
                'Vendors retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve vendors', $e);
        }
    }

    public function getVendor(int $id): JsonResponse
    {
        try {
            $vendor = User::where('id', $id)
                ->where('user_type', UserType::VENDOR->value)
                ->firstOrFail();

            return ApiResponse::success([
                'vendor' => new UserResource($vendor),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve vendor details', $e);
        }
    }

    /**
     * List all soft-deleted vendor users
     */
    public function listDeletedVendors(Request $request): JsonResponse
    {
        try {
            $query = User::onlyTrashed()
                ->where('user_type', UserType::VENDOR->value);

            // Apply search filter if provided
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%")
                        ->orWhere('phone_number', 'like', "%$search%");
                });
            }

            $vendors = $query->paginate($request->per_page ?? 15);

            return ApiResponse::success(
                new VendorCollection($vendors),
                'Deleted vendor users retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve deleted vendor users', $e);
        }
    }

    /**
     * Restore a soft-deleted vendor
     */
    public function restoreVendor(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor = User::onlyTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::VENDOR->value)
                ->firstOrFail();

            $vendor->restore();

            DB::commit();

            return ApiResponse::success([
                'vendor' => new UserResource($vendor),
            ], 'Vendor user restored successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to restore vendor user', $e);
        }
    }

    /**
     * Permanently delete a soft-deleted vendor
     */
    public function permanentlyDeleteVendor(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $vendor = User::withTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::VENDOR->value)
                ->firstOrFail();

            // Force delete the vendor
            $vendor->forceDelete();

            DB::commit();

            return ApiResponse::success([], 'Vendor user permanently deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to permanently delete vendor user', $e);
        }
    }
}
