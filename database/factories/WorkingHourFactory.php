<?php

namespace Database\Factories;

use App\Models\WorkingHour;
use App\Models\Workshop;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkingHourFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WorkingHour::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        $startHour = $this->faker->numberBetween(7, 10);
        $endHour = $this->faker->numberBetween(17, 22);

        return [
            'workshop_id' => Workshop::factory(), // This will be overridden in the seeder
            'day_of_week' => $this->faker->randomElement(['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday']),
            'start_time' => sprintf('%02d:00:00', $startHour),
            'end_time' => sprintf('%02d:00:00', $endHour),
            'is_day_off' => false,
        ];
    }

    /**
     * Configure the model factory to indicate a day off.
     *
     * @return $this
     */
    public function dayOff()
    {
        return $this->state(function (array $attributes) {
            return [
                'start_time' => null,
                'end_time' => null,
                'is_day_off' => true,
            ];
        });
    }
}
