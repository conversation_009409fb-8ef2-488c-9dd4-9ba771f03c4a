<?php

namespace App\Http\Requests\Vehicle;

use App\DTOs\VehicleReminderDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Vehicle;
use App\Models\VehicleReminder;
use Illuminate\Validation\Rule;

class VehicleReminderRequest extends BaseApiRequest
{
    protected function prepareForValidation()
    {
        if ($reminderId = $this->route('reminderId')) {
            $this->merge(['reminderId' => $reminderId]);
        }
    }

    public function authorize(): bool
    {
        // Create operation
        if (! $this->route('reminderId')) {
            // Find the vehicle (404 if not found)
            $vehicle = Vehicle::findOrFail($this->route('vehicleId'));

            return $this->user()->can('create', [VehicleReminder::class, $vehicle]);
        }

        // Update/delete operation
        $reminder = VehicleReminder::findOrFail($this->route('reminderId'));

        return $this->user()->can('update', $reminder);
    }

    public function rules(): array
    {
        $rules = [
            'reminder_type' => ['required', 'string', 'max:255'],
            'is_mileage_based' => ['required', 'boolean'],
            'mileage' => ['required_if:is_mileage_based,1', 'integer', 'min:0'],
            'reminder_date' => ['required_if:is_mileage_based,0', 'date'],
            'review' => ['nullable', 'string'],
        ];

        // For update operations, validate that the reminder exists and belongs to the vehicle
        if ($this->route('reminderId')) {
            $rules['reminderId'] = [
                'required',
                Rule::exists('vehicle_reminders', 'id')->where(function ($query) {
                    $query->where('vehicle_id', $this->route('vehicleId'));
                }),
            ];
        }

        return $rules;
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'reminder_type.required' => 'نوع التذكير مطلوب',
                'is_mileage_based.required' => 'يجب تحديد ما إذا كان التذكير يعتمد على المسافة',
                'review.string' => 'يجب أن تكون المراجعة نصية',
                'mileage.required_if' => 'عدد الكيلومترات مطلوب عندما يكون التذكير معتمد على المسافة',
                'reminder_date.required_if' => 'تاريخ التذكير مطلوب عندما لا يكون التذكير معتمد على المسافة',
                'reminderId.exists' => 'الIDL التذكير غير موجود',
            ];
        }

        return [
            'reminder_type.required' => 'Reminder type is required',
            'is_mileage_based.required' => 'Is mileage based is required',
            'review.string' => 'Review must be a string',
            'mileage.required_if' => 'Mileage is required when remainder method is based on mileage',
            'reminder_date.required_if' => 'Reminder date is required when remainder method based on date',
            'reminderId.exists' => 'The reminder ID does not exist',
        ];
    }

    public function toDTO(): VehicleReminderDTO
    {
        $validated = $this->validated();
        $vehicleId = $this->route('vehicleId');
        $currentLanguage = app()->getLocale();

        // Store the original text as-is in the source language
        $review = null;
        if (! empty($validated['review'])) {
            $review = [
                $currentLanguage => $validated['review'],
            ];
        }

        return new VehicleReminderDTO(
            vehicle_id: $vehicleId,
            reminder_type: $validated['reminder_type'],
            is_mileage_based: $validated['is_mileage_based'],
            reminder_date: $validated['reminder_date'] ?? null,
            mileage: $validated['mileage'] ?? null,
            review: $review
        );
    }
}
