<?php

namespace App\Http\Resources\Admin;

use Illuminate\Http\Resources\Json\JsonResource;

class DashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     */
    public function toArray($request): array
    {
        return [
            'statistics' => [
                'customers' => [
                    'count' => $this['customers']['count'] ?? 0,
                    'percent_change' => $this['customers']['percent_change'] ?? 0,
                ],
                'vendors' => [
                    'count' => $this['vendors']['count'] ?? 0,
                    'percent_change' => $this['vendors']['percent_change'] ?? 0,
                ],
                'workshops' => [
                    'count' => $this['workshops']['count'] ?? 0,
                    'percent_change' => $this['workshops']['percent_change'] ?? 0,
                ],
                'active_workshops' => [
                    'count' => $this['active_workshops']['count'] ?? 0,
                    'percent_change' => $this['active_workshops']['percent_change'] ?? 0,
                ],
                'vehicles' => [
                    'count' => $this['vehicles']['count'] ?? 0,
                    'percent_change' => $this['vehicles']['percent_change'] ?? 0,
                ],
                'upcoming_reminders' => [
                    'count' => $this['upcoming_reminders']['count'] ?? 0,
                    'percent_change' => $this['upcoming_reminders']['percent_change'] ?? 0,
                ],
                'upcoming_services' => [
                    'count' => $this['upcoming_services']['count'] ?? 0,
                    'percent_change' => $this['upcoming_services']['percent_change'] ?? 0,
                ],
            ],
            'monthly_registrations' => $this['monthly_registrations'] ?? [],
            'recent_services' => $this['recent_services'] ?? [],
            'workshops_by_city' => $this['workshops_by_city'] ?? [],
        ];
    }
}
