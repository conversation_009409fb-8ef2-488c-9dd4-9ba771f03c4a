<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class CreateAdminRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('admin.create');
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'password' => 'required|string|min:8',
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ];
    }

    public function attributes(): array
    {
        return [
            'email' => 'admin email',
            'roles.*' => 'role',
            'permissions.*' => 'permission',
        ];
    }
}
