<?php

namespace Database\Factories;

use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\VehicleService>
 */
class VehicleServiceFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'vehicle_id' => Vehicle::factory(),
            'service_type' => $this->faker->randomElement(['Oil Change', 'Tire Rotation', 'Brake Service', 'Engine Tune-Up', 'Battery Replacement', 'Air Filter Replacement']),
            'service_date' => $this->faker->dateTimeBetween('-1 year', 'now'),
            'maintenance_time' => $this->faker->time(),
            'mileage' => $this->faker->numberBetween(5000, 100000),
            'cost' => $this->faker->randomFloat(2, 20, 1000),
            'review' => [
                'en' => $this->faker->paragraph(3),
                'ar' => 'تعليق على الخدمة: '.$this->faker->sentence(10),
            ],
        ];
    }
}
