<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OtpResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'expires_at' => $this->expires_at,
            'resend_count' => $this->resend_count,
            'next_resend_at' => $this->next_resend_at,
        ];
    }
}
