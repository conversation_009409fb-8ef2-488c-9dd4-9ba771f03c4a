<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AssignPermissionsRequest;
use App\Http\Requests\Admin\CreateRoleRequest;
use App\Http\Requests\Admin\UpdateRoleRequest;
use App\Http\Resources\Admin\RoleResource;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\DB;
use Spatie\Permission\Models\Role;

class RoleManagementController extends Controller
{
    public function listRoles(): JsonResponse
    {
        try {
            $roles = Role::with('permissions')
                ->where('name', '!=', 'super-admin')
                ->get();

            return ApiResponse::success([
                'roles' => RoleResource::collection($roles),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve roles', $e);
        }
    }

    public function getRole(int $id): JsonResponse
    {
        try {
            $role = Role::with('permissions')->findOrFail($id);

            return ApiResponse::success([
                'role' => new RoleResource($role),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve role', $e);
        }
    }

    public function createRole(CreateRoleRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $role = Role::create(['name' => $request->name, 'guard_name' => 'web']);

            if ($request->has('permissions')) {
                $role->syncPermissions($request->permissions);
            }

            DB::commit();

            return ApiResponse::success([
                'role' => new RoleResource($role->load('permissions')),
            ], 'Role created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create role', $e);
        }
    }

    public function updateRole(UpdateRoleRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();
            $role = Role::findOrFail($id);

            if ($role->name === 'super-admin') {
                DB::rollBack();

                return ApiResponse::error('Cannot modify super-admin role', 403);
            }

            if ($request->has('name')) {
                $role->name = $request->name;
                $role->save();
            }

            DB::commit();

            return ApiResponse::success([
                'role' => new RoleResource($role->load('permissions')),
            ], 'Role updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update role', $e);
        }
    }

    public function deleteRole(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $role = Role::findOrFail($id);

            if ($role->name === 'super-admin') {
                DB::rollBack();

                return ApiResponse::error('Cannot delete super-admin role', 403);
            }

            $role->delete();

            DB::commit();

            return ApiResponse::success([], 'Role deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete role', $e);
        }
    }

    public function assignPermissions(AssignPermissionsRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $role = Role::findOrFail($id);

            if ($role->name === 'super-admin') {
                DB::rollBack();

                return ApiResponse::error('Cannot modify super-admin role permissions', 403);
            }

            $role->givePermissionTo($request->permissions);

            DB::commit();

            return ApiResponse::success([
                'role' => new RoleResource($role->load('permissions')),
            ], 'Permissions assigned successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to assign permissions', $e);
        }
    }

    public function revokePermissions(AssignPermissionsRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $role = Role::findOrFail($id);

            if ($role->name === 'super-admin') {
                DB::rollBack();

                return ApiResponse::error('Cannot modify super-admin role permissions', 403);
            }

            $role->revokePermissionTo($request->permissions);

            DB::commit();

            return ApiResponse::success([
                'role' => new RoleResource($role->load('permissions')),
            ], 'Permissions revoked successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to revoke permissions', $e);
        }
    }
}
