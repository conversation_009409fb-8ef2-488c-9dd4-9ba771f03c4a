<?php

return [
    // Common API messages
    'Validation failed' => 'فشل في التحقق من صحة البيانات',

    // Authentication & Authorization
    'Admin login successful' => 'تم تسجيل دخول المشرف بنجاح',
    'User Login successful' => 'تم تسجيل دخول المستخدم بنجاح',
    'Logged out successfully' => 'تم تسجيل الخروج بنجاح',
    'Admin logged out successfully' => 'تم تسجيل خروج المشرف بنجاح',
    'Vendor logged out successfully' => 'تم تسجيل خروج المورد بنجاح',
    'OTP sent successfully' => 'تم إرسال كلمة المرور المؤقتة بنجاح',
    'OTP sent successfully for login' => 'تم إرسال كلمة المرور المؤقتة لتسجيل الدخول بنجاح',
    'OTP sent successfully for vendor registration' => 'تم إرسال كلمة المرور المؤقتة لتسجيل المورد بنجاح',
    'OTP sent successfully for vendor login' => 'تم إرسال كلمة المرور المؤقتة لدخول المورد بنجاح',
    'OTP resent successfully' => 'تم إعادة إرسال كلمة المرور المؤقتة بنجاح',
    'Phone OTP resent successfully' => 'تم إعادة إرسال كلمة المرور المؤقتة للهاتف بنجاح',
    'Email OTP resent successfully' => 'تم إعادة إرسال كلمة المرور المؤقتة للبريد الإلكتروني بنجاح',
    'Failed to login' => 'فشل في تسجيل الدخول',
    'Failed to send OTP for registration' => 'فشل في إرسال كلمة المرور المؤقتة للتسجيل',
    'Failed to send OTP' => 'فشل في إرسال كلمة المرور المؤقتة',
    'Failed to send OTP for vendor registration' => 'فشل في إرسال كلمة المرور المؤقتة لتسجيل المورد',
    'Failed to send OTP for vendor login' => 'فشل في إرسال كلمة المرور المؤقتة لدخول المورد',
    'Failed to verify OTP' => 'فشل في التحقق من كلمة المرور المؤقتة',
    'Failed to verify phone OTP' => 'فشل في التحقق من كلمة المرور المؤقتة للهاتف',
    'Failed to verify email OTP' => 'فشل في التحقق من كلمة المرور المؤقتة للبريد الإلكتروني',
    'Logout failed' => 'فشل في تسجيل الخروج',
    'Admin logout failed' => 'فشل في تسجيل خروج المشرف',
    'Vendor logout failed' => 'فشل في تسجيل خروج المورد',
    'Failed to resend OTP' => 'فشل في إعادة إرسال كلمة المرور المؤقتة',
    'Failed to resend phone OTP' => 'فشل في إعادة إرسال كلمة المرور المؤقتة للهاتف',
    'Failed to resend email OTP' => 'فشل في إعادة إرسال كلمة المرور المؤقتة للبريد الإلكتروني',

    // Account Management
    'Your account has been deleted successfully' => 'تم حذف حسابك بنجاح',
    'Failed to delete your account' => 'حدث خطأ خلال عملية حذف الحساب',
    'Super admin accounts cannot be deleted' => 'لا يمكن حذف حساب المشرف العام',

    // Profile Management
    'Customer profile retrieved successfully' => 'تم استرداد ملف العميل الشخصي بنجاح',
    'Vendor profile retrieved successfully' => 'تم استرداد ملف المورد الشخصي بنجاح',
    'Your profile has been updated successfully' => 'تم تحديث ملفك الشخصي بنجاح',
    'Vendor profile updated successfully' => 'تم تحديث ملف المورد الشخصي بنجاح',
    'Gender options retrieved successfully' => 'تم استرداد خيارات الجنس بنجاح',
    'Avatar deleted successfully' => 'تم حذف الصورة الرمزية بنجاح',
    'Avatar uploaded successfully' => 'تم رفع الصورة الرمزية بنجاح',

    // Vehicle Management
    'Vehicles retrieved successfully' => 'تم استرداد المركبات بنجاح',
    'Vehicle added successfully' => 'تم إضافة المركبة بنجاح',
    'Vehicle updated successfully' => 'تم تحديث المركبة بنجاح',
    'Vehicle deleted successfully' => 'تم حذف المركبة بنجاح',
    'Vehicle details retrieved successfully' => 'تم استرداد تفاصيل المركبة بنجاح',
    'Vehicle created successfully' => 'تم إنشاء المركبة بنجاح',
    'Vehicle permanently deleted successfully' => 'تم حذف المركبة نهائياً بنجاح',
    'Vehicle not found' => 'المركبة غير موجودة',
    'You do not have permission to view this vehicle' => 'ليس لديك إذن لعرض هذه المركبة',
    'Failed to create vehicle' => 'فشل في إنشاء المركبة',
    'Failed to update vehicle' => 'فشل في تحديث المركبة',
    'Failed to delete vehicle' => 'فشل في حذف المركبة',

    // Vehicle Services
    'Service added successfully' => 'تمت إضافة الخدمة بنجاح',
    'Services synchronized successfully' => 'تمت مزامنة الخدمات بنجاح',
    'Service retrieved successfully' => 'تم استرداد الخدمة بنجاح',
    'Service updated successfully' => 'تم تحديث الخدمة بنجاح',
    'Service deleted successfully' => 'تم حذف الخدمة بنجاح',
    'Service not found' => 'الخدمة غير موجودة',
    'You do not have permission to view this service' => 'ليس لديك إذن لعرض هذه الخدمة',
    'You do not have permission to delete this service' => 'ليس لديك إذن لحذف هذه الخدمة',
    'You do not have permission to view these services' => 'ليس لديك إذن لعرض هذه الخدمات',
    'You are not authorized to delete this service' => 'غير مخول لك حذف هذه الخدمة',
    'Failed to create service' => 'فشل في إنشاء الخدمة',
    'Failed to update service' => 'فشل في تحديث الخدمة',
    'Failed to delete service' => 'فشل في حذف الخدمة',

    // Vehicle Notes
    'Notes retrieved successfully' => 'تم استرداد الملاحظات بنجاح',
    'Note added successfully' => 'تمت إضافة الملاحظة بنجاح',
    'Note retrieved successfully' => 'تم استرداد الملاحظة بنجاح',
    'Note updated successfully' => 'تم تحديث الملاحظة بنجاح',
    'Note deleted successfully' => 'تم حذف الملاحظة بنجاح',
    'Note not found' => 'الملاحظة غير موجودة',
    'You do not have permission to view this note' => 'ليس لديك إذن لعرض هذه الملاحظة',
    'You do not have permission to view these notes' => 'ليس لديك إذن لعرض هذه الملاحظات',
    'You do not have permission to delete this note' => 'ليس لديك إذن لحذف هذه الملاحظة',
    'Failed to create note' => 'فشل في إنشاء الملاحظة',
    'Failed to update note' => 'فشل في تحديث الملاحظة',
    'Failed to delete note' => 'فشل في حذف الملاحظة',

    // Vehicle Reminders
    'Reminders retrieved successfully' => 'تم استرداد التذكيرات بنجاح',
    'Reminder added successfully' => 'تمت إضافة التذكير بنجاح',
    'Reminder retrieved successfully' => 'تم استرداد التذكير بنجاح',
    'Reminder updated successfully' => 'تم تحديث التذكير بنجاح',
    'Reminder deleted successfully' => 'تم حذف التذكير بنجاح',
    'Reminder not found' => 'التذكير غير موجود',
    'You do not have permission to view this reminder' => 'ليس لديك إذن لعرض هذا التذكير',
    'You do not have permission to view these reminders' => 'ليس لديك إذن لعرض هذه التذكيرات',
    'You do not have permission to delete this reminder' => 'ليس لديك إذن لحذف هذا التذكير',
    'Failed to create reminder' => 'فشل في إنشاء التذكير',
    'Failed to update reminder' => 'فشل في تحديث التذكير',
    'Failed to delete reminder' => 'فشل في حذف التذكير',

    // Workshop Management
    'Workshops retrieved successfully' => 'تم استرداد الورش بنجاح',
    'Workshop created successfully' => 'تم إنشاء الورشة بنجاح',
    'Workshop retrieved successfully' => 'تم استرداد الورشة بنجاح',
    'Workshop updated successfully' => 'تم تحديث الورشة بنجاح',
    'Workshop deleted successfully' => 'تم حذف الورشة بنجاح',
    'Workshop image uploaded successfully' => 'تم رفع صورة الورشة بنجاح',
    'Workshop image deleted successfully' => 'تم حذف صورة الورشة بنجاح',
    'Working hours retrieved successfully' => 'تم استرداد ساعات العمل بنجاح',
    'Working hours updated successfully' => 'تم تحديث ساعات العمل بنجاح',
    'Services retrieved successfully' => 'تم استرداد الخدمات بنجاح',
    'Workshop not found' => 'الورشة غير موجودة',
    'Unauthorized access to this workshop' => 'دخول غير مصرح به لهذه الورشة',
    'Failed to create workshop' => 'فشل في إنشاء الورشة',
    'Failed to update workshop' => 'فشل في تحديث الورشة',
    'Failed to delete workshop' => 'فشل في حذف الورشة',
    'Failed to upload workshop image' => 'فشل في رفع صورة الورشة',
    'Failed to delete workshop image' => 'فشل في حذف صورة الورشة',
    'Failed to retrieve working hours' => 'فشل في استرداد ساعات العمل',
    'Failed to update working hours' => 'فشل في تحديث ساعات العمل',

    // Customer Workshops
    'Workshop bookmarked successfully' => 'تمت إضافة الورشة للمفضلة بنجاح',
    'Workshop bookmark removed successfully' => 'تمت إزالة الورشة من المفضلة بنجاح',
    'Bookmarked workshops retrieved successfully' => 'تم استرداد الورش المفضلة بنجاح',
    'Failed to toggle bookmark' => 'فشل في تغيير حالة المفضلة',
    'Failed to retrieve bookmarked workshops' => 'فشل في استرداد الورش المفضلة',

    // Support Requests
    'Support requests retrieved successfully' => 'تم استرداد طلبات الدعم بنجاح',
    'Support request created successfully' => 'تم إنشاء طلب الدعم بنجاح',
    'Support request retrieved successfully' => 'تم استرداد طلب الدعم بنجاح',
    'Support request status options retrieved successfully' => 'تم استرداد خيارات حالة طلب الدعم بنجاح',
    'Support request issue type options retrieved successfully' => 'تم استرداد خيارات نوع مشكلة طلب الدعم بنجاح',
    'Support request options retrieved successfully' => 'تم استرداد خيارات طلب الدعم بنجاح',
    'Failed to retrieve support requests' => 'فشل في استرداد طلبات الدعم',
    'Failed to create support request' => 'فشل في إنشاء طلب الدعم',
    'Failed to retrieve support request' => 'فشل في استرداد طلب الدعم',

    // Notifications
    'Notifications retrieved successfully' => 'تم استرداد الإشعارات بنجاح',
    'Notification retrieved successfully' => 'تم استرداد الإشعار بنجاح',
    'All notifications marked as read successfully' => 'تم تمييز جميع الإشعارات كمقروءة بنجاح',
    'Notification status toggled successfully' => 'تم تغيير حالة الإشعار بنجاح',
    'Unread count retrieved successfully' => 'تم استرداد عدد الإشعارات غير المقروءة بنجاح',
    'Notification not found' => 'الإشعار غير موجود',
    'Notification not found or does not belong to you' => 'الإشعار غير موجود أو لا ينتمي إليك',
    'Failed to toggle notification status' => 'فشل في تغيير حالة الإشعار',
    'Failed to retrieve notifications' => 'فشل في استرداد الإشعارات',
    'Failed to retrieve notification' => 'فشل في استرداد الإشعار',
    'Failed to mark all notifications as read' => 'فشل في تمييز جميع الإشعارات كمقروءة',
    'Failed to get unread count' => 'فشل في الحصول على عدد الإشعارات غير المقروءة',

    // Offers
    'Offers retrieved successfully' => 'تم استرداد العروض بنجاح',
    'No offers available' => 'لا توجد عروض متاحة',
    'Offer created successfully' => 'تم إنشاء العرض بنجاح',
    'Offer updated successfully' => 'تم تحديث العرض بنجاح',
    'Offer deleted successfully' => 'تم حذف العرض بنجاح',
    'Offer display orders updated successfully' => 'تم تحديث ترتيب عرض العروض بنجاح',
    'Failed to retrieve offers' => 'فشل في استرداد العروض',
    'Failed to create offer' => 'فشل في إنشاء العرض',
    'Failed to update offer' => 'فشل في تحديث العرض',
    'Failed to delete offer' => 'فشل في حذف العرض',
    'Failed to update offer display orders' => 'فشل في تحديث ترتيب عرض العروض',

    // Admin Management
    'Admin users retrieved successfully' => 'تم استرداد المستخدمين المشرفين بنجاح',
    'Admin user created successfully' => 'تم إنشاء المستخدم المشرف بنجاح',
    'Admin user updated successfully' => 'تم تحديث المستخدم المشرف بنجاح',
    'Admin user deleted successfully' => 'تم حذف المستخدم المشرف بنجاح',
    'Admin user permanently deleted successfully' => 'تم حذف المستخدم المشرف نهائياً بنجاح',
    'Admin user restored successfully' => 'تم استرداد المستخدم المشرف بنجاح',
    'Failed to create admin user' => 'فشل في إنشاء المستخدم المشرف',
    'Failed to update admin user' => 'فشل في تحديث المستخدم المشرف',
    'Failed to delete admin user' => 'فشل في حذف المستخدم المشرف',
    'Failed to permanently delete admin user' => 'فشل في حذف المستخدم المشرف نهائياً',
    'Failed to restore admin user' => 'فشل في استرداد المستخدم المشرف',
    'Failed to retrieve admin users' => 'فشل في استرداد المستخدمين المشرفين',

    // Vendor Management
    'Vendors retrieved successfully' => 'تم استرداد الموردين بنجاح',
    'Vendor user created successfully' => 'تم إنشاء مستخدم المورد بنجاح',
    'Vendor user updated successfully' => 'تم تحديث مستخدم المورد بنجاح',
    'Vendor user deleted successfully' => 'تم حذف مستخدم المورد بنجاح',
    'Vendor user permanently deleted successfully' => 'تم حذف مستخدم المورد نهائياً بنجاح',
    'Vendor user restored successfully' => 'تم استرداد مستخدم المورد بنجاح',
    'Vendor details retrieved successfully' => 'تم استرداد تفاصيل المورد بنجاح',
    'Unapproved vendors retrieved successfully' => 'تم استرداد الموردين غير المعتمدين بنجاح',
    'Approved vendors retrieved successfully' => 'تم استرداد الموردين المعتمدين بنجاح',
    'Vendor approved successfully' => 'تم اعتماد المورد بنجاح',
    'Vendor disapproved successfully' => 'تم رفض اعتماد المورد بنجاح',
    'User is not a vendor' => 'المستخدم ليس مورداً',
    'Failed to create vendor user' => 'فشل في إنشاء مستخدم المورد',
    'Failed to update vendor user' => 'فشل في تحديث مستخدم المورد',
    'Failed to delete vendor user' => 'فشل في حذف مستخدم المورد',
    'Failed to permanently delete vendor user' => 'فشل في حذف مستخدم المورد نهائياً',
    'Failed to restore vendor user' => 'فشل في استرداد مستخدم المورد',
    'Failed to retrieve vendors' => 'فشل في استرداد الموردين',
    'Failed to retrieve vendor details' => 'فشل في استرداد تفاصيل المورد',
    'Failed to retrieve deleted vendor users' => 'فشل في استرداد مستخدمي المورد المحذوفين',

    // Customer Management
    'Customers retrieved successfully' => 'تم استرداد العملاء بنجاح',
    'Customer user created successfully' => 'تم إنشاء مستخدم العميل بنجاح',
    'Customer user updated successfully' => 'تم تحديث مستخدم العميل بنجاح',
    'Customer user deleted successfully' => 'تم حذف مستخدم العميل بنجاح',
    'Customer user permanently deleted successfully' => 'تم حذف مستخدم العميل نهائياً بنجاح',
    'Customer user restored successfully' => 'تم استرداد مستخدم العميل بنجاح',
    'Customer details retrieved successfully' => 'تم استرداد تفاصيل العميل بنجاح',
    'Failed to create customer user' => 'فشل في إنشاء مستخدم العميل',
    'Failed to update customer user' => 'فشل في تحديث مستخدم العميل',
    'Failed to delete customer user' => 'فشل في حذف مستخدم العميل',
    'Failed to permanently delete customer user' => 'فشل في حذف مستخدم العميل نهائياً',
    'Failed to restore customer user' => 'فشل في استرداد مستخدم العميل',
    'Failed to retrieve customers' => 'فشل في استرداد العملاء',
    'Failed to retrieve customer details' => 'فشل في استرداد تفاصيل العميل',
    'Failed to retrieve deleted customer users' => 'فشل في استرداد مستخدمي العميل المحذوفين',

    // Role & Permission Management
    'Roles retrieved successfully' => 'تم استرداد الأدوار بنجاح',
    'Role created successfully' => 'تم إنشاء الدور بنجاح',
    'Role updated successfully' => 'تم تحديث الدور بنجاح',
    'Role deleted successfully' => 'تم حذف الدور بنجاح',
    'Role permissions retrieved successfully' => 'تم استرداد صلاحيات الدور بنجاح',
    'Permissions retrieved successfully' => 'تم استرداد الصلاحيات بنجاح',
    'Permission created successfully' => 'تم إنشاء الصلاحية بنجاح',
    'Permission updated successfully' => 'تم تحديث الصلاحية بنجاح',
    'Permission deleted successfully' => 'تم حذف الصلاحية بنجاح',
    'Permission roles retrieved successfully' => 'تم استرداد أدوار الصلاحية بنجاح',
    'Failed to retrieve roles' => 'فشل في استرداد الأدوار',
    'Failed to create role' => 'فشل في إنشاء الدور',
    'Failed to update role' => 'فشل في تحديث الدور',
    'Failed to delete role' => 'فشل في حذف الدور',
    'Failed to retrieve role permissions' => 'فشل في استرداد صلاحيات الدور',
    'Failed to retrieve permissions' => 'فشل في استرداد الصلاحيات',
    'Failed to create permission' => 'فشل في إنشاء الصلاحية',
    'Failed to update permission' => 'فشل في تحديث الصلاحية',
    'Failed to delete permission' => 'فشل في حذف الصلاحية',
    'Failed to retrieve permission roles' => 'فشل في استرداد أدوار الصلاحية',

    // User Management
    'Users retrieved successfully' => 'تم استرداد المستخدمين بنجاح',
    'User created successfully' => 'تم إنشاء المستخدم بنجاح',
    'User updated successfully' => 'تم تحديث المستخدم بنجاح',
    'User deleted successfully' => 'تم حذف المستخدم بنجاح',
    'User details retrieved successfully' => 'تم استرداد تفاصيل المستخدم بنجاح',
    'Failed to retrieve users' => 'فشل في استرداد المستخدمين',
    'Failed to create user' => 'فشل في إنشاء المستخدم',
    'Failed to update user' => 'فشل في تحديث المستخدم',
    'Failed to delete user' => 'فشل في حذف المستخدم',
    'Failed to retrieve user details' => 'فشل في استرداد تفاصيل المستخدم',

    // FAQ Management
    'FAQs retrieved successfully' => 'تم استرداد الأسئلة الشائعة بنجاح',
    'FAQ created successfully' => 'تم إنشاء السؤال الشائع بنجاح',
    'FAQ updated successfully' => 'تم تحديث السؤال الشائع بنجاح',
    'FAQ deleted successfully' => 'تم حذف السؤال الشائع بنجاح',
    'FAQs reordered successfully' => 'تم إعادة ترتيب الأسئلة الشائعة بنجاح',
    'Failed to retrieve FAQs' => 'فشل في استرداد الأسئلة الشائعة',
    'Failed to create FAQ' => 'فشل في إنشاء السؤال الشائع',
    'Failed to retrieve FAQ' => 'فشل في استرداد السؤال الشائع',
    'Failed to update FAQ' => 'فشل في تحديث السؤال الشائع',
    'Failed to delete FAQ' => 'فشل في حذف السؤال الشائع',
    'Failed to reorder FAQs' => 'فشل في إعادة ترتيب الأسئلة الشائعة',

    // Onboarding Screens
    'Onboarding screens retrieved successfully' => 'تم استرداد شاشات التعريف بنجاح',
    'Onboarding screen created successfully' => 'تم إنشاء شاشة التعريف بنجاح',
    'Onboarding screen updated successfully' => 'تم تحديث شاشة التعريف بنجاح',
    'Onboarding screen deleted successfully' => 'تم حذف شاشة التعريف بنجاح',
    'Onboarding screens reordered successfully' => 'تم إعادة ترتيب شاشات التعريف بنجاح',
    'Failed to retrieve onboarding screens' => 'فشل في استرداد شاشات التعريف',
    'Failed to create onboarding screen' => 'فشل في إنشاء شاشة التعريف',
    'Failed to retrieve onboarding screen' => 'فشل في استرداد شاشة التعريف',
    'Failed to update onboarding screen' => 'فشل في تحديث شاشة التعريف',
    'Failed to delete onboarding screen' => 'فشل في حذف شاشة التعريف',
    'Failed to reorder onboarding screens' => 'فشل في إعادة ترتيب شاشات التعريف',

    // Static Content
    'Static contents retrieved successfully' => 'تم استرداد المحتوى الثابت بنجاح',
    'Static content created/updated successfully' => 'تم إنشاء/تحديث المحتوى الثابت بنجاح',
    'Static content retrieved successfully' => 'تم استرداد المحتوى الثابت بنجاح',
    'Static content deleted successfully' => 'تم حذف المحتوى الثابت بنجاح',
    'Invalid content type' => 'نوع محتوى غير صالح',
    'Invalid user type' => 'نوع مستخدم غير صالح',
    'Content not found' => 'المحتوى غير موجود',
    'Failed to retrieve static contents' => 'فشل في استرداد المحتوى الثابت',
    'Failed to create/update static content' => 'فشل في إنشاء/تحديث المحتوى الثابت',
    'Failed to retrieve static content' => 'فشل في استرداد المحتوى الثابت',
    'Failed to delete static content' => 'فشل في حذف المحتوى الثابت',

    // Generic Error Messages
    'Database error while retrieving offers' => 'خطأ في قاعدة البيانات أثناء استرداد العروض',
    'An error occurred while processing your request' => 'حدث خطأ أثناء معالجة طلبك',
    'Invalid data provided' => 'تم تقديم بيانات غير صالحة',
    'Unauthorized' => 'غير مخول',
    'Access denied' => 'تم رفض الوصول',
    'Resource not found' => 'المورد غير موجود',
    'Internal server error' => 'خطأ داخلي في الخادم',
    'Bad request' => 'طلب غير صالح',
    'Forbidden' => 'محظور',
    'Method not allowed' => 'الطريقة غير مسموحة',
    'Unprocessable entity' => 'كيان غير قابل للمعالجة',
    'Too many requests' => 'تم رصد محاولات أكثر من المعتاد للوصول للصفحة ، الرجاء المحاولة لاحقاً',
];
