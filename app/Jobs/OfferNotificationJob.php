<?php

namespace App\Jobs;

use App\Enums\UserType;
use App\Models\Offer;
use App\Models\User;
use App\Services\FcmTokenService;
use App\Services\FirebaseService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class OfferNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected Offer $offer;

    public function __construct(Offer $offer)
    {
        $this->offer = $offer;
    }

    public function handle(FcmTokenService $fcmTokenService, FirebaseService $firebaseService): void
    {
        try {
            Log::info('Starting offer notification job', [
                'offer_id' => $this->offer->id,
                'offer_title' => $this->offer->title,
            ]);

            // Get all active customers
            $activeCustomers = User::where('user_type', UserType::CUSTOMER)
                ->where('is_verified', true)
                ->whereNull('deleted_at')
                ->pluck('id')
                ->toArray();

            if (empty($activeCustomers)) {
                Log::info('No active customers found for offer notification', [
                    'offer_id' => $this->offer->id,
                ]);

                return;
            }

            Log::info('Found active customers for offer notification', [
                'offer_id' => $this->offer->id,
                'customer_count' => count($activeCustomers),
            ]);

            // Get all active FCM tokens for these customers
            $fcmTokens = $fcmTokenService->getMultipleUsersActiveTokens($activeCustomers);

            if (empty($fcmTokens)) {
                Log::info('No active FCM tokens found for offer notification', [
                    'offer_id' => $this->offer->id,
                    'customer_count' => count($activeCustomers),
                ]);

                return;
            }

            Log::info('Found FCM tokens for offer notification', [
                'offer_id' => $this->offer->id,
                'token_count' => count($fcmTokens),
            ]);

            // Prepare multilingual notification data
            $titleAr = 'عرض جديد متاح! 🎉';
            $titleEn = 'New Offer Available! 🎉';

            $data = [
                'type' => 'offer',
                'offer_id' => (string) $this->offer->id,
                'offer_title_ar' => $this->offer->title['ar'] ?? '',
                'offer_title_en' => $this->offer->title['en'] ?? '',
                'offer_description_ar' => $this->offer->description['ar'] ?? '',
                'offer_description_en' => $this->offer->description['en'] ?? '',
                'click_action' => 'FLUTTER_NOTIFICATION_CLICK',
            ];

            // Use English version for push notification display
            $bodyEn = $this->offer->title['en'] ?? 'New Offer';

            // Send push notification to all tokens
            $successCount = 0;
            $failureCount = 0;

            foreach ($fcmTokens as $token) {
                try {
                    // Use English as default for push notification display
                    $result = $firebaseService->sendToDevice($token, $titleEn, $bodyEn, $data);

                    if ($result['success']) {
                        $successCount++;
                        // Mark token as used
                        $fcmTokenService->markTokenAsUsed($token);
                    } else {
                        $failureCount++;
                        Log::warning('FCM notification failed', [
                            'offer_id' => $this->offer->id,
                            'token' => substr($token, 0, 20).'...',
                            'error' => $result['error'] ?? 'Unknown error',
                        ]);
                    }
                } catch (\Exception $e) {
                    $failureCount++;
                    Log::warning('Failed to send offer notification to FCM token', [
                        'offer_id' => $this->offer->id,
                        'token' => substr($token, 0, 20).'...', // Log partial token for privacy
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('Completed offer notification job', [
                'offer_id' => $this->offer->id,
                'total_tokens' => count($fcmTokens),
                'success_count' => $successCount,
                'failure_count' => $failureCount,
            ]);

        } catch (\Exception $e) {
            Log::error('Offer notification job failed', [
                'offer_id' => $this->offer->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            // Re-throw to mark job as failed
            throw $e;
        }
    }

    public function failed(\Throwable $exception): void
    {
        Log::error('Offer notification job permanently failed', [
            'offer_id' => $this->offer->id,
            'error' => $exception->getMessage(),
        ]);
    }
}
