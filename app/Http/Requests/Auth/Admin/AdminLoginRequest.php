<?php

namespace App\Http\Requests\Auth\Admin;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\User;

class AdminLoginRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|email|exists:users,email',
            'password' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        if (app()->getLocale() === 'ar') {
            return [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح',
                'email.exists' => 'البريد الإلكتروني غير مسجل',
                'password.required' => 'كلمة المرور مطلوبة',
            ];
        }

        return [
            'email.required' => 'Email is required',
            'email.email' => 'Please enter a valid email address',
            'email.exists' => 'Email is not registered',
            'password.required' => 'Password is required',
        ];
    }

    protected function afterValidation()
    {
        $user = User::where('email', $this->email)->first();

        if (! $user || $user->user_type !== UserType::ADMIN) {
            $validator = \Illuminate\Support\Facades\Validator::make($this->all(), []);
            $validator->errors()->add('email', 'Invalid admin credentials');
            $this->failedValidation($validator);
        }
    }
}
