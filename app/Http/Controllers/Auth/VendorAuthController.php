<?php

namespace App\Http\Controllers\Auth;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Auth\Vendor\VendorEmailOtpLoginRequest;
use App\Http\Requests\Auth\Vendor\VendorPhoneLoginRequest;
use App\Http\Requests\Auth\Vendor\VendorRegisterRequest;
use App\Http\Requests\Auth\Vendor\VendorResendEmailOtpRequest;
use App\Http\Requests\Auth\Vendor\VendorResendOtpRequest;
use App\Http\Requests\Auth\Vendor\VendorVerifyEmailOtpRequest;
use App\Http\Requests\Auth\Vendor\VendorVerifyPhoneOtpRequest;
use App\Http\Resources\OtpResource;
use App\Http\Resources\UserResource;
use App\Services\Auth\VendorAuthService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VendorAuthController extends Controller
{
    protected VendorAuthService $authService;

    public function __construct(VendorAuthService $authService)
    {
        $this->authService = $authService;
    }

    public function register(VendorRegisterRequest $request): JsonResponse
    {
        try {
            $this->authService->createUnVerifiedUser(
                $request->first_name,
                $request->last_name,
                $request->phone_number,
                $request->email,
            );
            $otp = $this->authService->sendOtp($request->phone_number);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP sent successfully for vendor registration');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to send OTP for vendor registration', $e);
        }
    }

    public function loginWithPhone(VendorPhoneLoginRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->sendOtp($request->phone_number);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP sent successfully for vendor login');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to send OTP for vendor login', $e);
        }
    }

    public function loginWithEmailOtp(VendorEmailOtpLoginRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->sendEmailOtp($request->email);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP sent successfully to vendor email');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to send OTP to vendor email', $e);
        }
    }

    public function verifyPhoneOtp(VendorVerifyPhoneOtpRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->processPhoneOtpVerification($request->phone_number);

            return ApiResponse::success([
                'user' => new UserResource($result['user']),
                'token' => $result['token'],
            ], 'Vendor login successful');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to verify vendor phone OTP', $e);
        }
    }

    public function verifyEmailOtp(VendorVerifyEmailOtpRequest $request): JsonResponse
    {
        try {
            $result = $this->authService->processEmailOtpVerification($request->email);

            return ApiResponse::success([
                'user' => new UserResource($result['user']),
                'token' => $result['token'],
            ], 'Vendor login successful');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to verify vendor email OTP', $e);
        }
    }

    public function logout(Request $request): JsonResponse
    {
        try {
            $this->authService->logout($request->user());

            return ApiResponse::success([], 'Vendor logged out successfully');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Vendor logout failed', $e);
        }
    }

    public function resendPhoneOtp(VendorResendOtpRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->resendOtp($request->phone_number);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP resent successfully for vendor');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to resend vendor OTP', $e);
        }
    }

    public function resendEmailOtp(VendorResendEmailOtpRequest $request): JsonResponse
    {
        try {
            $otp = $this->authService->resendEmailOtp($request->email);

            return ApiResponse::success([
                'otp' => new OtpResource($otp),
            ], 'OTP resent successfully to vendor email');
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to resend OTP to vendor email', $e);
        }
    }
}
