<?php

namespace App\Http\Controllers\Vehicle;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vehicle\CreateVehicleRequest;
use App\Http\Requests\Vehicle\DeleteVehicleRequest;
use App\Http\Requests\Vehicle\UpdateVehicleRequest;
use App\Http\Resources\Vehicle\VehicleDetailsResource;
use App\Http\Resources\Vehicle\VehicleResource;
use App\Models\Vehicle;
use App\Services\Vehicle\VehicleService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class VehicleController extends Controller
{
    protected VehicleService $vehicleService;

    public function __construct(VehicleService $vehicleService)
    {
        $this->vehicleService = $vehicleService;
    }

    public function index(Request $request): JsonResponse
    {
        $vehicles = $this->vehicleService->getAllVehicles($request->user()->id);

        return ApiResponse::success([
            'vehicles' => VehicleResource::collection($vehicles),
        ], 'Vehicles retrieved successfully');
    }

    public function store(CreateVehicleRequest $request): JsonResponse
    {
        $vehicle = $this->vehicleService->createVehicle(
            $request->user()->id,
            $request->validated()
        );

        return ApiResponse::success([
            'vehicle' => new VehicleResource($vehicle),
        ], 'Vehicle added successfully');
    }

    public function update(UpdateVehicleRequest $request, int $id): JsonResponse
    {
        $vehicle = $this->vehicleService->updateVehicle(
            $id,
            $request->user()->id,
            $request->validated()
        );

        if (! $vehicle) {
            return ApiResponse::error('Vehicle not found', 404);
        }

        return ApiResponse::success([
            'vehicle' => new VehicleResource($vehicle),
        ], 'Vehicle updated successfully');
    }

    public function destroy(DeleteVehicleRequest $request, int $id): JsonResponse
    {
        $result = $this->vehicleService->deleteVehicle($id, $request->user()->id);

        if (! $result) {
            return ApiResponse::error('Vehicle not found', 404);
        }

        return ApiResponse::success([], 'Vehicle deleted successfully');
    }

    public function show(Request $request, int $id): JsonResponse
    {

        $vehicle = Vehicle::find($id);

        if (! $vehicle) {
            return ApiResponse::error('Vehicle not found', 404);
        }

        if (! $request->user()->can('view', $vehicle)) {
            return ApiResponse::error('You do not have permission to view this vehicle', 403);
        }

        $vehicle = $this->vehicleService->getVehicleWithRelationships($id, $request->user()->id);

        return ApiResponse::success([
            'vehicle' => new VehicleDetailsResource($vehicle),
        ], 'Vehicle details retrieved successfully');
    }
}
