<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Http\Controllers\Controller;
use App\Http\Responses\ApiResponse;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;

class UserManagementController extends Controller
{
    /**
     * Create a new user with specified role and permissions
     */
    public function createUser(Request $request): JsonResponse
    {
        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'required|string|unique:users,phone_number',
            'password' => 'required|string|min:8',
            'user_type' => 'required|string|in:'.implode(',', UserType::values()),
            'roles' => 'nullable|array',
            'roles.*' => 'exists:roles,name',
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return ApiResponse::validationError($validator->errors());
        }

        // Create user
        $user = User::create([
            'name' => $request->name,
            'first_name' => $request->first_name,
            'last_name' => $request->last_name,
            'email' => $request->email,
            'phone_number' => $request->phone_number,
            'password' => Hash::make($request->password),
            'user_type' => $request->user_type,
            'is_verified' => true, // Admin-created users are auto-verified
        ]);

        // Assign roles if specified
        if ($request->has('roles')) {
            $user->assignRole($request->roles);
        }

        // Assign direct permissions if specified
        if ($request->has('permissions')) {
            $user->givePermissionTo($request->permissions);
        }

        return ApiResponse::success([
            'user' => $user->load('roles', 'permissions'),
        ], 'User created successfully');
    }

    /**
     * Update an existing user
     */
    public function updateUser(Request $request, int $id): JsonResponse
    {
        $user = User::findOrFail($id);

        // Validate request
        $validator = Validator::make($request->all(), [
            'name' => 'sometimes|string|max:255',
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'email' => 'sometimes|email|unique:users,email,'.$id,
            'phone_number' => 'sometimes|string|unique:users,phone_number,'.$id,
            'password' => 'nullable|string|min:8',
            'user_type' => 'sometimes|string|in:'.implode(',', UserType::values()),
            'is_verified' => 'sometimes|boolean',
            'is_vendor_account_approved' => 'sometimes|boolean',
        ]);

        if ($validator->fails()) {
            return ApiResponse::validationError($validator->errors());
        }

        // Update user data
        $userData = $request->only([
            'name', 'first_name', 'last_name', 'email', 'phone_number', 'user_type',
            'is_verified', 'is_vendor_account_approved',
        ]);

        if ($request->has('password')) {
            $userData['password'] = Hash::make($request->password);
        }

        $user->update($userData);

        return ApiResponse::success([
            'user' => $user->load('roles', 'permissions'),
        ], 'User updated successfully');
    }

    /**
     * Delete a user
     */
    public function deleteUser(int $id): JsonResponse
    {
        $user = User::findOrFail($id);

        // Prevent deletion of super-admin
        if ($user->hasRole('super-admin')) {
            return ApiResponse::error('Cannot delete super admin user', 403);
        }

        $user->delete();

        return ApiResponse::success([], 'User deleted successfully');
    }

    /**
     * Get list of all users
     */
    public function listUsers(Request $request): JsonResponse
    {
        $query = User::with('roles', 'permissions');

        // Filter by user type
        if ($request->has('user_type')) {
            $query->where('user_type', $request->user_type);
        }

        // Filter by role
        if ($request->has('role')) {
            $query->role($request->role);
        }

        // Search by name or email
        if ($request->has('search')) {
            $search = $request->search;
            $query->where(function ($query) use ($search) {
                $query->where('name', 'like', "%{$search}%")
                    ->orWhere('email', 'like', "%{$search}%")
                    ->orWhere('phone_number', 'like', "%{$search}%");
            });
        }

        $users = $query->paginate($request->per_page ?? 15);

        return ApiResponse::success([
            'users' => $users,
        ]);
    }

    /**
     * Get details of a specific user
     */
    public function getUser(int $id): JsonResponse
    {
        $user = User::with('roles', 'roles.permissions', 'permissions')->findOrFail($id);

        return ApiResponse::success([
            'user' => $user,
        ]);
    }

    /**
     * Assign roles to a user
     */
    public function assignRoles(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'roles' => 'required|array',
            'roles.*' => [
                'exists:roles,name',
                'not_in:super-admin',
            ],
        ], [
            'roles.*.not_in' => 'The super-admin role cannot be assigned to users. It is a system reserved role.',
        ]);

        if ($validator->fails()) {
            return ApiResponse::validationError($validator->errors());
        }

        $user = User::findOrFail($id);

        // Additional server-side check to prevent super-admin role assignment
        if (in_array('super-admin', $request->roles)) {
            return ApiResponse::error('The super-admin role cannot be assigned to users. It is a system reserved role.', 403);
        }

        // Sync roles (removes old roles, adds new ones)
        $user->syncRoles($request->roles);

        return ApiResponse::success([
            'user' => $user->load('roles', 'permissions'),
        ], 'Roles assigned successfully');
    }

    /**
     * Assign direct permissions to a user
     */
    public function assignPermissions(Request $request, int $id): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name',
        ]);

        if ($validator->fails()) {
            return ApiResponse::validationError($validator->errors());
        }

        $user = User::findOrFail($id);

        // Sync direct permissions
        $user->syncPermissions($request->permissions);

        return ApiResponse::success([
            'user' => $user->load('roles', 'permissions'),
        ], 'Permissions assigned successfully');
    }
}
