<?php

namespace App\Http\Requests\Vendor;

use App\DTOs\Workshop\WorkshopServiceDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Workshop;
use App\Models\WorkshopService;
use Illuminate\Routing\Route;

/**
 * @method mixed user() Get the authenticated user.
 * @method mixed route(?string $param = null, $default = null) Get a route parameter.
 */
class WorkshopServiceRequest extends BaseApiRequest
{
    public function authorize()
    {
        // Get the workshop service for update/delete requests
        /** @var WorkshopService|null $workshopService */
        $workshopService = $this->route('workshopService');
        $user = $this->user();

        // For create requests, check if workshop belongs to vendor
        if (! $workshopService) {
            /** @var Workshop|null $workshop */
            $workshop = $this->route('workshop');

            return $user &&
                   $user->isVendor() &&
                   $workshop &&
                   $workshop->user_id === $user->id;
        }

        // For update/delete, check if the service's workshop belongs to vendor
        return $user &&
               $user->isVendor() &&
               $workshopService->workshop->user_id === $user->id;
    }

    public function rules()
    {
        return [
            'services' => 'required|array',
            'services.*' => 'required|string|max:255',
        ];
    }

    /**
     * Convert request to collection of DTOs
     */
    public function toDTOs(int $workshop_id): array
    {
        return WorkshopServiceDTO::collectionFromRequest($this, $workshop_id);
    }

    /**
     * Convert request to single DTO (for backward compatibility)
     */
    public function toDTO(int $workshop_id): WorkshopServiceDTO
    {
        return WorkshopServiceDTO::fromRequest($this, $workshop_id);
    }
}
