<?php

namespace App\Http\Requests\Vehicle;

use App\DTOs\VehicleServiceDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Vehicle;
use App\Models\VehicleService;
use Illuminate\Validation\Rule;

class VehicleServiceRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        if (! $this->route('serviceId')) {
            $vehicle = Vehicle::findOrFail($this->route('vehicleId'));

            return $this->user()->can('create', [VehicleService::class, $vehicle]);
        }

        $service = VehicleService::findOrFail($this->route('serviceId'));

        return $this->user()->can('update', $service);
    }

    public function rules(): array
    {
        $rules = [
            'service_type' => ['required', 'string', 'max:255'],
            'service_date' => ['required', 'date'],
            'maintenance_time' => ['required', 'date_format:H:i'],
            'mileage' => ['required', 'integer', 'min:0'],
            'cost' => ['required', 'numeric', 'min:0'],
            'review' => ['nullable', 'string'],
        ];

        if ($this->route('serviceId')) {
            $rules['serviceId'] = [
                'required',
                Rule::exists('vehicle_services', 'id')->where(function ($query) {
                    $query->where('vehicle_id', $this->route('vehicleId'));
                }),
            ];
        }

        return $rules;
    }

    protected function prepareForValidation()
    {
        if ($serviceId = $this->route('serviceId')) {
            $this->merge(['serviceId' => $serviceId]);
        }
    }

    public function toDTO(): VehicleServiceDTO
    {
        $validated = $this->validated();
        $vehicleId = $this->route('vehicleId');
        $currentLanguage = app()->getLocale();

        $detailedReview = null;
        if (! empty($validated['review'])) {
            $detailedReview = [
                $currentLanguage => $validated['review'],
            ];
        }

        return new VehicleServiceDTO(
            vehicle_id: $vehicleId,
            service_type: $validated['service_type'],
            service_date: $validated['service_date'],
            maintenance_time: $validated['maintenance_time'],
            mileage: $validated['mileage'],
            cost: $validated['cost'],
            review: $detailedReview
        );
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'service_type.required' => 'نوع الخدمة مطلوب.',
                'service_date.required' => 'تاريخ الخدمة مطلوب.',
                'maintenance_time.required' => 'وقت الصيانة مطلوب.',
                'mileage.required' => 'عدد الكيلومترات مطلوب.',
                'cost.required' => 'التكلفة مطلوبة.',
                'review.string' => 'يجب أن تكون المراجعة نصية.',
                'serviceId.exists' => 'الخدمة ID غير موجود.',
            ];
        }

        return [
            'service_type.required' => 'The service type is required.',
            'service_date.required' => 'The service date is required.',
            'maintenance_time.required' => 'The maintenance time is required.',
            'mileage.required' => 'The mileage is required.',
            'cost.required' => 'The cost is required.',
            'review.string' => 'The review must be a string.',
            'serviceId.exists' => 'The service ID does not exist.',
        ];
    }
}
