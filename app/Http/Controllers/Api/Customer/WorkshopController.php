<?php

namespace App\Http\Controllers\Api\Customer;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Customer\WorkshopBookmarkRequest;
use App\Http\Resources\Vendor\WorkshopResource;
use App\Models\Workshop;
use App\Services\WorkshopBookmarkService;

class WorkshopController extends Controller
{
    protected $bookmarkService;

    public function __construct(WorkshopBookmarkService $bookmarkService)
    {
        $this->bookmarkService = $bookmarkService;
    }

    /**
     * List all active and approved workshops
     */
    public function index()
    {
        $workshops = Workshop::with(['services', 'workingHours', 'user'])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->get();

        return ApiResponse::success([
            'workshops' => WorkshopResource::collection($workshops),
        ], 'Workshops retrieved successfully');
    }

    /**
     * Get a specific workshop details
     */
    public function show(Workshop $workshop)
    {
        // Only allow viewing active and approved workshops
        if (! $workshop->is_active || ! $workshop->is_approved) {
            return ApiResponse::error('Workshop not found', 404);
        }

        // Load relationships
        $workshop->load(['services', 'workingHours', 'user']);

        return ApiResponse::success(
            new WorkshopResource($workshop),
            'Workshop retrieved successfully'
        );
    }

    public function toggleBookmark(WorkshopBookmarkRequest $request)
    {
        $user = $request->user();
        $workshopId = $request->workshop_id;
        $isBookmarked = $request->is_bookmarked;

        $result = $this->bookmarkService->toggleBookmark($user, $workshopId, $isBookmarked);

        $message = $result ? 'Workshop bookmarked successfully' : 'Workshop bookmark removed successfully';

        return ApiResponse::success([], $message);
    }

    public function bookmarks()
    {
        $user = request()->user();
        $bookmarkedWorkshops = $this->bookmarkService->getBookmarkedWorkshops($user);

        return ApiResponse::success([
            'workshops' => WorkshopResource::collection($bookmarkedWorkshops),
        ], 'Bookmarked workshops retrieved successfully');
    }

    public function incrementCount($workshopId, $countType)
    {
        // Validate count type
        if (! in_array($countType, ['show_count', 'location_count'])) {
            return ApiResponse::error('Invalid count type. Must be either show_count or location_count', 400);
        }

        // Find the workshop
        $workshop = Workshop::where('id', $workshopId)
            ->where('is_active', true)
            ->where('is_approved', true)
            ->first();

        if (! $workshop) {
            return ApiResponse::error('Workshop not found', 404);
        }

        // Increment the specified count
        $workshop->increment($countType);

        // Return updated workshop data
        $workshop->load(['services', 'workingHours', 'user']);

        return ApiResponse::success(
            new WorkshopResource($workshop),
            ucfirst(str_replace('_', ' ', $countType)).' incremented successfully'
        );
    }
}
