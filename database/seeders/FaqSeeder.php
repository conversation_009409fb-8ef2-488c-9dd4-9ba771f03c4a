<?php

namespace Database\Seeders;

use App\Models\Faq;
use App\Models\FaqTranslation;
use Illuminate\Database\Seeder;

class FaqSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create customer FAQs
        $this->createCustomerFaqs();

        // Create vendor FAQs
        $this->createVendorFaqs();
    }

    /**
     * Create FAQs for customers
     */
    private function createCustomerFaqs(): void
    {
        // FAQ 1
        $faq1 = Faq::create([
            'user_type' => 'customer',
            'order' => 1,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq1->id,
            'locale' => 'en',
            'question' => 'How do I book a service for my vehicle?',
            'answer' => 'You can book a service by selecting the desired service category from the home screen, choosing a vendor, and following the booking process. You\'ll need to specify your vehicle details and preferred appointment time.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq1->id,
            'locale' => 'ar',
            'question' => 'كيف يمكنني حجز خدمة لمركبتي؟',
            'answer' => 'يمكنك حجز خدمة عن طريق اختيار فئة الخدمة المطلوبة من الشاشة الرئيسية، واختيار مزود الخدمة، واتباع عملية الحجز. ستحتاج إلى تحديد تفاصيل مركبتك والوقت المفضل للموعد.',
        ]);

        // FAQ 2
        $faq2 = Faq::create([
            'user_type' => 'customer',
            'order' => 2,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq2->id,
            'locale' => 'en',
            'question' => 'How do I track my service request?',
            'answer' => 'You can track your service request through the "My Bookings" section in the app. There you\'ll find the status of your request, including pending, in progress, and completed services.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq2->id,
            'locale' => 'ar',
            'question' => 'كيف يمكنني تتبع طلب الخدمة الخاص بي؟',
            'answer' => 'يمكنك تتبع طلب الخدمة من خلال قسم "حجوزاتي" في التطبيق. ستجد هناك حالة طلبك، بما في ذلك الخدمات المعلقة وقيد التنفيذ والمكتملة.',
        ]);

        // FAQ 3
        $faq3 = Faq::create([
            'user_type' => 'customer',
            'order' => 3,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq3->id,
            'locale' => 'en',
            'question' => 'How do I pay for services?',
            'answer' => 'Nabih offers multiple payment options including credit/debit cards and digital wallets. You can pay when booking a service or after the service is completed, depending on the vendor\'s requirements.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq3->id,
            'locale' => 'ar',
            'question' => 'كيف يمكنني الدفع مقابل الخدمات؟',
            'answer' => 'تقدم نبيه خيارات دفع متعددة بما في ذلك بطاقات الائتمان/الخصم والمحافظ الرقمية. يمكنك الدفع عند حجز الخدمة أو بعد اكتمال الخدمة، اعتماداً على متطلبات مقدم الخدمة.',
        ]);
    }

    /**
     * Create FAQs for vendors
     */
    private function createVendorFaqs(): void
    {
        // FAQ 1
        $faq1 = Faq::create([
            'user_type' => 'vendor',
            'order' => 1,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq1->id,
            'locale' => 'en',
            'question' => 'How do I register as a service provider?',
            'answer' => 'To register as a service provider, download the Nabih app, select "Register as Vendor", and follow the verification process. You\'ll need to provide your business details, service categories, and required documentation.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq1->id,
            'locale' => 'ar',
            'question' => 'كيف يمكنني التسجيل كمزود خدمة؟',
            'answer' => 'للتسجيل كمزود خدمة، قم بتنزيل تطبيق نبيه، واختر "التسجيل كمزود خدمة"، واتبع عملية التحقق. ستحتاج إلى تقديم تفاصيل عملك، وفئات الخدمة، والوثائق المطلوبة.',
        ]);

        // FAQ 2
        $faq2 = Faq::create([
            'user_type' => 'vendor',
            'order' => 2,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq2->id,
            'locale' => 'en',
            'question' => 'How do I manage my service bookings?',
            'answer' => 'You can manage all service bookings through the "Bookings" dashboard in your vendor account. Here you can view, accept or reject booking requests, and update the status of ongoing services.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq2->id,
            'locale' => 'ar',
            'question' => 'كيف يمكنني إدارة حجوزات الخدمة الخاصة بي؟',
            'answer' => 'يمكنك إدارة جميع حجوزات الخدمة من خلال لوحة "الحجوزات" في حساب المزود الخاص بك. هنا يمكنك عرض وقبول أو رفض طلبات الحجز وتحديث حالة الخدمات الجارية.',
        ]);

        // FAQ 3
        $faq3 = Faq::create([
            'user_type' => 'vendor',
            'order' => 3,
            'is_active' => true,
        ]);

        // English
        FaqTranslation::create([
            'faq_id' => $faq3->id,
            'locale' => 'en',
            'question' => 'How and when do I receive payments?',
            'answer' => 'Payments are processed after the service is marked as completed and the customer confirms satisfaction. The funds will be transferred to your registered bank account within 2-3 business days, minus the platform fee.',
        ]);

        // Arabic
        FaqTranslation::create([
            'faq_id' => $faq3->id,
            'locale' => 'ar',
            'question' => 'كيف ومتى أتلقى المدفوعات؟',
            'answer' => 'تتم معالجة المدفوعات بعد وضع علامة "مكتملة" على الخدمة وتأكيد رضا العميل. سيتم تحويل الأموال إلى حسابك المصرفي المسجل في غضون 2-3 أيام عمل، ناقص رسوم المنصة.',
        ]);
    }
}
