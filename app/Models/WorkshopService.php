<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class WorkshopService extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // WorkshopService model is a standalone entity
        });
    }

    protected $fillable = [
        'workshop_id',
        'name',
    ];

    public function workshop(): BelongsTo
    {
        return $this->belongsTo(Workshop::class);
    }
}
