<?php

namespace App\DTOs\Workshop;

use App\DTOs\BaseDTO;
use App\Http\Requests\Vendor\WorkshopServiceRequest;

class WorkshopServiceDTO extends BaseDTO
{
    public function __construct(
        public readonly int $workshop_id,
        public readonly string $name
    ) {}

    /**
     * Create a DTO from a request
     */
    public static function fromRequest(WorkshopServiceRequest $request, int $workshop_id): self
    {
        $data = $request->validated();

        return new self(
            workshop_id: $workshop_id,
            name: $data['name'] ?? $data['services'][0] ?? ''
        );
    }

    /**
     * Create a collection of DTOs from a request
     */
    public static function collectionFromRequest(WorkshopServiceRequest $request, int $workshop_id): array
    {
        $serviceDTOs = [];
        $data = $request->validated();

        foreach ($data['services'] as $serviceName) {
            $serviceDTOs[] = new self(
                workshop_id: $workshop_id,
                name: $serviceName
            );
        }

        return $serviceDTOs;
    }
}
