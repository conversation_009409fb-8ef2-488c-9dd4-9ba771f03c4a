<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class AssignRolesRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('role.assign');
    }

    public function rules(): array
    {
        return [
            'roles' => 'required|array',
            'roles.*' => [
                'exists:roles,name',
                'not_in:super-admin',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'roles.*.not_in' => 'The super-admin role cannot be assigned to users. It is a system reserved role.',
        ];
    }

    public function attributes(): array
    {
        return [
            'roles.*' => 'role',
        ];
    }
}
