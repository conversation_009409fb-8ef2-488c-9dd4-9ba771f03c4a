<?php

namespace App\Http\Controllers\Api;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\MobileOfferResource;
use App\Services\OfferService;
use Exception;
use Illuminate\Http\JsonResponse;

class OfferController extends Controller
{
    protected OfferService $offerService;

    public function __construct(OfferService $offerService)
    {
        $this->offerService = $offerService;
    }

    public function getActiveOffers(): JsonResponse
    {
        try {
            $offers = $this->offerService->getActiveOffers();

            return ApiResponse::success([
                'offers' => MobileOfferResource::collection($offers),
            ], 'Offers retrieved successfully');

        } catch (\Illuminate\Database\QueryException $e) {
            if (strpos($e->getMessage(), 'Base table or view not found') !== false) {
                return ApiResponse::success([
                    'offers' => [],
                ], 'No offers available');
            }

            return ApiResponse::errorWithLog('Database error while retrieving offers', $e);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve offers', $e);
        }
    }
}
