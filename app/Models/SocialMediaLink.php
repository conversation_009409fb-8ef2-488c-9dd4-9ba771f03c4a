<?php

namespace App\Models;

use App\Enums\SocialMediaType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class SocialMediaLink extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // SocialMediaLink model is a standalone entity
        });
    }

    protected $fillable = [
        'type',
        'title',
        'url',
        'username',
        'description',
        'is_active',
    ];

    protected $casts = [
        'type' => SocialMediaType::class,
        'is_active' => 'boolean',
    ];

    public function getIconAttribute(): string
    {
        return SocialMediaType::getIcon($this->type->value);
    }

    public static function getActive()
    {
        return self::where('is_active', true)
            ->orderBy('created_at')
            ->get();
    }
}
