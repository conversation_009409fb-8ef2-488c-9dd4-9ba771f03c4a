<?php

namespace App\Services;

use App\Models\User;
use App\Models\Workshop;

class WorkshopBookmarkService
{
    public function toggleBookmark(User $user, int $workshopId, bool $isBookmarked): bool
    {
        $workshop = Workshop::findOrFail($workshopId);

        if ($isBookmarked) {
            // Add bookmark if it doesn't exist
            if (! $user->bookmarkedWorkshops()->where('workshop_id', $workshopId)->exists()) {
                $user->bookmarkedWorkshops()->attach($workshopId);
            }

            return true;
        } else {
            // Remove bookmark if it exists
            $user->bookmarkedWorkshops()->detach($workshopId);

            return false;
        }
    }

    public function getBookmarkedWorkshops(User $user)
    {
        return $user->bookmarkedWorkshops()
            ->with(['services', 'workingHours', 'user'])
            ->where('is_active', true)
            ->where('is_approved', true)
            ->get();
    }
}
