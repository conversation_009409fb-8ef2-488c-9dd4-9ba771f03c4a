<?php

namespace Database\Seeders;

use App\Enums\UserType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class SuperAdminSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create super-admin role if it doesn't exist
        $superAdminRole = Role::firstOrCreate(['name' => 'super-admin']);

        // Create all permissions if they don't exist
        $this->createPermissions();

        // Give super-admin all permissions
        $allPermissions = Permission::all();
        $superAdminRole->syncPermissions($allPermissions);

        // Ensure only one super admin exists in the system
        $this->ensureOnlySuperAdminExists($superAdminRole);
    }

    /**
     * Create all necessary permissions for the system
     */
    private function createPermissions(): void
    {
        // Admin management permissions
        $adminPermissions = [
            'admin.view.list',           // View list of admins
            'admin.view.single',         // View single admin details
            'admin.view.deleted',        // View deleted admins
            'admin.view.profile',        // View current admin profile
            'admin.create',              // Create new admin
            'admin.update',              // Update admin details
            'admin.delete',              // Soft delete admin
            'admin.delete.permanent',    // Permanently delete admin
            'admin.restore',             // Restore deleted admin
        ];

        // Support Request management permissions
        $supportRequestPermissions = [
            'support.view.list',         // View list of support requests
            'support.view.single',       // View single support request details
            'support.view.deleted',      // View deleted support requests
            'support.create',            // Create new support request
            'support.update',            // Update support request details
            'support.delete',            // Soft delete support request
            'support.delete.permanent',  // Permanently delete support request
            'support.restore',           // Restore deleted support request
        ];

        // Vendor management permissions
        $vendorPermissions = [
            'vendor.view.list',          // View list of vendors
            'vendor.view.single',        // View single vendor details
            'vendor.view.deleted',       // View deleted vendors
            'vendor.view.unapproved',    // View unapproved vendors
            'vendor.view.approved',      // View approved vendors
            'vendor.create',             // Create new vendor
            'vendor.update',             // Update vendor details
            'vendor.delete',             // Soft delete vendor
            'vendor.delete.permanent',   // Permanently delete vendor
            'vendor.restore',            // Restore deleted vendor
            'vendor.approve',            // Approve vendor
            'vendor.disapprove',         // Disapprove vendor
        ];

        // Customer management permissions
        $customerPermissions = [
            'customer.view.list',        // View list of customers
            'customer.view.single',      // View single customer details
            'customer.view.deleted',     // View deleted customers
            'customer.create',           // Create new customer
            'customer.update',           // Update customer details
            'customer.delete',           // Soft delete customer
            'customer.delete.permanent', // Permanently delete customer
            'customer.restore',          // Restore deleted customer
        ];

        // Role management permissions
        $rolePermissions = [
            'role.view.list',            // View list of roles
            'role.view.single',          // View single role details
            'role.create',               // Create new role
            'role.update',               // Update role details
            'role.delete',               // Delete role
            'role.assign',               // Assign role to user
            'role.revoke',               // Revoke role from user
        ];

        // Permission management permissions
        $permissionPermissions = [
            'permission.view.list',      // View list of permissions
            'permission.view.single',    // View single permission details
            'permission.create',         // Create new permission
            'permission.update',         // Update permission details
            'permission.delete',         // Delete permission
            'permission.assign',         // Assign permission to role/user
            'permission.revoke',         // Revoke permission from role/user
        ];

        // Vehicle management permissions
        $vehiclePermissions = [
            'vehicle.view.list',         // View list of vehicles
            'vehicle.view.single',       // View single vehicle details
            'vehicle.view.deleted',      // View deleted vehicles
            'vehicle.view.byuser',       // View vehicles by user
            'vehicle.create',            // Create new vehicle
            'vehicle.update',            // Update vehicle details
            'vehicle.delete',            // Soft delete vehicle
            'vehicle.delete.permanent',  // Permanently delete vehicle
            'vehicle.restore',           // Restore deleted vehicle
        ];

        // System management permissions
        $systemPermissions = [
            'system.settings.view',      // View system settings
            'system.settings.update',    // Update system settings
            'system.logs.view',          // View system logs
        ];

        // Dashboard permissions
        $dashboardPermissions = [
            'dashboard.view.statistics',  // View dashboard statistics
            'dashboard.view.maintenance-logs',      // View dashboard maintenance logs
        ];

        // Content management permissions
        $contentPermissions = [
            // Onboarding Screens
            'content.onboarding.view.list',    // View list of onboarding screens
            'content.onboarding.view.single',  // View single onboarding screen
            'content.onboarding.create',       // Create onboarding screen
            'content.onboarding.update',       // Update onboarding screen
            'content.onboarding.delete',       // Delete onboarding screen
            'content.onboarding.reorder',      // Reorder onboarding screens

            // FAQs
            'content.faq.view.list',           // View list of FAQs
            'content.faq.view.single',         // View single FAQ
            'content.faq.view.customer',       // View customer FAQs
            'content.faq.view.vendor',         // View vendor FAQs
            'content.faq.create',              // Create FAQ
            'content.faq.update',              // Update FAQ
            'content.faq.delete',              // Delete FAQ
            'content.faq.reorder',             // Reorder FAQs

            // Static Content
            'content.static.view.list',         // View list of static content
            'content.static.view.single',       // View single static content
            'content.static.view.bytype',       // View static content by type
            'content.static.create',            // Create static content
            'content.static.update',            // Update static content
            'content.static.delete',            // Delete static content

            // Social Media Links
            'content.social-media.view.list',    // View list of social media links
            'content.social-media.view.single',  // View single social media link
            'content.social-media.manage',       // Create, update, delete social media links
        ];

        $permissions = array_merge(
            $adminPermissions,
            $supportRequestPermissions,
            $vendorPermissions,
            $customerPermissions,
            $rolePermissions,
            $permissionPermissions,
            $vehiclePermissions,
            $systemPermissions,
            $dashboardPermissions,
            $contentPermissions
        );

        // Create each permission if it doesn't exist
        foreach ($permissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }
    }

    /**
     * Ensure only one super admin exists in the system
     */
    private function ensureOnlySuperAdminExists(Role $superAdminRole): void
    {
        // Check if any users already have the super-admin role
        $existingSuperAdmins = User::role('super-admin')->get();

        if ($existingSuperAdmins->count() > 1) {
            // If multiple super admins exist, keep only the first one and remove the role from others
            $firstSuperAdmin = $existingSuperAdmins->first();
            $otherSuperAdmins = $existingSuperAdmins->skip(1);

            foreach ($otherSuperAdmins as $admin) {
                $admin->removeRole('super-admin');
                $this->command->warn("Removed super-admin role from user: {$admin->email}");
            }

            $this->command->info("Ensured only one super admin exists: {$firstSuperAdmin->email}");

            return;
        }

        if ($existingSuperAdmins->count() === 1) {
            $this->command->info('Super Admin user already exists. System is properly configured.');

            return;
        }

        // No super admin exists, create one
        $superAdmin = User::where('email', config('app.nabih.super_admin_email', '<EMAIL>'))->first();

        if (! $superAdmin) {
            $superAdmin = User::create([
                'first_name' => 'Super',
                'last_name' => 'Admin',
                'email' => config('app.nabih.super_admin_email', '<EMAIL>'),
                'phone_number' => '+123456789012',
                'password' => Hash::make('SuperAdmin@123'), // Change this in production!
                'user_type' => UserType::ADMIN->value,
                'is_verified' => true,
            ]);

            $this->command->info('Super Admin user created successfully.');
        }

        // Assign the super-admin role
        $superAdmin->assignRole($superAdminRole);
        $this->command->info("Super-admin role assigned to: {$superAdmin->email}");
    }
}
