<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'sometimes|array',
            'title.en' => 'required_with:title|string|max:255',
            'title.ar' => 'required_with:title|string|max:255',
            'description' => 'sometimes|array',
            'description.en' => 'required_with:description|string',
            'description.ar' => 'required_with:description|string',
            'image' => 'sometimes|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'discount_text' => 'sometimes|array',
            'discount_text.en' => 'required_with:discount_text|string|max:255',
            'discount_text.ar' => 'required_with:discount_text|string|max:255',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date_format:Y-m-d H:i:s',
            'end_date' => 'nullable|date_format:Y-m-d H:i:s|after_or_equal:start_date',
            'button_text' => 'sometimes|array',
            'button_text.en' => 'required_with:button_text|string|max:255',
            'button_text.ar' => 'required_with:button_text|string|max:255',
            'button_link' => 'nullable|string|max:255',
            'is_limited_time' => 'boolean',
        ];
    }
}
