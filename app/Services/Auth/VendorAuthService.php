<?php

namespace App\Services\Auth;

use App\Enums\UserType;
use App\Models\Otp;
use App\Models\User;
use App\Services\NotificationService;
use Exception;
use Illuminate\Support\Facades\Hash;

class VendorAuthService
{
    protected OtpService $otpService;

    protected NotificationService $notificationService;

    public function __construct(OtpService $otpService, NotificationService $notificationService)
    {
        $this->otpService = $otpService;
        $this->notificationService = $notificationService;
    }

    public function sendOtp(string $phoneNumber)
    {
        // Check if there's a soft deleted user with this phone number and restore if found
        $this->restoreUserIfDeleted(['phone_number' => $phoneNumber]);

        return $this->otpService->generateOtp($phoneNumber, 'phone');
    }

    public function sendEmailOtp(string $email)
    {
        // Check if there's a soft deleted user with this email and restore if found
        $this->restoreUserIfDeleted(['email' => $email]);

        return $this->otpService->generateOtp($email, 'email');
    }

    public function resendOtp(string $phoneNumber)
    {
        return $this->otpService->resendOtp($phoneNumber, 'phone');
    }

    public function resendEmailOtp(string $email)
    {
        return $this->otpService->resendOtp($email, 'email');
    }

    public function createUnVerifiedUser(string $firstName, string $lastName, string $phoneNumber, string $email)
    {
        // First check if there's a soft deleted user with this phone number or email
        $existingUser = User::withTrashed()
            ->where(function ($query) use ($phoneNumber, $email) {
                $query->where('phone_number', $phoneNumber)
                    ->orWhere('email', $email);
            })
            ->first();

        if ($existingUser) {
            if ($existingUser->trashed()) {
                // Restore the user
                $existingUser->restore();
                $existingUser->update([
                    'name' => $firstName.' '.$lastName,
                    'first_name' => $firstName,
                    'last_name' => $lastName,
                    'phone_number' => $phoneNumber,
                    'email' => $email,
                    'is_verified' => false,
                    'is_vendor_account_approved' => false,
                ]);

                return $existingUser;
            }
        } else {
            // Create new user if no existing user (including soft deleted) found
            $newVendor = User::create([
                'name' => $firstName.' '.$lastName,
                'first_name' => $firstName,
                'last_name' => $lastName,
                'phone_number' => $phoneNumber,
                'email' => $email,
                'is_verified' => false,
                'user_type' => UserType::VENDOR,
                'is_vendor_account_approved' => false, // Vendors need approval
            ]);

            // Trigger admin notification for new vendor registration
            $this->notificationService->createVendorRegistrationNotification($newVendor);

            return $newVendor;
        }
    }

    public function processPhoneOtpVerification(string $phoneNumber)
    {
        // Check if there's a soft deleted user with this phone number and restore if found
        $this->restoreUserIfDeleted(['phone_number' => $phoneNumber]);

        $user = User::where('phone_number', $phoneNumber)->first();
        $user->is_verified = true;
        $user->save();

        // Trigger admin notification for vendor approval request (only for vendors)
        if ($user->user_type === UserType::VENDOR && ! $user->is_vendor_account_approved) {
            $this->notificationService->createVendorApprovalRequestNotification($user);
        }

        // Delete OTP after successful verification
        Otp::where('phone_number', $phoneNumber)
            ->delete();

        return [
            'user' => $user,
            'token' => $this->createToken($user),
        ];
    }

    public function processEmailOtpVerification(string $email)
    {
        // Check if there's a soft deleted user with this email and restore if found
        $this->restoreUserIfDeleted(['email' => $email]);

        $user = User::where('email', $email)->first();
        $user->is_verified = true;
        $user->save();

        // Trigger admin notification for vendor approval request (only for vendors)
        if ($user->user_type === UserType::VENDOR && ! $user->is_vendor_account_approved) {
            $this->notificationService->createVendorApprovalRequestNotification($user);
        }

        // Delete OTP after successful verification
        Otp::where('identifier', $email)
            ->where('type', 'email')
            ->delete();

        return [
            'user' => $user,
            'token' => $this->createToken($user),
        ];
    }

    private function createToken(User $user): string
    {
        $user->tokens()->delete();

        return $user->createToken('auth_token')->plainTextToken;
    }

    /**
     * Helper method to restore a soft-deleted user if one exists with the given criteria
     *
     * @param  array  $criteria  The criteria to search for the user (e.g., ['phone_number' => $phoneNumber])
     * @return User|null The restored user or null if no deleted user was found
     */
    private function restoreUserIfDeleted(array $criteria): ?User
    {
        // Find soft-deleted user by criteria
        $deletedUser = User::onlyTrashed()
            ->where($criteria)
            ->first();

        if ($deletedUser) {
            // Restore the user if found
            $deletedUser->restore();

            return $deletedUser;
        }

        return null;
    }

    public function logout(User $user): bool
    {
        $user->tokens()->delete();

        return true;
    }

    public function loginWithEmail(string $email, string $password): string
    {
        // Check if there's a soft deleted user with this email and restore if found
        $this->restoreUserIfDeleted(['email' => $email, 'user_type' => UserType::VENDOR]);

        $vendor = User::where('email', $email)
            ->where('user_type', UserType::VENDOR)
            ->first();

        if (! $vendor || ! Hash::check($password, $vendor->password)) {
            throw new Exception('Invalid vendor credentials');
        }

        // Check if vendor is approved
        if (! $vendor->is_vendor_account_approved) {
            throw new Exception('Your vendor account is pending approval by an administrator');
        }

        return $this->createToken($vendor);
    }
}
