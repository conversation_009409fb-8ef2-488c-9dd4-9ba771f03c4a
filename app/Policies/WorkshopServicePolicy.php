<?php

namespace App\Policies;

use App\Models\User;
use App\Models\WorkshopService;
use Illuminate\Auth\Access\HandlesAuthorization;

class WorkshopServicePolicy
{
    use HandlesAuthorization;

    /**
     * Determine whether the user can update the workshop service.
     */
    public function update(User $user, WorkshopService $workshopService)
    {
        return $user->isVendor() && $workshopService->workshop && $workshopService->workshop->user_id === $user->id;
    }

    /**
     * Determine whether the user can delete the workshop service.
     */
    public function delete(User $user, WorkshopService $workshopService)
    {
        return $user->isVendor() && $workshopService->workshop && $workshopService->workshop->user_id === $user->id;
    }
}
