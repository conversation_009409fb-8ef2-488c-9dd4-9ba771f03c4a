<?php

namespace App\Services;

use App\Models\UserFcmToken;
use Illuminate\Support\Facades\Log;

class FcmTokenService
{
    public function addOrUpdateToken(
        int $userId,
        string $fcmToken,
        ?string $deviceType = null,
        ?string $deviceName = null,
    ): UserFcmToken {
        // Use updateOrCreate to handle duplicates atomically
        return UserFcmToken::updateOrCreate(
            [
                'user_id' => $userId,
                'fcm_token' => $fcmToken,
            ],
            [
                'device_type' => $deviceType,
                'device_name' => $deviceName,
                'is_active' => true,
                'last_used_at' => now(),
            ]
        );
    }

    /**
     * Remove FCM token
     */
    public function removeToken(int $userId, string $fcmToken): bool
    {
        $token = UserFcmToken::where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->first();

        if ($token) {
            $token->delete();

            return true;
        }

        return false;
    }

    /**
     * Deactivate FCM token
     */
    public function deactivateToken(int $userId, string $fcmToken): bool
    {
        $token = UserFcmToken::where('user_id', $userId)
            ->where('fcm_token', $fcmToken)
            ->first();

        if ($token) {
            $token->deactivate();

            return true;
        }

        return false;
    }

    /**
     * Get all active tokens for a user
     */
    public function getUserActiveTokens(int $userId): array
    {
        return UserFcmToken::where('user_id', $userId)
            ->active()
            ->pluck('fcm_token')
            ->toArray();
    }

    /**
     * Get all active tokens for multiple users
     */
    public function getMultipleUsersActiveTokens(array $userIds): array
    {
        return UserFcmToken::whereIn('user_id', $userIds)
            ->active()
            ->pluck('fcm_token')
            ->toArray();
    }

    /**
     * Get all active tokens from all users
     */
    public function getAllActiveTokens(): array
    {
        return UserFcmToken::active()
            ->pluck('fcm_token')
            ->toArray();
    }

    /**
     * Clean up inactive tokens (older than X days)
     */
    public function cleanupOldTokens(int $daysOld = 30): int
    {
        $cutoffDate = now()->subDays($daysOld);

        $deletedCount = UserFcmToken::where('last_used_at', '<', $cutoffDate)
            ->orWhere(function ($query) use ($cutoffDate) {
                $query->whereNull('last_used_at')
                    ->where('created_at', '<', $cutoffDate);
            })
            ->delete();

        Log::info("Cleaned up {$deletedCount} old FCM tokens");

        return $deletedCount;
    }

    /**
     * Get tokens count per user
     */
    public function getTokensCountPerUser(): array
    {
        return UserFcmToken::active()
            ->selectRaw('user_id, COUNT(*) as tokens_count')
            ->groupBy('user_id')
            ->get()
            ->pluck('tokens_count', 'user_id')
            ->toArray();
    }

    /**
     * Mark token as used (update last_used_at)
     */
    public function markTokenAsUsed(string $fcmToken): void
    {
        UserFcmToken::where('fcm_token', $fcmToken)
            ->update(['last_used_at' => now()]);
    }

    /**
     * Deactivate all tokens for a user (used when user logs out from all devices)
     */
    public function deactivateAllUserTokens(int $userId): int
    {
        return UserFcmToken::where('user_id', $userId)
            ->update(['is_active' => false]);
    }

    /**
     * Remove duplicate tokens (same token for same user)
     */
    public function removeDuplicateTokens(): int
    {
        $duplicates = UserFcmToken::selectRaw('user_id, fcm_token, MIN(id) as keep_id')
            ->groupBy('user_id', 'fcm_token')
            ->havingRaw('COUNT(*) > 1')
            ->get();

        $deletedCount = 0;

        foreach ($duplicates as $duplicate) {
            $deletedCount += UserFcmToken::where('user_id', $duplicate->user_id)
                ->where('fcm_token', $duplicate->fcm_token)
                ->where('id', '!=', $duplicate->keep_id)
                ->delete();
        }

        return $deletedCount;
    }
}
