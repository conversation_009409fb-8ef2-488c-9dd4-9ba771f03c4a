<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UpdateSupportRequestRequest;
use App\Http\Resources\Admin\SupportRequestCollection;
use App\Http\Resources\Admin\SupportRequestResource;
use App\Models\SupportRequest;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class SupportRequestManagementController extends Controller
{
    /**
     * Display a paginated listing of all support requests.
     */
    public function listSupportRequests(Request $request): JsonResponse
    {
        try {
            $query = SupportRequest::with('user')
                ->when($request->issue_type, function ($q) use ($request) {
                    return $q->where('issue_type', $request->issue_type);
                })
                ->when($request->status, function ($q) use ($request) {
                    return $q->where('status', $request->status);
                })
                ->when($request->sort_field, function ($q) use ($request) {
                    return $q->orderBy(
                        $request->sort_field,
                        $request->sort_direction ?? 'asc'
                    );
                }, function ($q) {
                    return $q->orderBy('created_at', 'desc');
                });

            $supportRequests = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new SupportRequestCollection($supportRequests),
                'Support requests retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve support requests', $e);
        }
    }

    /**
     * Display a listing of deleted support requests.
     */
    public function listDeletedSupportRequests(Request $request): JsonResponse
    {
        try {
            $query = SupportRequest::onlyTrashed()->with('user')
                ->when($request->search, function ($q) use ($request) {
                    return $q->where(function ($subQuery) use ($request) {
                        $subQuery->where('request_id', 'LIKE', "%{$request->search}%")
                            ->orWhere('subject', 'LIKE', "%{$request->search}%");
                    });
                })
                ->when($request->sort_field, function ($q) use ($request) {
                    return $q->orderBy(
                        $request->sort_field,
                        $request->sort_direction ?? 'asc'
                    );
                }, function ($q) {
                    return $q->orderBy('deleted_at', 'desc');
                });

            $supportRequests = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new SupportRequestCollection($supportRequests),
                'Deleted support requests retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve deleted support requests', $e);
        }
    }

    /**
     * Display the specified support request.
     */
    public function getSupportRequest(int $id): JsonResponse
    {
        try {
            $supportRequest = SupportRequest::with('user')->findOrFail($id);

            return ApiResponse::success(
                new SupportRequestResource($supportRequest),
                'Support request retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve support request', $e);
        }
    }

    /**
     * Update the specified support request with admin response.
     */
    public function updateSupportRequest(UpdateSupportRequestRequest $request, int $id): JsonResponse
    {
        try {
            $supportRequest = SupportRequest::findOrFail($id);

            $supportRequest->update([
                'admin_response' => $request->admin_response,
                'status' => $request->status,
            ]);

            return ApiResponse::success(
                new SupportRequestResource($supportRequest),
                'Support request updated successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to update support request', $e);
        }
    }

    /**
     * Soft delete the specified support request.
     */
    public function deleteSupportRequest(int $id): JsonResponse
    {
        try {
            $supportRequest = SupportRequest::findOrFail($id);
            $supportRequest->delete();

            return ApiResponse::success(
                [],
                'Support request deleted successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to delete support request', $e);
        }
    }

    /**
     * Restore the specified soft-deleted support request.
     */
    public function restoreSupportRequest(int $id): JsonResponse
    {
        try {
            $supportRequest = SupportRequest::onlyTrashed()->findOrFail($id);
            $supportRequest->restore();

            return ApiResponse::success(
                new SupportRequestResource($supportRequest),
                'Support request restored successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to restore support request', $e);
        }
    }

    /**
     * Permanently delete the specified support request.
     */
    public function permanentlyDeleteSupportRequest(int $id): JsonResponse
    {
        try {
            $supportRequest = SupportRequest::withTrashed()->findOrFail($id);
            $supportRequest->forceDelete();

            return ApiResponse::success(
                [],
                'Support request permanently deleted successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to permanently delete support request', $e);
        }
    }
}
