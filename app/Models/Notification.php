<?php

namespace App\Models;

use App\Enums\NotificationType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Notification extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'title',
        'description',
        'notification_type',
        'notifiable_type',
        'notifiable_id',
    ];

    protected $casts = [
        'title' => 'array',
        'description' => 'array',
        'notification_type' => NotificationType::class,
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // Load relationships to avoid N+1 queries
            $model->load(['readStatus']);

            // Cascade delete to related models
            $model->readStatus->each(function ($item) {
                $item->delete();
            });
        });
    }

    // Relationships
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the model that this notification is related to (polymorphic)
     */
    public function notifiable(): MorphTo
    {
        return $this->morphTo();
    }

    /**
     * Get the read status records for this notification
     */
    public function readStatus(): HasMany
    {
        return $this->hasMany(NotificationReadStatus::class);
    }

    // Scopes
    public function scopeUnread($query)
    {
        return $query->whereHas('readStatus', function ($query) {
            $query->where('is_read', false);
        });
    }

    public function scopeRead($query)
    {
        return $query->whereHas('readStatus', function ($query) {
            $query->where('is_read', true);
        });
    }

    public function scopeByType($query, string $type)
    {
        return $query->where('notification_type', $type);
    }

    public function scopeForUser($query, int $userId)
    {
        return $query->where(function ($q) use ($userId) {
            $q->where('notifications.user_id', $userId) // User-specific notifications
                ->orWhereNull('notifications.user_id'); // Common notifications (for all users)
        });
    }

    public function scopeUserSpecific($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    public function scopeCommon($query)
    {
        return $query->whereNull('user_id');
    }

    // Helper methods
    public function markAsRead(): bool
    {
        return $this->update(['is_read' => true]);
    }

    public function markAsUnread(): bool
    {
        return $this->update(['is_read' => false]);
    }

    public function toggleReadStatus(): bool
    {
        return $this->update(['is_read' => ! $this->is_read]);
    }
}
