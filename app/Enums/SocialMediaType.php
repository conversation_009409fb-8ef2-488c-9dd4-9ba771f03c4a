<?php

namespace App\Enums;

enum SocialMediaType: string
{
    case FACEBOOK = 'facebook';
    case TWITTER = 'twitter';
    case INSTAGRAM = 'instagram';
    case LINKEDIN = 'linkedin';
    case YOUTUBE = 'youtube';
    case TIKTOK = 'tiktok';
    case SNAPCHAT = 'snapchat';
    case WHATSAPP = 'whatsapp';
    case TELEGRAM = 'telegram';
    case PINTEREST = 'pinterest';
    case REDDIT = 'reddit';
    case EMAIL = 'email';
    case PHONE = 'phone';

    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    public static function options(): array
    {
        return [
            ['value' => self::FACEBOOK->value, 'label' => 'Facebook'],
            ['value' => self::TWITTER->value, 'label' => 'Twitter'],
            ['value' => self::INSTAGRAM->value, 'label' => 'Instagram'],
            ['value' => self::LINKEDIN->value, 'label' => 'LinkedIn'],
            ['value' => self::YOUTUBE->value, 'label' => 'YouTube'],
            ['value' => self::TIKTOK->value, 'label' => 'TikTok'],
            ['value' => self::SNAPCHAT->value, 'label' => 'Snapchat'],
            ['value' => self::WHATSAPP->value, 'label' => 'WhatsApp'],
            ['value' => self::TELEGRAM->value, 'label' => 'Telegram'],
            ['value' => self::PINTEREST->value, 'label' => 'Pinterest'],
            ['value' => self::REDDIT->value, 'label' => 'Reddit'],
            ['value' => self::EMAIL->value, 'label' => 'Email'],
            ['value' => self::PHONE->value, 'label' => 'Phone'],
        ];
    }

    public static function getIcon(string $type): string
    {
        return match ($type) {
            self::FACEBOOK->value => 'facebook',
            self::TWITTER->value => 'twitter',
            self::INSTAGRAM->value => 'instagram',
            self::LINKEDIN->value => 'linkedin',
            self::YOUTUBE->value => 'youtube',
            self::TIKTOK->value => 'tiktok',
            self::SNAPCHAT->value => 'snapchat',
            self::WHATSAPP->value => 'whatsapp',
            self::TELEGRAM->value => 'telegram',
            self::PINTEREST->value => 'pinterest',
            self::REDDIT->value => 'reddit',
            self::EMAIL->value => 'email',
            self::PHONE->value => 'phone',
            default => 'link',
        };
    }
}
