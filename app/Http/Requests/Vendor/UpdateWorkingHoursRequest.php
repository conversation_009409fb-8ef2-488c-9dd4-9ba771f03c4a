<?php

namespace App\Http\Requests\Vendor;

use App\DTOs\Workshop\WorkingHourDTO;
use App\Http\Requests\BaseApiRequest;
use App\Models\Workshop;
use Illuminate\Routing\Route;

/**
 * @method mixed user() Get the authenticated user.
 * @method mixed route(?string $param = null, $default = null) Get a route parameter.
 */
class UpdateWorkingHoursRequest extends BaseApiRequest
{
    public function authorize()
    {
        /** @var Workshop|null $workshop */
        $workshop = $this->route('workshop');
        $user = $this->user();

        return $user &&
               $user->isVendor() &&
               $workshop &&
               $workshop->user_id === $user->id;
    }

    public function rules()
    {
        return [
            'working_hours' => 'required|array',
            'working_hours.*.day_of_week' => 'required|string|in:Sunday,Monday,Tuesday,Wednesday,Thursday,Friday,Saturday',
            'working_hours.*.start_time' => 'nullable|date_format:H:i|required_if:working_hours.*.enabled,true',
            'working_hours.*.end_time' => 'nullable|date_format:H:i|required_if:working_hours.*.enabled,true',
            'working_hours.*.enabled' => 'boolean',
        ];
    }

    /**
     * Convert request to collection of DTOs
     */
    public function toDTO(int $workshop_id): array
    {
        return WorkingHourDTO::collectionFromRequest($this, $workshop_id);
    }
}
