<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateOfferRequest;
use App\Http\Requests\Admin\UpdateOfferRequest;
use App\Http\Resources\Admin\OfferCollection;
use App\Http\Resources\Admin\OfferResource;
use App\Models\Offer;
use App\Services\OfferService;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class OfferManagementController extends Controller
{
    protected OfferService $offerService;

    /**
     * Constructor
     */
    public function __construct(OfferService $offerService)
    {
        $this->offerService = $offerService;
    }

    /**
     * Create a new offer
     */
    public function createOffer(CreateOfferRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $offer = $this->offerService->createOffer($request->validated());

            DB::commit();

            return ApiResponse::success([
                'offer' => new OfferResource($offer),
            ], 'Offer created successfully');

        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create offer', $e);
        }
    }

    /**
     * Update an existing offer
     */
    public function updateOffer(UpdateOfferRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $offer = Offer::findOrFail($id);
            $offer = $this->offerService->updateOffer($offer, $request->validated());

            DB::commit();

            return ApiResponse::success([
                'offer' => new OfferResource($offer),
            ], 'Offer updated successfully');

        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update offer', $e);
        }
    }

    /**
     * Delete an offer
     */
    public function deleteOffer(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $offer = Offer::findOrFail($id);
            $this->offerService->deleteOffer($offer);

            DB::commit();

            return ApiResponse::success([], 'Offer deleted successfully');

        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete offer', $e);
        }
    }

    /**
     * List all offers with pagination
     */
    public function listOffers(Request $request): JsonResponse
    {
        try {
            $filters = [
                'search' => $request->search,
                'is_active' => $request->is_active,
                'per_page' => $request->per_page,
            ];

            $offers = $this->offerService->getAllOffers($filters);

            return ApiResponse::success(
                new OfferCollection($offers),
                'Offers retrieved successfully'
            );

        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve offers', $e);
        }
    }

    /**
     * Get a single offer details
     */
    public function getOffer(int $id): JsonResponse
    {
        try {
            $offer = Offer::findOrFail($id);

            return ApiResponse::success([
                'offer' => new OfferResource($offer),
            ], 'Offer details retrieved successfully');

        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve offer details', $e);
        }
    }

    /**
     * Update offer display order
     */
    public function updateDisplayOrder(Request $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $request->validate([
                'orders' => 'required|array',
                'orders.*.id' => 'required|exists:offers,id',
                'orders.*.display_order' => 'required|integer|min:0',
            ]);

            foreach ($request->orders as $item) {
                Offer::where('id', $item['id'])->update(['display_order' => $item['display_order']]);
            }

            DB::commit();

            return ApiResponse::success([], 'Offer display orders updated successfully');

        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update offer display orders', $e);
        }
    }
}
