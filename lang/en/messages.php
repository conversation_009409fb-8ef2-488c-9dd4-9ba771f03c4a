<?php

return [
    // Common API messages
    'Validation failed' => 'Validation failed',

    // Authentication & Authorization
    'Admin login successful' => 'Admin login successful',
    'User Login successful' => 'User Login successful',
    'Logged out successfully' => 'Logged out successfully',
    'Admin logged out successfully' => 'Admin logged out successfully',
    'Vendor logged out successfully' => 'Vendor logged out successfully',
    'O<PERSON> sent successfully' => 'OTP sent successfully',
    'OTP sent successfully for login' => 'OTP sent successfully for login',
    'OTP sent successfully for vendor registration' => 'OTP sent successfully for vendor registration',
    'OTP sent successfully for vendor login' => 'OTP sent successfully for vendor login',
    'O<PERSON> resent successfully' => 'OTP resent successfully',
    'Phone OTP resent successfully' => 'Phone OTP resent successfully',
    'Email OTP resent successfully' => 'Email OTP resent successfully',
    'Failed to login' => 'Failed to login',
    'Failed to send OTP for registration' => 'Failed to send OTP for registration',
    'Failed to send OTP' => 'Failed to send OTP',
    'Failed to send OTP for vendor registration' => 'Failed to send OTP for vendor registration',
    'Failed to send OTP for vendor login' => 'Failed to send OTP for vendor login',
    'Failed to verify OTP' => 'Failed to verify OTP',
    'Failed to verify phone OTP' => 'Failed to verify phone OTP',
    'Failed to verify email OTP' => 'Failed to verify email OTP',
    'Logout failed' => 'Logout failed',
    'Admin logout failed' => 'Admin logout failed',
    'Vendor logout failed' => 'Vendor logout failed',
    'Failed to resend OTP' => 'Failed to resend OTP',
    'Failed to resend phone OTP' => 'Failed to resend phone OTP',
    'Failed to resend email OTP' => 'Failed to resend email OTP',

    // Account Management
    'Your account has been deleted successfully' => 'Your account has been deleted successfully',
    'Failed to delete your account' => 'Failed to delete your account',
    'Super admin accounts cannot be deleted' => 'Super admin accounts cannot be deleted',

    // Profile Management
    'Customer profile retrieved successfully' => 'Customer profile retrieved successfully',
    'Vendor profile retrieved successfully' => 'Vendor profile retrieved successfully',
    'Your profile has been updated successfully' => 'Your profile has been updated successfully',
    'Vendor profile updated successfully' => 'Vendor profile updated successfully',
    'Gender options retrieved successfully' => 'Gender options retrieved successfully',
    'Avatar deleted successfully' => 'Avatar deleted successfully',
    'Avatar uploaded successfully' => 'Avatar uploaded successfully',

    // Vehicle Management
    'Vehicles retrieved successfully' => 'Vehicles retrieved successfully',
    'Vehicle added successfully' => 'Vehicle added successfully',
    'Vehicle updated successfully' => 'Vehicle updated successfully',
    'Vehicle deleted successfully' => 'Vehicle deleted successfully',
    'Vehicle details retrieved successfully' => 'Vehicle details retrieved successfully',
    'Vehicle created successfully' => 'Vehicle created successfully',
    'Vehicle permanently deleted successfully' => 'Vehicle permanently deleted successfully',
    'Vehicle not found' => 'Vehicle not found',
    'You do not have permission to view this vehicle' => 'You do not have permission to view this vehicle',
    'Failed to create vehicle' => 'Failed to create vehicle',
    'Failed to update vehicle' => 'Failed to update vehicle',
    'Failed to delete vehicle' => 'Failed to delete vehicle',

    // Vehicle Services
    'Service added successfully' => 'Service added successfully',
    'Service retrieved successfully' => 'Service retrieved successfully',
    'Service updated successfully' => 'Service updated successfully',
    'Service deleted successfully' => 'Service deleted successfully',
    'Services synchronized successfully' => 'Services synchronized successfully',
    'Service not found' => 'Service not found',
    'You do not have permission to view this service' => 'You do not have permission to view this service',
    'You do not have permission to delete this service' => 'You do not have permission to delete this service',
    'You do not have permission to view these services' => 'You do not have permission to view these services',
    'You are not authorized to delete this service' => 'You are not authorized to delete this service',
    'Failed to create service' => 'Failed to create service',
    'Failed to update service' => 'Failed to update service',
    'Failed to delete service' => 'Failed to delete service',

    // Vehicle Notes
    'Notes retrieved successfully' => 'Notes retrieved successfully',
    'Note added successfully' => 'Note added successfully',
    'Note retrieved successfully' => 'Note retrieved successfully',
    'Note updated successfully' => 'Note updated successfully',
    'Note deleted successfully' => 'Note deleted successfully',
    'Note not found' => 'Note not found',
    'You do not have permission to view this note' => 'You do not have permission to view this note',
    'You do not have permission to view these notes' => 'You do not have permission to view these notes',
    'You do not have permission to delete this note' => 'You do not have permission to delete this note',
    'Failed to create note' => 'Failed to create note',
    'Failed to update note' => 'Failed to update note',
    'Failed to delete note' => 'Failed to delete note',

    // Vehicle Reminders
    'Reminders retrieved successfully' => 'Reminders retrieved successfully',
    'Reminder added successfully' => 'Reminder added successfully',
    'Reminder retrieved successfully' => 'Reminder retrieved successfully',
    'Reminder updated successfully' => 'Reminder updated successfully',
    'Reminder deleted successfully' => 'Reminder deleted successfully',
    'Reminder not found' => 'Reminder not found',
    'You do not have permission to view this reminder' => 'You do not have permission to view this reminder',
    'You do not have permission to view these reminders' => 'You do not have permission to view these reminders',
    'You do not have permission to delete this reminder' => 'You do not have permission to delete this reminder',
    'Failed to create reminder' => 'Failed to create reminder',
    'Failed to update reminder' => 'Failed to update reminder',
    'Failed to delete reminder' => 'Failed to delete reminder',

    // Workshop Management
    'Workshops retrieved successfully' => 'Workshops retrieved successfully',
    'Workshop created successfully' => 'Workshop created successfully',
    'Workshop retrieved successfully' => 'Workshop retrieved successfully',
    'Workshop updated successfully' => 'Workshop updated successfully',
    'Workshop deleted successfully' => 'Workshop deleted successfully',
    'Workshop image uploaded successfully' => 'Workshop image uploaded successfully',
    'Workshop image deleted successfully' => 'Workshop image deleted successfully',
    'Working hours retrieved successfully' => 'Working hours retrieved successfully',
    'Working hours updated successfully' => 'Working hours updated successfully',
    'Services retrieved successfully' => 'Services retrieved successfully',
    'Workshop not found' => 'Workshop not found',
    'Unauthorized access to this workshop' => 'Unauthorized access to this workshop',
    'Failed to create workshop' => 'Failed to create workshop',
    'Failed to update workshop' => 'Failed to update workshop',
    'Failed to delete workshop' => 'Failed to delete workshop',
    'Failed to upload workshop image' => 'Failed to upload workshop image',
    'Failed to delete workshop image' => 'Failed to delete workshop image',
    'Failed to retrieve working hours' => 'Failed to retrieve working hours',
    'Failed to update working hours' => 'Failed to update working hours',

    // Customer Workshops
    'Workshop bookmarked successfully' => 'Workshop bookmarked successfully',
    'Workshop bookmark removed successfully' => 'Workshop bookmark removed successfully',
    'Bookmarked workshops retrieved successfully' => 'Bookmarked workshops retrieved successfully',
    'Failed to toggle bookmark' => 'Failed to toggle bookmark',
    'Failed to retrieve bookmarked workshops' => 'Failed to retrieve bookmarked workshops',

    // Support Requests
    'Support requests retrieved successfully' => 'Support requests retrieved successfully',
    'Support request created successfully' => 'Support request created successfully',
    'Support request retrieved successfully' => 'Support request retrieved successfully',
    'Support request status options retrieved successfully' => 'Support request status options retrieved successfully',
    'Support request issue type options retrieved successfully' => 'Support request issue type options retrieved successfully',
    'Support request options retrieved successfully' => 'Support request options retrieved successfully',
    'Failed to retrieve support requests' => 'Failed to retrieve support requests',
    'Failed to create support request' => 'Failed to create support request',
    'Failed to retrieve support request' => 'Failed to retrieve support request',

    // Notifications
    'Notifications retrieved successfully' => 'Notifications retrieved successfully',
    'Notification retrieved successfully' => 'Notification retrieved successfully',
    'All notifications marked as read successfully' => 'All notifications marked as read successfully',
    'Notification status toggled successfully' => 'Notification status toggled successfully',
    'Unread count retrieved successfully' => 'Unread count retrieved successfully',
    'Notification not found' => 'Notification not found',
    'Notification not found or does not belong to you' => 'Notification not found or does not belong to you',
    'Failed to toggle notification status' => 'Failed to toggle notification status',
    'Failed to retrieve notifications' => 'Failed to retrieve notifications',
    'Failed to retrieve notification' => 'Failed to retrieve notification',
    'Failed to mark all notifications as read' => 'Failed to mark all notifications as read',
    'Failed to get unread count' => 'Failed to get unread count',

    // Offers
    'Offers retrieved successfully' => 'Offers retrieved successfully',
    'No offers available' => 'No offers available',
    'Offer created successfully' => 'Offer created successfully',
    'Offer updated successfully' => 'Offer updated successfully',
    'Offer deleted successfully' => 'Offer deleted successfully',
    'Offer display orders updated successfully' => 'Offer display orders updated successfully',
    'Failed to retrieve offers' => 'Failed to retrieve offers',
    'Failed to create offer' => 'Failed to create offer',
    'Failed to update offer' => 'Failed to update offer',
    'Failed to delete offer' => 'Failed to delete offer',
    'Failed to update offer display orders' => 'Failed to update offer display orders',

    // Admin Management
    'Admin users retrieved successfully' => 'Admin users retrieved successfully',
    'Admin user created successfully' => 'Admin user created successfully',
    'Admin user updated successfully' => 'Admin user updated successfully',
    'Admin user deleted successfully' => 'Admin user deleted successfully',
    'Admin user permanently deleted successfully' => 'Admin user permanently deleted successfully',
    'Admin user restored successfully' => 'Admin user restored successfully',
    'Failed to create admin user' => 'Failed to create admin user',
    'Failed to update admin user' => 'Failed to update admin user',
    'Failed to delete admin user' => 'Failed to delete admin user',
    'Failed to permanently delete admin user' => 'Failed to permanently delete admin user',
    'Failed to restore admin user' => 'Failed to restore admin user',
    'Failed to retrieve admin users' => 'Failed to retrieve admin users',

    // Vendor Management
    'Vendors retrieved successfully' => 'Vendors retrieved successfully',
    'Vendor user created successfully' => 'Vendor user created successfully',
    'Vendor user updated successfully' => 'Vendor user updated successfully',
    'Vendor user deleted successfully' => 'Vendor user deleted successfully',
    'Vendor user permanently deleted successfully' => 'Vendor user permanently deleted successfully',
    'Vendor user restored successfully' => 'Vendor user restored successfully',
    'Vendor details retrieved successfully' => 'Vendor details retrieved successfully',
    'Unapproved vendors retrieved successfully' => 'Unapproved vendors retrieved successfully',
    'Approved vendors retrieved successfully' => 'Approved vendors retrieved successfully',
    'Vendor approved successfully' => 'Vendor approved successfully',
    'Vendor disapproved successfully' => 'Vendor disapproved successfully',
    'User is not a vendor' => 'User is not a vendor',
    'Failed to create vendor user' => 'Failed to create vendor user',
    'Failed to update vendor user' => 'Failed to update vendor user',
    'Failed to delete vendor user' => 'Failed to delete vendor user',
    'Failed to permanently delete vendor user' => 'Failed to permanently delete vendor user',
    'Failed to restore vendor user' => 'Failed to restore vendor user',
    'Failed to retrieve vendors' => 'Failed to retrieve vendors',
    'Failed to retrieve vendor details' => 'Failed to retrieve vendor details',
    'Failed to retrieve deleted vendor users' => 'Failed to retrieve deleted vendor users',

    // Customer Management
    'Customers retrieved successfully' => 'Customers retrieved successfully',
    'Customer user created successfully' => 'Customer user created successfully',
    'Customer user updated successfully' => 'Customer user updated successfully',
    'Customer user deleted successfully' => 'Customer user deleted successfully',
    'Customer user permanently deleted successfully' => 'Customer user permanently deleted successfully',
    'Customer user restored successfully' => 'Customer user restored successfully',
    'Customer details retrieved successfully' => 'Customer details retrieved successfully',
    'Failed to create customer user' => 'Failed to create customer user',
    'Failed to update customer user' => 'Failed to update customer user',
    'Failed to delete customer user' => 'Failed to delete customer user',
    'Failed to permanently delete customer user' => 'Failed to permanently delete customer user',
    'Failed to restore customer user' => 'Failed to restore customer user',
    'Failed to retrieve customers' => 'Failed to retrieve customers',
    'Failed to retrieve customer details' => 'Failed to retrieve customer details',
    'Failed to retrieve deleted customer users' => 'Failed to retrieve deleted customer users',

    // Role & Permission Management
    'Roles retrieved successfully' => 'Roles retrieved successfully',
    'Role created successfully' => 'Role created successfully',
    'Role updated successfully' => 'Role updated successfully',
    'Role deleted successfully' => 'Role deleted successfully',
    'Role permissions retrieved successfully' => 'Role permissions retrieved successfully',
    'Permissions retrieved successfully' => 'Permissions retrieved successfully',
    'Permission created successfully' => 'Permission created successfully',
    'Permission updated successfully' => 'Permission updated successfully',
    'Permission deleted successfully' => 'Permission deleted successfully',
    'Permission roles retrieved successfully' => 'Permission roles retrieved successfully',
    'Failed to retrieve roles' => 'Failed to retrieve roles',
    'Failed to create role' => 'Failed to create role',
    'Failed to update role' => 'Failed to update role',
    'Failed to delete role' => 'Failed to delete role',
    'Failed to retrieve role permissions' => 'Failed to retrieve role permissions',
    'Failed to retrieve permissions' => 'Failed to retrieve permissions',
    'Failed to create permission' => 'Failed to create permission',
    'Failed to update permission' => 'Failed to update permission',
    'Failed to delete permission' => 'Failed to delete permission',
    'Failed to retrieve permission roles' => 'Failed to retrieve permission roles',

    // User Management
    'Users retrieved successfully' => 'Users retrieved successfully',
    'User created successfully' => 'User created successfully',
    'User updated successfully' => 'User updated successfully',
    'User deleted successfully' => 'User deleted successfully',
    'User details retrieved successfully' => 'User details retrieved successfully',
    'Failed to retrieve users' => 'Failed to retrieve users',
    'Failed to create user' => 'Failed to create user',
    'Failed to update user' => 'Failed to update user',
    'Failed to delete user' => 'Failed to delete user',
    'Failed to retrieve user details' => 'Failed to retrieve user details',

    // FAQ Management
    'FAQs retrieved successfully' => 'FAQs retrieved successfully',
    'FAQ created successfully' => 'FAQ created successfully',
    'FAQ updated successfully' => 'FAQ updated successfully',
    'FAQ deleted successfully' => 'FAQ deleted successfully',
    'FAQs reordered successfully' => 'FAQs reordered successfully',
    'Failed to retrieve FAQs' => 'Failed to retrieve FAQs',
    'Failed to create FAQ' => 'Failed to create FAQ',
    'Failed to retrieve FAQ' => 'Failed to retrieve FAQ',
    'Failed to update FAQ' => 'Failed to update FAQ',
    'Failed to delete FAQ' => 'Failed to delete FAQ',
    'Failed to reorder FAQs' => 'Failed to reorder FAQs',

    // Onboarding Screens
    'Onboarding screens retrieved successfully' => 'Onboarding screens retrieved successfully',
    'Onboarding screen created successfully' => 'Onboarding screen created successfully',
    'Onboarding screen updated successfully' => 'Onboarding screen updated successfully',
    'Onboarding screen deleted successfully' => 'Onboarding screen deleted successfully',
    'Onboarding screens reordered successfully' => 'Onboarding screens reordered successfully',
    'Failed to retrieve onboarding screens' => 'Failed to retrieve onboarding screens',
    'Failed to create onboarding screen' => 'Failed to create onboarding screen',
    'Failed to retrieve onboarding screen' => 'Failed to retrieve onboarding screen',
    'Failed to update onboarding screen' => 'Failed to update onboarding screen',
    'Failed to delete onboarding screen' => 'Failed to delete onboarding screen',
    'Failed to reorder onboarding screens' => 'Failed to reorder onboarding screens',

    // Static Content
    'Static contents retrieved successfully' => 'Static contents retrieved successfully',
    'Static content created/updated successfully' => 'Static content created/updated successfully',
    'Static content retrieved successfully' => 'Static content retrieved successfully',
    'Static content deleted successfully' => 'Static content deleted successfully',
    'Invalid content type' => 'Invalid content type',
    'Invalid user type' => 'Invalid user type',
    'Content not found' => 'Content not found',
    'Failed to retrieve static contents' => 'Failed to retrieve static contents',
    'Failed to create/update static content' => 'Failed to create/update static content',
    'Failed to retrieve static content' => 'Failed to retrieve static content',
    'Failed to delete static content' => 'Failed to delete static content',

    // Generic Error Messages
    'Database error while retrieving offers' => 'Database error while retrieving offers',
    'An error occurred while processing your request' => 'An error occurred while processing your request',
    'Invalid data provided' => 'Invalid data provided',
    'Unauthorized' => 'Unauthorized',
    'Access denied' => 'Access denied',
    'Resource not found' => 'Resource not found',
    'Internal server error' => 'Internal server error',
    'Bad request' => 'Bad request',
    'Forbidden' => 'Forbidden',
    'Method not allowed' => 'Method not allowed',
    'Unprocessable entity' => 'Unprocessable entity',
    'Too many requests' => 'Too many requests',
];
