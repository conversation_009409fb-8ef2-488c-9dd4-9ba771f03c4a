server {
    listen 80;
    index index.php index.html;
    error_log  /var/log/nginx/error.log;
    access_log /var/log/nginx/access.log;
    root /var/www/public;
    
    # Development-specific - more detailed errors
    fastcgi_intercept_errors off;
    
    # Disable caching for development
    add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
    expires off;
    
    # PHP handling with longer timeouts for debugging
    location ~ \.php$ {
        try_files $uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass app:9000;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
        
        # Extended timeout for Xdebug sessions
        fastcgi_read_timeout 600;
        
        # Development environment flag
        fastcgi_param APP_ENV "local";
    }
    
    location / {
        try_files $uri $uri/ /index.php?$query_string;
        
        # For WebSocket connections (hot reloading)
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
    }
    
    # Development-specific assets handling
    location ~* \.(js|css|png|jpg|jpeg|gif|ico)$ {
        expires -1;
        add_header Cache-Control "no-store, no-cache, must-revalidate, proxy-revalidate, max-age=0";
    }
    
    location ~ /\.ht {
        deny all;
    }
}
