<?php

namespace App\Http\Requests\Admin\SocialMediaLink;

use App\Enums\SocialMediaType;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rules\Enum;

class StoreSocialMediaLinkRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Check if user has permission to manage social media links
        return $this->user()->can('content.social-media.manage');
    }

    public function rules(): array
    {
        return [
            'type' => ['required', new Enum(SocialMediaType::class)],
            'title' => ['required', 'string', 'max:255'],
            'url' => ['required', 'url', 'max:255'],
            'username' => ['nullable', 'string', 'max:255'],
            'description' => ['nullable', 'string', 'max:1000'],
            'is_active' => ['boolean'],
        ];
    }

    public function attributes(): array
    {
        return [
            'type' => 'social media type',
            'title' => 'title',
            'url' => 'URL',
            'username' => 'username',
            'description' => 'description',
            'is_active' => 'active status',
        ];
    }
}
