<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\Faq\FaqResource;
use App\Services\FaqService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class FaqController extends Controller
{
    /**
     * @var FaqService
     */
    protected $faqService;

    /**
     * FaqController constructor.
     */
    public function __construct(FaqService $faqService)
    {
        $this->faqService = $faqService;
    }

    /**
     * Get all active FAQs for customers.
     */
    public function customerFaqs(Request $request): AnonymousResourceCollection
    {
        $locale = $request->header('Accept-Language', 'en');
        $faqs = $this->faqService->getAllFaqsByUserType('customer', $locale);

        return FaqResource::collection($faqs);
    }

    /**
     * Get all active FAQs for vendors.
     */
    public function vendorFaqs(Request $request): AnonymousResourceCollection
    {
        $locale = $request->header('Accept-Language', 'en');
        $faqs = $this->faqService->getAllFaqsByUserType('vendor', $locale);

        return FaqResource::collection($faqs);
    }
}
