<?php

namespace App\DTOs;

class VehicleNoteDTO
{
    public function __construct(
        public readonly int $vehicle_id,
        public readonly string $title,
        public readonly string $note_date,
        public readonly ?array $details = null
    ) {}

    public function toArray(): array
    {
        return [
            'vehicle_id' => $this->vehicle_id,
            'title' => $this->title,
            'note_date' => $this->note_date,
            'details' => $this->details,
        ];
    }
}
