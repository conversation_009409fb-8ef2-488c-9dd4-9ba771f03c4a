<?php

namespace App\Http\Requests\Vendor;

use App\Models\Workshop;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class WorkshopImageRequest extends FormRequest
{
    public function authorize(): bool
    {
        // Check if the authenticated user is the owner of the workshop
        if ($this->route('workshop')) {
            $workshop = $this->route('workshop');

            return $workshop->user_id === Auth::id();
        }

        return Auth::check();
    }

    public function rules(): array
    {
        return [
            'image' => ['required', 'image', 'mimes:jpeg,png,jpg', 'max:5120'], // 5MB max size
        ];
    }
}
