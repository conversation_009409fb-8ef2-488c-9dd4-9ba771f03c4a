<?php

namespace App\Http\Requests\Vendor;

use App\Http\Requests\BaseApiRequest;

class VendorProfileUpdateRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->isVendor();
    }

    public function rules(): array
    {
        return [
            'first_name' => 'nullable|string|max:255',
            'last_name' => 'nullable|string|max:255',
            'address' => 'nullable|string',
            'city' => 'nullable|string|max:255',
            'avatar' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
        ];
    }

    public function messages(): array
    {
        if (app()->getLocale() === 'ar') {
            return [
                'first_name.required' => 'الاسم الأول مطلوب',
                'first_name.string' => 'يجب أن يكون الاسم الأول نصًا',
                'first_name.max' => 'يجب ألا يتجاوز الاسم الأول 255 حرفًا',
                'last_name.required' => 'الاسم الأخير مطلوب',
                'last_name.string' => 'يجب أن يكون الاسم الأخير نصًا',
                'last_name.max' => 'يجب ألا يتجاوز الاسم الأخير 255 حرفًا',
                'gender.required' => 'الجنس مطلوب',
                'gender.enum' => 'تم اختيار جنس غير صالح',
                'avatar.image' => 'يجب أن تكون الصورة الرمزية صورة',
                'avatar.mimes' => 'يجب أن تكون الصورة الرمزية بتنسيق jpeg أو png أو jpg أو gif',
                'avatar.max' => 'يجب ألا يتجاوز حجم الصورة الرمزية 2 ميجابايت',
            ];
        }

        return [
            'first_name.required' => 'First name is required',
            'first_name.string' => 'First name must be a string',
            'first_name.max' => 'First name must not exceed 255 characters',
            'last_name.required' => 'Last name is required',
            'last_name.string' => 'Last name must be a string',
            'last_name.max' => 'Last name must not exceed 255 characters',
            'email.required' => 'Email is required',
            'email.email' => 'Please enter a valid email address',
            'email.max' => 'Email must not exceed 255 characters',
            'email.unique' => 'Email is already registered',
            'gender.required' => 'Gender is required',
            'gender.enum' => 'Invalid gender selected',
            'avatar.image' => 'Avatar must be an image',
            'avatar.mimes' => 'Avatar must be a jpeg, png, jpg, or gif file',
            'avatar.max' => 'Avatar size must not exceed 2MB',
        ];
    }
}
