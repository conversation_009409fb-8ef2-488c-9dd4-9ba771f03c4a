<?php

namespace Database\Factories;

use App\Enums\Gender;
use App\Enums\UserType;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\User>
 */
class UserFactory extends Factory
{
    /**
     * The model that the factory creates
     */
    protected $model = User::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'name' => fake()->name(),
            'first_name' => fake()->firstName(),
            'last_name' => fake()->lastName(),
            'phone_number' => '+'.fake()->numberBetween(1, 9).fake()->numerify('##########'),
            'email' => fake()->optional(0.9)->safeEmail(),
            'password' => '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi', // password
            'user_type' => fake()->randomElement(UserType::cases())->value,
            'gender' => fake()->randomElement(Gender::cases()),
            'address' => fake()->optional()->address(),
            'city' => fake()->optional()->city(),
            'is_verified' => fake()->boolean(80), // 80% chance of being verified
            'is_vendor_account_approved' => false,
        ];
    }

    /**
     * Indicate that the user is unverified.
     */
    public function unverified(): static
    {
        return $this->state(fn (array $attributes) => [
            'is_verified' => false,
        ]);
    }

    /**
     * Configure the model as a customer.
     */
    public function customer(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => UserType::CUSTOMER->value,
        ]);
    }

    /**
     * Configure the model as a vendor.
     */
    public function vendor(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => UserType::VENDOR->value,
        ]);
    }

    /**
     * Configure the model as an admin.
     */
    public function admin(): static
    {
        return $this->state(fn (array $attributes) => [
            'user_type' => UserType::ADMIN->value,
        ])->afterCreating(function (User $user) {
            $user->assignRole('admin');
        });
    }
}
