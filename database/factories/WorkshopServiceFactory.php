<?php

namespace Database\Factories;

use App\Models\Workshop;
use App\Models\WorkshopService;
use Illuminate\Database\Eloquent\Factories\Factory;

class WorkshopServiceFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var string
     */
    protected $model = WorkshopService::class;

    /**
     * Define the model's default state.
     *
     * @return array
     */
    public function definition()
    {
        return [
            'workshop_id' => Workshop::factory(), // This will be overridden in the seeder
            'name' => $this->faker->randomElement([
                'Oil Change',
                'Brake Repair',
                'Tire Rotation',
                'Engine Tune-up',
                'AC Service',
                'Battery Replacement',
                'Transmission Service',
                'Wheel Alignment',
                'Suspension Repair',
                'Exhaust System Service',
                'Diagnostic Service',
                'Electrical System Repair',
            ]),
        ];
    }
}
