<?php

namespace App\Http\Resources\Faq;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class FaqResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'user_type' => $this->user_type,
            'order' => $this->order,
            'is_active' => $this->is_active,
            'translations' => $this->translations->mapWithKeys(function ($item) {
                return [$item->locale => [
                    'question' => $item->question,
                    'answer' => $item->answer,
                ]];
            }),
        ];
    }
}
