<?php

namespace Database\Seeders;

use App\Models\Offer;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class OfferSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing offers first
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');
        Offer::truncate();
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');

        // Create 3 featured offers with specific data
        $this->createFeaturedOffers();

        // Create some random offers
        Offer::factory()->count(5)->active()->create();

        // Create some limited time offers
        Offer::factory()->count(3)->active()->limitedTime()->create();

        // Create some inactive offers
        Offer::factory()->count(2)->state(['is_active' => false])->create();
    }

    /**
     * Create featured offers with predefined multilingual content
     */
    private function createFeaturedOffers(): void
    {
        $featuredOffers = [
            [
                'title' => [
                    'en' => 'Summer Service Special',
                    'ar' => 'عروض خدمات الصيف الخاصة',
                ],
                'description' => [
                    'en' => 'Get exclusive discounts on all vehicle maintenance services this summer. Professional service with guaranteed satisfaction.',
                    'ar' => 'احصل على خصومات حصرية على جميع خدمات صيانة المركبات هذا الصيف. خدمة احترافية مع ضمان الرضا.',
                ],
                'discount_text' => [
                    'en' => 'Up to 30% OFF',
                    'ar' => 'خصم يصل إلى ٣٠٪',
                ],
                'button_text' => [
                    'en' => 'Book Service',
                    'ar' => 'احجز الخدمة',
                ],
                'button_link' => '/services/maintenance',
                'is_active' => true,
                'display_order' => 1,
                'is_limited_time' => true,
                'start_date' => now(),
                'end_date' => now()->addMonths(2),
                'image_url' => 'https://picsum.photos/800/400?random=1',
            ],
            [
                'title' => [
                    'en' => 'Premium Car Wash Package',
                    'ar' => 'باقة غسيل السيارات الفاخرة',
                ],
                'description' => [
                    'en' => 'Comprehensive car wash package including interior cleaning, waxing, and detailing. Make your car shine like new!',
                    'ar' => 'باقة شاملة لغسيل السيارات تشمل تنظيف داخلي، تشميع، وتفاصيل كاملة. اجعل سيارتك تلمع كالجديدة!',
                ],
                'discount_text' => [
                    'en' => '25% Discount',
                    'ar' => 'خصم ٢٥٪',
                ],
                'button_text' => [
                    'en' => 'View Details',
                    'ar' => 'عرض التفاصيل',
                ],
                'button_link' => '/services/car-wash',
                'is_active' => true,
                'display_order' => 2,
                'is_limited_time' => false,
                'start_date' => now(),
                'end_date' => null,
                'image_url' => 'https://picsum.photos/800/400?random=2',
            ],
            [
                'title' => [
                    'en' => 'Tire Change Special',
                    'ar' => 'عرض خاص لتغيير الإطارات',
                ],
                'description' => [
                    'en' => 'Professional tire change service with alignment check. Best quality tires from top brands at discounted prices.',
                    'ar' => 'خدمة احترافية لتغيير الإطارات مع فحص للمحاذاة. أفضل إطارات ذات جودة عالية من علامات تجارية رائدة بأسعار مخفضة.',
                ],
                'discount_text' => [
                    'en' => 'Buy 3 Get 1 FREE',
                    'ar' => 'اشترِ ٣ واحصل على ١ مجانًا',
                ],
                'button_text' => [
                    'en' => 'Book Now',
                    'ar' => 'احجز الآن',
                ],
                'button_link' => '/services/tires',
                'is_active' => true,
                'display_order' => 3,
                'is_limited_time' => true,
                'start_date' => now()->subDays(5),
                'end_date' => now()->addDays(25),
                'image_url' => 'https://picsum.photos/800/400?random=3',
            ],
        ];

        foreach ($featuredOffers as $offerData) {
            $imageUrl = $offerData['image_url'];
            unset($offerData['image_url']);

            $offer = Offer::create($offerData);

            try {
                // Add the image to the offer using Spatie Media Library
                $offer->addMediaFromUrl($imageUrl)
                    ->toMediaCollection('offer_image');
            } catch (\Exception $e) {
                \Log::warning("Failed to download image for featured offer #{$offer->id}: ".$e->getMessage());
            }
        }
    }
}
