<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Otp;
use App\Models\User;
use Carbon\Carbon;

class VendorResendOtpRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone_number' => [
                'required',
                'regex:/^\+[1-9]\d{1,14}$/',
                'exists:users,phone_number',
                function ($attribute, $value, $fail) {
                    $existingOtp = Otp::where('phone_number', $value)
                        ->where('expires_at', '>', Carbon::now())
                        ->first();

                    if ($existingOtp && $existingOtp->next_resend_at && $existingOtp->next_resend_at > Carbon::now()) {
                        $remainingSeconds = abs((int) $existingOtp->next_resend_at->diffInSeconds(Carbon::now()));
                        $fail(app()->getLocale() === 'ar'
                            ? "الرجاء الانتظار {$remainingSeconds} ثوانٍ قبل طلب رمز تحقق آخر."
                            : "Please wait {$remainingSeconds} seconds before requesting another OTP.");
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        if (app()->getLocale() === 'ar') {
            return [
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.exists' => 'رقم الهاتف غير موجود',
            ];
        }

        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.exists' => 'Phone number does not exist',
        ];
    }

    protected function afterValidation()
    {
        $user = User::where('phone_number', $this->phone_number)->first();

        if (! $user || $user->user_type !== UserType::VENDOR->value) {
            $message = app()->getLocale() === 'ar'
                ? 'المستخدم غير مسجل كبائع'
                : 'This user is not registered as a vendor';

            $validator = \Illuminate\Support\Facades\Validator::make($this->all(), []);
            $validator->errors()->add('phone_number', $message);
            $this->failedValidation($validator);
        }
    }
}
