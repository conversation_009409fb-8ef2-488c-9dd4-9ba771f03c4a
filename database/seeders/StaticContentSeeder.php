<?php

namespace Database\Seeders;

use App\Models\StaticContent;
use App\Models\StaticContentTranslation;
use Illuminate\Database\Seeder;

class StaticContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create Terms and Conditions for customers
        $this->createStaticContent('terms', 'customer');

        // Create Terms and Conditions for vendors
        $this->createStaticContent('terms', 'vendor');

        // Create Privacy Policy for customers
        $this->createStaticContent('privacy', 'customer');

        // Create Privacy Policy for vendors
        $this->createStaticContent('privacy', 'vendor');

        // Create About Us (applies to both user types)
        $this->createStaticContent('about', 'both');
    }

    /**
     * Create static content with translations
     *
     * @param  string  $type  The content type (terms, privacy, about)
     * @param  string  $userType  The user type (customer, vendor, both)
     */
    private function createStaticContent(string $type, string $userType): void
    {
        $content = StaticContent::create([
            'type' => $type,
            'user_type' => $userType,
            'is_active' => true,
        ]);

        // Create English translation
        StaticContentTranslation::create([
            'static_content_id' => $content->id,
            'locale' => 'en',
            'title' => $this->getTitle($type, $userType, 'en'),
            'content' => $this->getContent($type, $userType, 'en'),
        ]);

        // Create Arabic translation
        StaticContentTranslation::create([
            'static_content_id' => $content->id,
            'locale' => 'ar',
            'title' => $this->getTitle($type, $userType, 'ar'),
            'content' => $this->getContent($type, $userType, 'ar'),
        ]);
    }

    /**
     * Get the title for a static content
     *
     * @param  string  $type  The content type
     * @param  string  $userType  The user type
     * @param  string  $locale  The language locale
     */
    private function getTitle(string $type, string $userType, string $locale): string
    {
        $titles = [
            'en' => [
                'terms' => [
                    'customer' => 'Terms and Conditions for Customers',
                    'vendor' => 'Terms and Conditions for Service Providers',
                    'both' => 'Terms and Conditions',
                ],
                'privacy' => [
                    'customer' => 'Privacy Policy for Customers',
                    'vendor' => 'Privacy Policy for Service Providers',
                    'both' => 'Privacy Policy',
                ],
                'about' => [
                    'both' => 'About Nabih',
                ],
            ],
            'ar' => [
                'terms' => [
                    'customer' => 'الشروط والأحكام للعملاء',
                    'vendor' => 'الشروط والأحكام لمزودي الخدمة',
                    'both' => 'الشروط والأحكام',
                ],
                'privacy' => [
                    'customer' => 'سياسة الخصوصية للعملاء',
                    'vendor' => 'سياسة الخصوصية لمزودي الخدمة',
                    'both' => 'سياسة الخصوصية',
                ],
                'about' => [
                    'both' => 'عن نبيه',
                ],
            ],
        ];

        return $titles[$locale][$type][$userType] ?? "$type for $userType";
    }

    /**
     * Get the content for a static content
     *
     * @param  string  $type  The content type
     * @param  string  $userType  The user type
     * @param  string  $locale  The language locale
     */
    private function getContent(string $type, string $userType, string $locale): string
    {
        if ($locale === 'en') {
            switch ($type) {
                case 'terms':
                    return $this->getEnglishTermsContent($userType);
                case 'privacy':
                    return $this->getEnglishPrivacyContent($userType);
                case 'about':
                    return $this->getEnglishAboutContent();
                default:
                    return 'Content not available.';
            }
        } else {
            switch ($type) {
                case 'terms':
                    return $this->getArabicTermsContent($userType);
                case 'privacy':
                    return $this->getArabicPrivacyContent($userType);
                case 'about':
                    return $this->getArabicAboutContent();
                default:
                    return 'المحتوى غير متوفر.';
            }
        }
    }

    /**
     * Get English terms content
     */
    private function getEnglishTermsContent(string $userType): string
    {
        if ($userType === 'customer') {
            return '<h1>Terms and Conditions for Customers</h1>
<p>Last Updated: July 6, 2025</p>

<h2>1. Introduction</h2>
<p>Welcome to Nabih. These Terms and Conditions govern your use of our mobile application and services as a customer. By accessing or using Nabih, you agree to be bound by these Terms.</p>

<h2>2. Definitions</h2>
<p>"App" refers to the Nabih mobile application.</p>
<p>"Services" refers to the vehicle maintenance and repair services facilitated through our platform.</p>
<p>"Vendor" refers to service providers registered on our platform.</p>

<h2>3. Account Registration</h2>
<p>You must register an account to use our services. You are responsible for maintaining the confidentiality of your account information and for all activities under your account.</p>

<h2>4. Booking Services</h2>
<p>When you book a service through our platform, you enter into a direct agreement with the vendor. Nabih acts as a facilitator and is not responsible for the quality of services provided by vendors.</p>

<h2>5. Payments</h2>
<p>All payments are processed securely through our platform. You agree to pay all fees associated with the services you book.</p>

<h2>6. Cancellation Policy</h2>
<p>Cancellation policies vary by vendor. Please review the specific cancellation policy before booking a service.</p>';
        } else {
            return '<h1>Terms and Conditions for Service Providers</h1>
<p>Last Updated: July 6, 2025</p>

<h2>1. Introduction</h2>
<p>Welcome to Nabih. These Terms and Conditions govern your use of our mobile application and services as a service provider. By registering as a vendor on Nabih, you agree to be bound by these Terms.</p>

<h2>2. Vendor Registration</h2>
<p>To register as a vendor, you must provide accurate business information and required documentation. Nabih reserves the right to verify your information and reject applications that do not meet our standards.</p>

<h2>3. Service Provision</h2>
<p>As a vendor, you agree to provide services to customers in a professional and timely manner. You are solely responsible for the quality of services provided.</p>

<h2>4. Commission and Payments</h2>
<p>Nabih charges a commission fee on each completed service. Payments will be transferred to your registered bank account within the timeframe specified in our payment policy.</p>

<h2>5. Cancellation Policy</h2>
<p>You may set your own cancellation policy, subject to Nabih\'s guidelines. Repeated cancellations or no-shows may result in penalties.</p>';
        }
    }

    /**
     * Get Arabic terms content
     */
    private function getArabicTermsContent(string $userType): string
    {
        if ($userType === 'customer') {
            return '<h1>الشروط والأحكام للعملاء</h1>
<p>آخر تحديث: 6 يوليو 2025</p>

<h2>1. مقدمة</h2>
<p>مرحبا بك في نبيه. تحكم هذه الشروط والأحكام استخدامك لتطبيق الهاتف المحمول والخدمات كعميل. من خلال الوصول إلى نبيه أو استخدامه، فإنك توافق على الالتزام بهذه الشروط.</p>

<h2>2. التعريفات</h2>
<p>"التطبيق" يشير إلى تطبيق نبيه للهاتف المحمول.</p>
<p>"الخدمات" تشير إلى خدمات صيانة وإصلاح المركبات التي يتم تسهيلها من خلال منصتنا.</p>
<p>"مزود الخدمة" يشير إلى مقدمي الخدمة المسجلين على منصتنا.</p>

<h2>3. تسجيل الحساب</h2>
<p>يجب عليك تسجيل حساب لاستخدام خدماتنا. أنت مسؤول عن الحفاظ على سرية معلومات حسابك وعن جميع الأنشطة التي تتم تحت حسابك.</p>

<h2>4. حجز الخدمات</h2>
<p>عندما تحجز خدمة من خلال منصتنا، فإنك تدخل في اتفاق مباشر مع مزود الخدمة. تعمل نبيه كوسيط وليست مسؤولة عن جودة الخدمات التي يقدمها مزودو الخدمة.</p>

<h2>5. المدفوعات</h2>
<p>تتم معالجة جميع المدفوعات بشكل آمن من خلال منصتنا. أنت توافق على دفع جميع الرسوم المرتبطة بالخدمات التي تحجزها.</p>

<h2>6. سياسة الإلغاء</h2>
<p>تختلف سياسات الإلغاء حسب مزود الخدمة. يرجى مراجعة سياسة الإلغاء المحددة قبل حجز خدمة.</p>';
        } else {
            return '<h1>الشروط والأحكام لمزودي الخدمة</h1>
<p>آخر تحديث: 6 يوليو 2025</p>

<h2>1. مقدمة</h2>
<p>مرحبا بك في نبيه. تحكم هذه الشروط والأحكام استخدامك لتطبيق الهاتف المحمول والخدمات كمزود خدمة. من خلال التسجيل كمزود خدمة في نبيه، فإنك توافق على الالتزام بهذه الشروط.</p>

<h2>2. تسجيل مزود الخدمة</h2>
<p>للتسجيل كمزود خدمة، يجب عليك تقديم معلومات دقيقة عن الأعمال والوثائق المطلوبة. تحتفظ نبيه بالحق في التحقق من معلوماتك ورفض الطلبات التي لا تلبي معاييرنا.</p>

<h2>3. تقديم الخدمة</h2>
<p>كمزود خدمة، توافق على تقديم خدمات للعملاء بطريقة محترفة وفي الوقت المناسب. أنت وحدك المسؤول عن جودة الخدمات المقدمة.</p>

<h2>4. العمولة والمدفوعات</h2>
<p>تفرض نبيه رسوم عمولة على كل خدمة مكتملة. سيتم تحويل المدفوعات إلى حسابك المصرفي المسجل خلال الإطار الزمني المحدد في سياسة الدفع لدينا.</p>

<h2>5. سياسة الإلغاء</h2>
<p>يمكنك تحديد سياسة الإلغاء الخاصة بك، وفقًا لإرشادات نبيه. قد يؤدي الإلغاء المتكرر أو عدم الحضور إلى فرض عقوبات.</p>';
        }
    }

    /**
     * Get English privacy content
     */
    private function getEnglishPrivacyContent(string $userType): string
    {
        if ($userType === 'customer') {
            return '<h1>Privacy Policy for Customers</h1>
<p>Last Updated: July 6, 2025</p>

<h2>1. Information We Collect</h2>
<p>We collect personal information when you register an account, book services, or interact with our app. This may include your name, contact details, vehicle information, location data, and payment information.</p>

<h2>2. How We Use Your Information</h2>
<p>We use your information to provide and improve our services, process payments, communicate with you, and match you with appropriate service providers.</p>

<h2>3. Information Sharing</h2>
<p>We share your information with service providers as necessary to fulfill your service requests. We may also share information with third-party payment processors and other service providers who assist in operating our platform.</p>

<h2>4. Data Security</h2>
<p>We implement appropriate security measures to protect your personal information. However, no method of transmission over the internet is 100% secure.</p>

<h2>5. Your Rights</h2>
<p>You have the right to access, correct, or delete your personal information. You may also withdraw consent for certain data processing activities.</p>';
        } else {
            return '<h1>Privacy Policy for Service Providers</h1>
<p>Last Updated: July 6, 2025</p>

<h2>1. Information We Collect</h2>
<p>We collect information about your business, services, pricing, availability, and banking details. We may also collect information about your performance and customer ratings.</p>

<h2>2. How We Use Your Information</h2>
<p>We use your information to create and maintain your vendor profile, match you with customer requests, process payments, and improve our services.</p>

<h2>3. Information Sharing</h2>
<p>We share your business information with customers who book your services. We may also share information with third-party payment processors and other service providers who assist in operating our platform.</p>

<h2>4. Data Security</h2>
<p>We implement appropriate security measures to protect your business information. However, no method of transmission over the internet is 100% secure.</p>

<h2>5. Your Rights</h2>
<p>You have the right to access, correct, or delete your information. You may also withdraw consent for certain data processing activities.</p>';
        }
    }

    /**
     * Get Arabic privacy content
     */
    private function getArabicPrivacyContent(string $userType): string
    {
        if ($userType === 'customer') {
            return '<h1>سياسة الخصوصية للعملاء</h1>
<p>آخر تحديث: 6 يوليو 2025</p>

<h2>1. المعلومات التي نجمعها</h2>
<p>نجمع المعلومات الشخصية عند تسجيل حساب، أو حجز خدمات، أو التفاعل مع تطبيقنا. قد يشمل ذلك اسمك، وتفاصيل الاتصال، ومعلومات المركبة، وبيانات الموقع، ومعلومات الدفع.</p>

<h2>2. كيف نستخدم معلوماتك</h2>
<p>نستخدم معلوماتك لتقديم خدماتنا وتحسينها، ومعالجة المدفوعات، والتواصل معك، ومطابقتك مع مزودي الخدمة المناسبين.</p>

<h2>3. مشاركة المعلومات</h2>
<p>نشارك معلوماتك مع مزودي الخدمة حسب الضرورة لتلبية طلبات الخدمة الخاصة بك. قد نشارك أيضًا المعلومات مع معالجات الدفع التابعة لجهات خارجية ومزودي الخدمة الآخرين الذين يساعدون في تشغيل منصتنا.</p>

<h2>4. أمن البيانات</h2>
<p>نقوم بتنفيذ إجراءات أمنية مناسبة لحماية معلوماتك الشخصية. ومع ذلك، لا توجد طريقة نقل عبر الإنترنت آمنة بنسبة 100٪.</p>

<h2>5. حقوقك</h2>
<p>لديك الحق في الوصول إلى معلوماتك الشخصية أو تصحيحها أو حذفها. يمكنك أيضًا سحب الموافقة على أنشطة معالجة البيانات المعينة.</p>';
        } else {
            return '<h1>سياسة الخصوصية لمزودي الخدمة</h1>
<p>آخر تحديث: 6 يوليو 2025</p>

<h2>1. المعلومات التي نجمعها</h2>
<p>نجمع معلومات عن عملك، والخدمات، والأسعار، والتوافر، وتفاصيل البنك. قد نجمع أيضًا معلومات حول أدائك وتقييمات العملاء.</p>

<h2>2. كيف نستخدم معلوماتك</h2>
<p>نستخدم معلوماتك لإنشاء ملف تعريف المزود والحفاظ عليه، ومطابقتك مع طلبات العملاء، ومعالجة المدفوعات، وتحسين خدماتنا.</p>

<h2>3. مشاركة المعلومات</h2>
<p>نشارك معلومات عملك مع العملاء الذين يحجزون خدماتك. قد نشارك أيضًا المعلومات مع معالجات الدفع التابعة لجهات خارجية ومزودي الخدمة الآخرين الذين يساعدون في تشغيل منصتنا.</p>

<h2>4. أمن البيانات</h2>
<p>نقوم بتنفيذ إجراءات أمنية مناسبة لحماية معلومات عملك. ومع ذلك، لا توجد طريقة نقل عبر الإنترنت آمنة بنسبة 100٪.</p>

<h2>5. حقوقك</h2>
<p>لديك الحق في الوصول إلى معلوماتك أو تصحيحها أو حذفها. يمكنك أيضًا سحب الموافقة على أنشطة معالجة البيانات المعينة.</p>';
        }
    }

    /**
     * Get English about content
     */
    private function getEnglishAboutContent(): string
    {
        return '<h1>About Nabih</h1>
<p>Welcome to Nabih, your trusted platform for vehicle maintenance and repair services.</p>

<h2>Our Mission</h2>
<p>At Nabih, we aim to revolutionize the vehicle service industry by connecting vehicle owners with qualified service providers through an easy-to-use mobile platform.</p>

<h2>Our Vision</h2>
<p>We envision a world where vehicle maintenance is convenient, transparent, and stress-free for everyone.</p>

<h2>How It Works</h2>
<p>For customers, Nabih offers a seamless way to find, book, and pay for vehicle services. Our platform provides transparent pricing, verified reviews, and real-time tracking of service status.</p>

<p>For service providers, Nabih provides a digital presence, customer management tools, and secure payment processing to help grow their business and provide excellent service.</p>

<h2>Contact Us</h2>
<p>If you have any questions or feedback, please don\'t hesitate to contact <NAME_EMAIL> or through the support section in our app.</p>';
    }

    /**
     * Get Arabic about content
     */
    private function getArabicAboutContent(): string
    {
        return '<h1>عن نبيه</h1>
<p>مرحبًا بك في نبيه، منصتك الموثوقة لخدمات صيانة وإصلاح المركبات.</p>

<h2>مهمتنا</h2>
<p>في نبيه، نهدف إلى إحداث ثورة في صناعة خدمة المركبات من خلال ربط مالكي المركبات بمزودي الخدمة المؤهلين من خلال منصة محمولة سهلة الاستخدام.</p>

<h2>رؤيتنا</h2>
<p>نحن نتصور عالماً حيث تكون صيانة المركبات مريحة وشفافة وخالية من التوتر للجميع.</p>

<h2>كيف يعمل</h2>
<p>بالنسبة للعملاء، توفر نبيه طريقة سلسة للعثور على خدمات المركبات وحجزها ودفعها. توفر منصتنا أسعارًا شفافة ومراجعات تم التحقق منها وتتبع في الوقت الفعلي لحالة الخدمة.</p>

<p>بالنسبة لمزودي الخدمة، توفر نبيه وجودًا رقميًا، وأدوات إدارة العملاء، ومعالجة آمنة للدفع للمساعدة في تنمية أعمالهم وتقديم خدمة ممتازة.</p>

<h2>اتصل بنا</h2>
<p>إذا كان لديك أي أسئلة أو ملاحظات، فلا تتردد في الاتصال بنا على <EMAIL> أو من خلال قسم الدعم في تطبيقنا.</p>';
    }
}
