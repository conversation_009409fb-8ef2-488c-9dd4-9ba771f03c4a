<?php

namespace App\Policies;

use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Auth\Access\HandlesAuthorization;

class VehiclePolicy
{
    use HandlesAuthorization;

    public function viewAny(User $user)
    {
        return true;
    }

    public function view(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function create(User $user)
    {
        return true; // Any authenticated user can create a vehicle
    }

    public function update(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }

    public function delete(User $user, Vehicle $vehicle)
    {
        return $user->id === $vehicle->user_id;
    }
}
