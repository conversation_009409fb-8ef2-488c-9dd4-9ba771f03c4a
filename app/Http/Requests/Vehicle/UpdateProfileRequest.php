<?php

namespace App\Http\Requests\Vehicle;

use App\Enums\Gender;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateProfileRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $user = $this->user();
        $rules = [
            'gender' => ['required', new Enum(Gender::class)],
            'avatar' => 'nullable|image|max:2048',
        ];

        if ($user->isVendor()) {
            // Vendor profile rules
            $rules['first_name'] = 'required|string|max:255';
            $rules['last_name'] = 'required|string|max:255';
            $rules['email'] = 'required|email|max:255|unique:users,email,'.$user->id;
        } else {
            // Customer profile rules
            $rules['name'] = 'required|string|max:255';
        }

        return $rules;
    }

    public function messages(): array
    {
        $locale = app()->getLocale();
        $user = $this->user();

        if ($locale === 'ar') {
            $messages = [
                'gender.required' => 'الجنس مطلوب',
                'gender.enum' => 'تم اختيار جنس غير صالح',
                'avatar.image' => 'يجب أن تكون الصورة الرمزية صورة',
                'avatar.max' => 'يجب ألا يتجاوز حجم الصورة الرمزية 2 ميجابايت',
            ];

            if ($user->isVendor()) {
                $messages['first_name.required'] = 'الاسم الأول مطلوب';
                $messages['first_name.string'] = 'يجب أن يكون الاسم الأول نصًا';
                $messages['first_name.max'] = 'يجب ألا يتجاوز الاسم الأول 255 حرفًا';
                $messages['last_name.required'] = 'الاسم الأخير مطلوب';
                $messages['last_name.string'] = 'يجب أن يكون الاسم الأخير نصًا';
                $messages['last_name.max'] = 'يجب ألا يتجاوز الاسم الأخير 255 حرفًا';
                $messages['email.required'] = 'البريد الإلكتروني مطلوب';
                $messages['email.email'] = 'الرجاء إدخال بريد إلكتروني صالح';
                $messages['email.max'] = 'يجب ألا يتجاوز البريد الإلكتروني 255 حرفًا';
                $messages['email.unique'] = 'البريد الإلكتروني مسجل بالفعل';
            } else {
                $messages['name.required'] = 'الاسم مطلوب';
                $messages['name.string'] = 'يجب أن يكون الاسم نصًا';
                $messages['name.max'] = 'يجب ألا يتجاوز الاسم 255 حرفًا';
            }

            return $messages;
        }

        $messages = [
            'gender.required' => 'Gender is required',
            'gender.enum' => 'Invalid gender selected',
            'avatar.image' => 'Avatar must be an image',
            'avatar.max' => 'Avatar size must not exceed 2MB',
        ];

        if ($user->isVendor()) {
            $messages['first_name.required'] = 'First name is required';
            $messages['first_name.string'] = 'First name must be a string';
            $messages['first_name.max'] = 'First name must not exceed 255 characters';
            $messages['last_name.required'] = 'Last name is required';
            $messages['last_name.string'] = 'Last name must be a string';
            $messages['last_name.max'] = 'Last name must not exceed 255 characters';
            $messages['email.required'] = 'Email is required';
            $messages['email.email'] = 'Please enter a valid email address';
            $messages['email.max'] = 'Email must not exceed 255 characters';
            $messages['email.unique'] = 'Email is already registered';
        } else {
            $messages['name.required'] = 'Name is required';
            $messages['name.string'] = 'Name must be a string';
            $messages['name.max'] = 'Name must not exceed 255 characters';
        }

        return $messages;
    }
}
