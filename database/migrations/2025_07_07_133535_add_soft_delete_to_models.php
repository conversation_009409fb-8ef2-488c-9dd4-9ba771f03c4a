<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add deleted_at column to vehicle-related tables
        if (! Schema::hasColumn('vehicles', 'deleted_at')) {
            Schema::table('vehicles', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('vehicle_services', 'deleted_at')) {
            Schema::table('vehicle_services', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('vehicle_reminders', 'deleted_at')) {
            Schema::table('vehicle_reminders', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('vehicle_notes', 'deleted_at')) {
            Schema::table('vehicle_notes', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        // Add deleted_at column to content-related tables
        if (! Schema::hasColumn('faqs', 'deleted_at')) {
            Schema::table('faqs', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('faq_translations', 'deleted_at')) {
            Schema::table('faq_translations', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('onboarding_screens', 'deleted_at')) {
            Schema::table('onboarding_screens', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('onboarding_screen_translations', 'deleted_at')) {
            Schema::table('onboarding_screen_translations', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('static_contents', 'deleted_at')) {
            Schema::table('static_contents', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        if (! Schema::hasColumn('static_content_translations', 'deleted_at')) {
            Schema::table('static_content_translations', function (Blueprint $table) {
                $table->softDeletes();
            });
        }

        // Add deleted_at column to Otp table
        if (! Schema::hasColumn('otps', 'deleted_at')) {
            Schema::table('otps', function (Blueprint $table) {
                $table->softDeletes();
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Remove deleted_at column from vehicle-related tables
        Schema::table('vehicles', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('vehicle_services', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('vehicle_reminders', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('vehicle_notes', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove deleted_at column from content-related tables
        Schema::table('faqs', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('faq_translations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('onboarding_screens', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('onboarding_screen_translations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('static_contents', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        Schema::table('static_content_translations', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });

        // Remove deleted_at column from Otp table
        Schema::table('otps', function (Blueprint $table) {
            $table->dropSoftDeletes();
        });
    }
};
