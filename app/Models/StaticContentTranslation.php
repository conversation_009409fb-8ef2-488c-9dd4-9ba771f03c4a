<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class StaticContentTranslation extends Model
{
    use SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // StaticContentTranslation model is a standalone entity
        });
    }

    protected $fillable = [
        'static_content_id',
        'locale',
        'title',
        'content',
    ];

    /**
     * Get the static content that owns the translation.
     */
    public function staticContent(): BelongsTo
    {
        return $this->belongsTo(StaticContent::class);
    }
}
