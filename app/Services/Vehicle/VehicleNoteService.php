<?php

namespace App\Services\Vehicle;

use App\DTOs\VehicleNoteDTO;
use App\Models\VehicleNote;

class VehicleNoteService
{
    public function getAllByVehicleId(int $vehicleId)
    {
        return VehicleNote::where('vehicle_id', $vehicleId)
            ->orderBy('note_date', 'desc')
            ->get();
    }

    public function create(VehicleNoteDTO $dto): VehicleNote
    {
        return VehicleNote::create($dto->toArray());
    }

    public function getById(int $id): ?VehicleNote
    {
        return VehicleNote::find($id);
    }

    public function update(int $id, VehicleNoteDTO|array $data): ?VehicleNote
    {
        $note = $this->getById($id);

        if (! $note) {
            return null;
        }

        $updateData = $data instanceof VehicleNoteDTO ? $data->toArray() : $data;
        $note->update($updateData);

        return $note->fresh();
    }

    public function delete(int $id): bool
    {
        $note = $this->getById($id);

        if (! $note) {
            return false;
        }

        return $note->delete();
    }
}
