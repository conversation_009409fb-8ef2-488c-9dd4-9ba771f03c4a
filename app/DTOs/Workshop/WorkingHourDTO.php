<?php

namespace App\DTOs\Workshop;

use App\DTOs\BaseDTO;
use App\Http\Requests\Vendor\UpdateWorkingHoursRequest;

class WorkingHourDTO extends BaseDTO
{
    public function __construct(
        public readonly int $workshop_id,
        public readonly string $day_of_week,
        public readonly ?string $start_time = null,
        public readonly ?string $end_time = null,
        public readonly bool $is_day_off = false
    ) {}

    /**
     * Create a collection of DTOs from a request
     */
    public static function collectionFromRequest(UpdateWorkingHoursRequest $request, int $workshop_id): array
    {
        $workingHoursDTOs = [];
        $data = $request->validated();

        foreach ($data['working_hours'] as $hourData) {
            $workingHoursDTOs[] = new self(
                workshop_id: $workshop_id,
                day_of_week: $hourData['day_of_week'],
                start_time: $hourData['enabled'] ? $hourData['start_time'] : null,
                end_time: $hourData['enabled'] ? $hourData['end_time'] : null,
                is_day_off: ! ($hourData['enabled'] ?? true)
            );
        }

        return $workingHoursDTOs;
    }
}
