<?php

namespace App\Http\Requests\Admin;

use App\Enums\Gender;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class UpdateCustomerRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('customer.update');
    }

    public function rules(): array
    {
        $customerId = $this->route('id');

        return [
            'name' => 'sometimes|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'gender' => ['sometimes', new Enum(Gender::class)],
            'is_verified' => 'sometimes|boolean',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'name' => 'name',
            'phone_number' => 'phone number',
            'gender' => 'gender',
            'is_verified' => 'verification status',
        ];
    }
}
