<?php

namespace App\Http\Requests\Admin;

use App\Enums\Gender;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Validation\Rules\Enum;

class CreateCustomerRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('customer.create');
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'phone_number' => 'nullable|string|max:20',
            'gender' => ['required', new Enum(Gender::class)],
            'is_verified' => 'required|boolean',
        ];
    }

    public function attributes(): array
    {
        return [
            'name' => 'name',
            'phone_number' => 'phone number',
            'gender' => 'gender',
            'is_verified' => 'is verified',
        ];
    }
}
