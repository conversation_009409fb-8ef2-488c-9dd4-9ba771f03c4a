<?php

namespace App\Jobs;

use App\Services\FirebaseService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class SendFCMNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $timeout = 60;

    protected $tokens;

    protected $title;

    protected $body;

    protected $data;

    /**
     * Create a new job instance.
     */
    public function __construct($tokens, string $title, string $body, array $data = [])
    {
        $this->tokens = is_array($tokens) ? $tokens : [$tokens];
        $this->title = $title;
        $this->body = $body;
        $this->data = $data;
    }

    /**
     * Execute the job.
     */
    public function handle(FirebaseService $firebaseService)
    {
        try {
            if (count($this->tokens) === 1) {
                $result = $firebaseService->sendToDevice(
                    $this->tokens[0],
                    $this->title,
                    $this->body,
                    $this->data
                );

                Log::info('FCM notification sent', [
                    'token' => $this->tokens[0],
                    'success' => $result['success'],
                ]);
            } else {
                $results = $firebaseService->sendToMultipleDevices(
                    $this->tokens,
                    $this->title,
                    $this->body,
                    $this->data
                );

                Log::info('FCM bulk notifications sent', [
                    'total_tokens' => count($this->tokens),
                    'results' => $results,
                ]);
            }
        } catch (\Exception $e) {
            Log::error('FCM Job failed: '.$e->getMessage(), [
                'tokens' => $this->tokens,
                'title' => $this->title,
                'body' => $this->body,
            ]);

            throw $e; // Re-throw to trigger retry mechanism
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception)
    {
        Log::error('FCM Job permanently failed after all retries', [
            'tokens' => $this->tokens,
            'title' => $this->title,
            'body' => $this->body,
            'error' => $exception->getMessage(),
        ]);
    }
}
