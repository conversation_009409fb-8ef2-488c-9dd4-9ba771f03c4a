<?php

namespace App\Models;

use App\Enums\Gender;
use App\Enums\UserType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Log;
use Laravel\Sanctum\HasApiTokens;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Permission\Traits\HasRoles;

class User extends Authenticatable implements HasMedia
{
    use HasApiTokens, HasFactory, HasRoles, InteractsWithMedia, Notifiable, SoftDeletes;

    protected $fillable = [
        'name',
        'first_name',
        'last_name',
        'phone_number',
        'email',
        'password',
        'gender',
        'address',
        'city',
        'user_type',
        'is_verified',
        'is_vendor_account_approved',
    ];

    protected $casts = [
        'user_type' => UserType::class,
        'gender' => Gender::class,
        'is_vendor_account_approved' => 'boolean',
        'is_verified' => 'boolean',
        'password' => 'hashed',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            try {
                // Cascade delete vehicles and their related records
                if (method_exists($model, 'vehicles')) {
                    $model->vehicles()->each(function ($vehicle) {
                        $vehicle->delete();
                    });
                }

                // Cascade delete workshops and their related records
                if (method_exists($model, 'workshops')) {
                    $model->workshops()->each(function ($workshop) {
                        $workshop->delete();
                    });
                }

                // Cascade delete FCM tokens
                if (method_exists($model, 'fcmTokens')) {
                    $model->fcmTokens()->delete();
                }

                // Cascade delete notifications
                if (method_exists($model, 'notifications')) {
                    $model->notifications()->each(function ($notification) {
                        $notification->delete();
                    });
                }

                // Cascade delete support requests
                if (method_exists($model, 'supportRequests')) {
                    $model->supportRequests()->delete();
                }
            } catch (\Exception $e) {
                Log::error('Error in User cascading delete: '.$e->getMessage());
            }
        });
    }

    public function vehicles(): HasMany
    {
        return $this->hasMany(Vehicle::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('avatar')
            ->singleFile();
    }

    public function isCustomer(): bool
    {
        return $this->user_type === UserType::CUSTOMER;
    }

    public function isVendor(): bool
    {
        return $this->user_type === UserType::VENDOR;
    }

    public function isAdmin(): bool
    {
        return $this->user_type === UserType::ADMIN;
    }

    public function workshops(): HasMany
    {
        return $this->hasMany(Workshop::class);
    }

    public function bookmarkedWorkshops(): BelongsToMany
    {
        return $this->belongsToMany(Workshop::class, 'workshop_bookmarks')
            ->withTimestamps();
    }

    /**
     * Get FCM tokens for this user
     */
    public function fcmTokens(): HasMany
    {
        return $this->hasMany(UserFcmToken::class);
    }

    /**
     * Get notifications for this user
     */
    public function notifications(): HasMany
    {
        return $this->hasMany(Notification::class);
    }

    /**
     * Get support requests for this user
     */
    public function supportRequests(): HasMany
    {
        return $this->hasMany(SupportRequest::class);
    }
}
