<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Vehicle extends Model
{
    use HasFactory, SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'make_brand',
        'model_year',
        'vin_number',
        'chassis_number',
        'mileage',
        'vehicle_type',
        'plate_number',
    ];

    protected $hidden = [
        'created_at',
        'updated_at',
    ];

    protected $casts = [
        'mileage' => 'integer',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // Load relationships to avoid N+1 queries
            $model->load([
                'services',
                'reminders',
                'notes',
            ]);

            // Cascade delete to related models
            $model->services->each(function ($item) {
                $item->delete();
            });

            $model->reminders->each(function ($item) {
                $item->delete();
            });

            $model->notes->each(function ($item) {
                $item->delete();
            });
        });
    }

    /**
     * Get the user that owns the vehicle
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the services for this vehicle
     */
    public function services(): HasMany
    {
        return $this->hasMany(VehicleService::class);
    }

    /**
     * Get the reminders for this vehicle
     */
    public function reminders(): HasMany
    {
        return $this->hasMany(VehicleReminder::class);
    }

    /**
     * Get the notes for this vehicle
     */
    public function notes(): HasMany
    {
        return $this->hasMany(VehicleNote::class);
    }
}
