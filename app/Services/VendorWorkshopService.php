<?php

namespace App\Services;

use App\DTOs\Workshop\WorkingHourDTO;
use App\DTOs\Workshop\WorkshopDTO;
use App\DTOs\Workshop\WorkshopServiceDTO;
use App\Jobs\ProcessTranslationJob;
use App\Models\User;
use App\Models\WorkingHour;
use App\Models\Workshop;
use App\Models\WorkshopService;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;

class VendorWorkshopService
{
    public function uploadImage(Workshop $workshop, $image, string $collection = 'workshop_photo'): bool
    {
        try {
            // First, clear any existing media in this collection
            $workshop->clearMediaCollection($collection);

            // Then, add the new image to the specified media collection
            $workshop->addMedia($image)
                ->toMediaCollection($collection);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    public function deleteImage(Workshop $workshop, string $collection = 'workshop_photo'): bool
    {
        try {
            // Clear the media collection
            $workshop->clearMediaCollection($collection);

            return true;
        } catch (\Exception $e) {
            return false;
        }
    }

    /**
     * Get all workshops for a vendor
     */
    public function getWorkshops(User $vendor)
    {
        return $vendor->workshops()
            ->with(['workingHours', 'services', 'user'])
            ->get();
    }

    /**
     * Get a specific workshop by ID
     */
    public function getWorkshop(User $vendor, int $workshopId)
    {
        return $vendor->workshops()
            ->with(['workingHours', 'services'])
            ->findOrFail($workshopId);
    }

    /**
     * Create a new workshop for a vendor
     *
     * @return Workshop
     *
     * @throws \Exception
     */
    public function createWorkshop(User $vendor, WorkshopDTO $workshopDTO)
    {
        try {
            DB::beginTransaction();

            // Handle description translations
            $locale = App::getLocale();
            $description = [$locale => $workshopDTO->description];

            // Prepare data for mass assignment
            $data = [
                'user_id' => $vendor->id,
                'name' => $workshopDTO->name,
                'description' => $description,
                'phone_number' => $workshopDTO->phone_number,
                'whatsapp' => $workshopDTO->whatsapp,
                'city' => $workshopDTO->city,
                'address' => $workshopDTO->address,
                'latitude' => $workshopDTO->latitude,
                'longitude' => $workshopDTO->longitude,
                'is_active' => $workshopDTO->is_active,
                'is_approved' => $workshopDTO->is_approved,
            ];

            // Create workshop with mass assignment
            $workshop = Workshop::create($data);

            // Handle photo if provided
            if ($workshopDTO->photo) {
                $workshop->addMedia($workshopDTO->photo)
                    ->toMediaCollection('workshop_photo');
            }

            // Process translations if description is provided
            if ($workshopDTO->description) {
                ProcessTranslationJob::dispatch(
                    Workshop::class,
                    $workshop->id,
                    'description',
                    $locale
                );
            }

            DB::commit();

            return $workshop->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update an existing workshop
     *
     * @return Workshop
     *
     * @throws \Exception
     */
    public function updateWorkshop(Workshop $workshop, WorkshopDTO $workshopDTO)
    {
        try {
            DB::beginTransaction();

            // Handle description translations
            $locale = App::getLocale();
            $description = $workshop->description ?: [];

            if ($workshopDTO->description !== null) {
                // Preserve existing translations in other languages
                $description[$locale] = $workshopDTO->description;
            }

            // Prepare data for mass update
            $data = [
                'name' => $workshopDTO->name,
                'description' => $description,
                'phone_number' => $workshopDTO->phone_number,
                'whatsapp' => $workshopDTO->whatsapp,
                'city' => $workshopDTO->city,
                'address' => $workshopDTO->address,
                'latitude' => $workshopDTO->latitude,
                'longitude' => $workshopDTO->longitude,
                'is_active' => $workshopDTO->is_active,
            ];

            // Update workshop with mass assignment
            $workshop->update($data);

            // Handle photo if provided
            if ($workshopDTO->photo) {
                $workshop->clearMediaCollection('workshop_photo');
                $workshop->addMedia($workshopDTO->photo)
                    ->toMediaCollection('workshop_photo');
            }

            // Process translations if description is updated
            if ($workshopDTO->description !== null) {
                ProcessTranslationJob::dispatch(
                    Workshop::class,
                    $workshop->id,
                    'description',
                    $locale
                );
            }

            DB::commit();

            return $workshop->fresh();
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Update working hours for a workshop
     *
     * @param  WorkingHourDTO[]  $workingHoursDTO
     * @return bool
     *
     * @throws \Exception
     */
    public function updateWorkingHours(Workshop $workshop, array $workingHoursDTO)
    {
        try {
            DB::beginTransaction();

            // Delete existing working hours
            $workshop->workingHours()->delete();

            // Prepare data for mass creation
            $workingHoursData = [];
            foreach ($workingHoursDTO as $hourDTO) {
                $workingHoursData[] = [
                    'workshop_id' => $workshop->id, // Required for mass creation
                    'day_of_week' => $hourDTO->day_of_week,
                    'start_time' => $hourDTO->is_day_off ? null : $hourDTO->start_time,
                    'end_time' => $hourDTO->is_day_off ? null : $hourDTO->end_time,
                    'is_day_off' => $hourDTO->is_day_off,
                ];
            }

            // Create new working hours using mass insert
            WorkingHour::insert($workingHoursData);

            DB::commit();

            return true;
        } catch (\Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }

    /**
     * Get working hours for a workshop
     */
    public function getWorkingHours(Workshop $workshop)
    {
        return $workshop->workingHours()->get();
    }

    /**
     * Create a new service for a workshop
     *
     * @return WorkshopService
     */
    public function createService(Workshop $workshop, WorkshopServiceDTO $serviceDTO)
    {
        return $workshop->services()->create([
            'name' => $serviceDTO->name,
        ]);
    }

    /**
     * Update an existing service
     *
     * @return WorkshopService
     */
    public function updateService(WorkshopService $service, WorkshopServiceDTO $serviceDTO)
    {
        $service->update([
            'name' => $serviceDTO->name,
        ]);

        return $service->fresh();
    }

    /**
     * Delete a service
     */
    public function deleteService(WorkshopService $service)
    {
        return $service->delete();
    }

    /**
     * Get all services for a workshop
     */
    public function getServices(Workshop $workshop)
    {
        return $workshop->services()->get();
    }

    public function syncServices(Workshop $workshop, array $serviceNames): array
    {
        // Get existing services
        $existingServices = $workshop->services()->get();
        $existingServiceNames = $existingServices->pluck('name')->toArray();

        // Services to add (in requested list but not in existing list)
        $servicesToAdd = array_diff($serviceNames, $existingServiceNames);

        // Services to remove (in existing list but not in requested list)
        $servicesToRemove = $existingServices->filter(function ($service) use ($serviceNames) {
            return ! in_array($service->name, $serviceNames);
        });

        // Add new services
        foreach ($servicesToAdd as $serviceName) {
            $this->createService(
                $workshop,
                new WorkshopServiceDTO(
                    workshop_id: $workshop->id,
                    name: $serviceName
                )
            );
        }

        // Remove services not in the list
        foreach ($servicesToRemove as $service) {
            $this->deleteService($service);
        }

        // Return updated services
        return $workshop->services()->get()->all();
    }
}
