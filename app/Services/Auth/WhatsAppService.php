<?php

namespace App\Services\Auth;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class WhatsAppService
{
    protected string $baseUrl = 'https://graph.facebook.com/v22.0/';

    protected string $phoneNumberId;

    protected string $accessToken;

    public function __construct()
    {
        $this->phoneNumberId = config('services.whatsapp.phone_number_id');
        $this->accessToken = config('services.whatsapp.access_token');
    }

    public function sendOtp(string $phoneNumber, string $otp): bool
    {
        try {
            // Determine template and language based on app locale
            $templateName = app()->getLocale() === 'ar' ? 'otp_arabic' : 'otp_english';
            $languageCode = app()->getLocale() === 'ar' ? 'ar' : 'en';

            $response = Http::withHeaders([
                'Authorization' => 'Bearer '.$this->accessToken,
                'Content-Type' => 'application/json',
            ])->post($this->baseUrl.$this->phoneNumberId.'/messages', [
                'messaging_product' => 'whatsapp',
                'recipient_type' => 'individual',
                'to' => $phoneNumber,
                'type' => 'template',
                'template' => [
                    'name' => $templateName,
                    'language' => [
                        'code' => $languageCode,
                    ],
                    'components' => [
                        [
                            'type' => 'body',
                            'parameters' => [
                                [
                                    'type' => 'text',
                                    'text' => $otp,
                                ],
                            ],
                        ],
                        [
                            'type' => 'button',
                            'sub_type' => 'url',
                            'index' => '0',
                            'parameters' => [
                                [
                                    'type' => 'text',
                                    'text' => $otp,
                                ],
                            ],
                        ],
                    ],
                ],
            ]);

            if ($response->successful()) {
                Log::info('WhatsApp OTP sent successfully', [
                    'phone' => $phoneNumber,
                    'template' => $templateName,
                    'language' => $languageCode,
                ]);

                return true;
            }

            Log::error('Failed to send WhatsApp OTP', [
                'phone' => $phoneNumber,
                'response' => $response->json(),
            ]);

            return false;
        } catch (Exception $e) {
            Log::error('Exception when sending WhatsApp OTP', [
                'phone' => $phoneNumber,
                'error' => $e->getMessage(),
            ]);

            return false;
        }
    }
}
