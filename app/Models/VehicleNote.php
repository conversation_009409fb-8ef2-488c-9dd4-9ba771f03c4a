<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class VehicleNote extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // VehicleNote model is a standalone entity
        });
    }

    protected $fillable = [
        'vehicle_id',
        'title',
        'note_date',
        'details',
    ];

    protected $casts = [
        'note_date' => 'date',
        'details' => 'json',
    ];

    /**
     * Get the vehicle that owns this note
     */
    public function vehicle(): BelongsTo
    {
        return $this->belongsTo(Vehicle::class);
    }
}
