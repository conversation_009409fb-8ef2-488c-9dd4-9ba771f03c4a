<?php

namespace App\Http\Resources\Vehicle;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleReminderResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        // Make sure review is properly decoded if it's still a JSON string
        $review = $this->review;
        if (is_string($review)) {
            $review = json_decode($review, true);
        }

        return [
            'id' => $this->id,
            'reminder_type' => $this->reminder_type,
            'is_mileage_based' => $this->is_mileage_based,
            'reminder_date' => $this->reminder_date,
            'mileage' => $this->mileage,
            'review' => $review,
        ];
    }
}
