<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Workshop extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, SoftDeletes;

    protected $fillable = [
        'user_id',
        'name',
        'description',
        'phone_number',
        'whatsapp',
        'city',
        'address',
        'latitude',
        'longitude',
        'is_active',
        'is_approved',
        'rating',
        'show_count',
        'location_count',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_approved' => 'boolean',
        'description' => 'array',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // Load relationships to avoid N+1 queries
            $model->load([
                'services',
                'workingHours',
            ]);

            // Cascade delete to related models
            $model->services->each(function ($item) {
                $item->delete();
            });

            $model->workingHours->each(function ($item) {
                $item->delete();
            });
        });
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function services(): HasMany
    {
        return $this->hasMany(WorkshopService::class);
    }

    public function workingHours(): HasMany
    {
        return $this->hasMany(WorkingHour::class);
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('workshop_photo')
            ->singleFile();
    }

    public function bookmarkedBy(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'workshop_bookmarks')
            ->withTimestamps();
    }
}
