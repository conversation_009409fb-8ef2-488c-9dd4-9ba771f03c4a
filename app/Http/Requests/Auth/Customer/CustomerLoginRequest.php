<?php

namespace App\Http\Requests\Auth\Customer;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\User;

class CustomerLoginRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone_number' => 'required|string|regex:/^\+[1-9]\d{1,14}$/|exists:users,phone_number,deleted_at,NULL',
            'fcm_token' => 'required|string|max:500',
            'device_type' => 'nullable|string|in:android,ios',
            'device_name' => 'nullable|string|max:255',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.exists' => 'المستخدم غير مسجل',
                'fcm_token.required' => 'Token Firebase مطلوب',
                'fcm_token.string' => 'Token Firebase يجب أن يكون سلاسلة',
                'fcm_token.max' => 'Token Firebase يجب أن لا يتجاوز 500 حرف',
            ];
        }

        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.exists' => 'This phone number is not registered. Please sign up first',
            'fcm_token.required' => 'Token Firebase is required',
            'fcm_token.string' => 'Token Firebase must be a string',
            'fcm_token.max' => 'Token Firebase must not exceed 500 characters',
        ];
    }

    protected function afterValidation()
    {
        $user = User::where('phone_number', $this->phone_number)->first();

        if (! $user || $user->user_type !== UserType::CUSTOMER) {
            $validator = \Illuminate\Support\Facades\Validator::make($this->all(), []);
            $validator->errors()->add('phone_number', 'The User is not a customer');
            $this->failedValidation($validator);
        }
    }
}
