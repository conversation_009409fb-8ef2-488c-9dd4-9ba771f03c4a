<?php

namespace App\Http\Controllers\Vehicle;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vehicle\VehicleServiceRequest;
use App\Http\Resources\Vehicle\VehicleServiceResource;
use App\Jobs\ProcessTranslationJob;
use App\Models\Vehicle;
use App\Models\VehicleService;
use App\Services\Vehicle\VehicleServiceService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Facades\DB;

class VehicleServiceController extends Controller
{
    protected $vehicleServiceService;

    public function __construct(VehicleServiceService $vehicleServiceService)
    {
        $this->vehicleServiceService = $vehicleServiceService;
    }

    public function index(Request $request, int $vehicleId): JsonResponse|AnonymousResourceCollection
    {
        // Check if user owns the vehicle
        $vehicle = Vehicle::findOrFail($vehicleId);
        if (! $request->user()->can('viewAny', [VehicleService::class, $vehicle])) {
            return ApiResponse::error('You do not have permission to view these services', 403);
        }

        $services = $this->vehicleServiceService->getAllByVehicleId($vehicleId);

        return VehicleServiceResource::collection($services);
    }

    public function store(VehicleServiceRequest $request, int $vehicleId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $service = $this->vehicleServiceService->create($dto);

            if (! empty($dto->review)) {
                ProcessTranslationJob::dispatch(
                    VehicleService::class,
                    $service->id,
                    'review',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'service' => new VehicleServiceResource($service),
            ], 'Service added successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to create service: '.$e->getMessage(), 500);
        }
    }

    public function show(Request $request, int $vehicleId, int $serviceId): JsonResponse
    {
        $service = $this->vehicleServiceService->getById($serviceId);

        if (! $service) {
            return ApiResponse::error('Service not found', 404);
        }

        // Check if user has permission to view this service
        if (! $request->user()->can('view', $service)) {
            return ApiResponse::error('You do not have permission to view this service', 403);
        }

        return ApiResponse::success([
            'service' => new VehicleServiceResource($service),
        ], 'Service retrieved successfully');
    }

    public function update(VehicleServiceRequest $request, int $vehicleId, int $serviceId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $service = $this->vehicleServiceService->update($serviceId, $dto);

            if (! $service) {
                DB::rollBack();

                return ApiResponse::error('Service not found', 404);
            }

            // Dispatch translation job if review exists
            if (! empty($dto->review)) {
                ProcessTranslationJob::dispatch(
                    VehicleService::class,
                    $service->id,
                    'review',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'service' => new VehicleServiceResource($service),
            ], 'Service updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to update service: '.$e->getMessage(), 500);
        }
    }

    public function destroy(Request $request, int $vehicleId, int $serviceId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $service = VehicleService::find($serviceId);
            if (! $service) {
                DB::rollBack();

                return ApiResponse::error('Service not found', 404);
            }

            // Check if user has permission to delete this service
            if (! $request->user()->can('delete', $service)) {
                DB::rollBack();

                return ApiResponse::error('You do not have permission to delete this service', 403);
            }

            $deleted = $this->vehicleServiceService->delete($serviceId);

            if (! $deleted) {
                DB::rollBack();

                return ApiResponse::error('Failed to delete service', 500);
            }

            DB::commit();

            return ApiResponse::success([], 'Service deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to delete service: '.$e->getMessage(), 500);
        }
    }
}
