<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class CreateRoleRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('role.create');
    }

    public function rules(): array
    {
        return [
            'name' => [
                'required',
                'string',
                'unique:roles,name',
                'not_in:super-admin',
            ],
            'permissions' => 'nullable|array',
            'permissions.*' => 'exists:permissions,name',
        ];
    }

    public function messages(): array
    {
        return [
            'name.not_in' => 'The super-admin role cannot be created. It is a system reserved role.',
        ];
    }
}
