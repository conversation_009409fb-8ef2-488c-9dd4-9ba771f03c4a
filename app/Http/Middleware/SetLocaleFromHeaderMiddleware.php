<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;

class SetLocaleFromHeaderMiddleware
{
    public function handle(Request $request, Closure $next): mixed
    {
        $acceptLanguage = $request->header('Accept-Language', config('app.locale'));

        // Parse the Accept-Language header to get the primary locale
        $locale = config('app.locale'); // Default locale

        if (! empty($acceptLanguage)) {
            // Extract the primary locale from the Accept-Language header
            // Format could be like: en_GB,en;q=0.9,es;q=0.8,en_US;q=0.7
            $locales = explode(',', $acceptLanguage);
            if (! empty($locales[0])) {
                // Get the first locale and remove any quality suffixD
                $primaryLocale = explode(';', $locales[0])[0];
                // Replace underscore with hyphen if needed
                $locale = str_replace('_', '-', trim($primaryLocale));
            }
        }

        App::setLocale($locale);

        return $next($request);
    }
}
