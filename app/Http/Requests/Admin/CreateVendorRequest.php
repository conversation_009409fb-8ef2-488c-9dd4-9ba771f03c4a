<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class CreateVendorRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('vendor.create');
    }

    public function rules(): array
    {
        return [
            'first_name' => 'required|string|max:255',
            'last_name' => 'required|string|max:255',
            'email' => 'required|email|unique:users,email',
            'phone_number' => 'nullable|string|max:20',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'first_name' => 'first name',
            'last_name' => 'last name',
        ];
    }
}
