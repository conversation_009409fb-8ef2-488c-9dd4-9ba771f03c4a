<?php

namespace App\Services;

use App\Jobs\OfferNotificationJob;
use App\Models\Offer;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\UploadedFile;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Facades\Log;

class OfferService
{
    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function createOffer(array $data): Offer
    {
        $offer = Offer::create($data);

        if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
            $offer->addMedia($data['image'])->toMediaCollection('offer_image');
        }

        // Create single common notification for all customers when new offer is created
        if ($offer->is_active) {
            try {
                $notification = $this->notificationService->createOfferNotification($offer);
                Log::info('Created common notification for new offer', [
                    'offer_id' => $offer->id,
                    'notification_id' => $notification->id,
                    'offer_title' => $offer->title,
                ]);

                // Dispatch job to send FCM push notifications to all active customers
                OfferNotificationJob::dispatch($offer);
                Log::info('Dispatched FCM notification job for new offer', [
                    'offer_id' => $offer->id,
                    'offer_title' => $offer->title,
                ]);
            } catch (\Exception $e) {
                Log::error('Failed to create notification for new offer: '.$e->getMessage(), [
                    'offer_id' => $offer->id,
                    'exception' => $e,
                ]);
            }
        }

        return $offer;
    }

    public function updateOffer(Offer $offer, array $data): Offer
    {
        // Handle image upload separately
        if (isset($data['image']) && $data['image'] instanceof UploadedFile) {
            // Clear existing media in this collection before adding new one
            $offer->clearMediaCollection('offer_image');

            // Add new media to the collection
            $offer->addMedia($data['image'])->toMediaCollection('offer_image');

            // Remove image from data array to avoid database update issues
            unset($data['image']);
        }

        // Update all other fields
        $offer->update($data);

        return $offer;
    }

    public function deleteOffer(Offer $offer): bool
    {
        return $offer->delete();
    }

    public function getAllOffers(array $filters = []): LengthAwarePaginator
    {
        $query = Offer::query();

        if (isset($filters['search'])) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            });
        }

        if (isset($filters['is_active']) && $filters['is_active'] !== '') {
            $query->where('is_active', $filters['is_active'] == 'true');
        }

        return $query->orderBy('display_order', 'asc')
            ->paginate($filters['per_page'] ?? 10);
    }

    public function getActiveOffers(): Collection
    {
        return Offer::active()->ordered()->get();
    }
}
