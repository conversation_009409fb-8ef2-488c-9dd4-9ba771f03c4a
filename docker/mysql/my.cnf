[mysqld]
# Add bind-address to allow connections from any host
bind-address = 0.0.0.0
default_authentication_plugin=mysql_native_password
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci

# Performance Settings
max_connections=1000
innodb_buffer_pool_size=1G
innodb_log_file_size=256M
innodb_flush_log_at_trx_commit=1
innodb_flush_method=O_DIRECT
innodb_file_per_table=1

# Logging
general_log=1
general_log_file=/var/log/mysql/mysql-general.log
slow_query_log=1
slow_query_log_file=/var/log/mysql/mysql-slow.log
long_query_time=2
log_queries_not_using_indexes=1

[client]
default-character-set=utf8mb4

[mysql]
default-character-set=utf8mb4
