<?php

namespace App\Services\Auth;

use App\Enums\UserType;
use App\Http\Resources\UserResource;
use App\Models\Otp;
use App\Models\User;
use App\Services\FcmTokenService;

class CustomerAuthService
{
    protected OtpService $otpService;

    protected FcmTokenService $fcmTokenService;

    public function __construct(OtpService $otpService, FcmTokenService $fcmTokenService)
    {
        $this->otpService = $otpService;
        $this->fcmTokenService = $fcmTokenService;
    }

    public function sendOtp(string $phoneNumber)
    {
        // Check if there's a soft deleted user with this phone number and restore if found
        $this->restoreUserIfDeleted(['phone_number' => $phoneNumber]);

        return $this->otpService->generateOtp($phoneNumber, 'phone');
    }

    public function resendOtp(string $phoneNumber)
    {
        return $this->otpService->resendOtp($phoneNumber, 'phone');
    }

    public function createUnVerifiedUser(string $name, string $phoneNumber)
    {
        // First check if there's a soft deleted user with this phone number
        $existingUser = User::withTrashed()
            ->where('phone_number', $phoneNumber)
            ->first();

        if ($existingUser) {
            if ($existingUser->trashed()) {
                // Restore the user
                $existingUser->restore();
                $existingUser->update([
                    'name' => $name,
                    'is_verified' => false,
                ]);

                return $existingUser;
            }
        } else {
            // Create new user if no existing user (including soft deleted) found
            return User::create([
                'name' => $name,
                'phone_number' => $phoneNumber,
                'is_verified' => false,
                'user_type' => UserType::CUSTOMER,
            ]);
        }
    }

    public function processOtpVerification(string $phoneNumber)
    {
        // Check if there's a soft deleted user with this phone number and restore if found
        $this->restoreUserIfDeleted(['phone_number' => $phoneNumber]);

        $user = User::where('phone_number', $phoneNumber)->first();
        $user->is_verified = true;
        $user->save();

        // Delete OTP after successful verification
        Otp::where('phone_number', $phoneNumber)
            ->delete();

        return [
            'user' => new UserResource($user),
            'token' => $this->createToken($user),
        ];
    }

    private function createToken(User $user): string
    {
        return $user->createToken('auth_token')->plainTextToken;
    }

    private function restoreUserIfDeleted(array $criteria): ?User
    {
        // Find soft-deleted user by criteria
        $deletedUser = User::onlyTrashed()
            ->where($criteria)
            ->first();

        if ($deletedUser) {
            // Restore the user if found
            $deletedUser->restore();

            return $deletedUser;
        }

        return null;
    }

    public function logout(User $user, string $fcmToken): bool
    {
        $user->currentAccessToken()->delete();

        if ($fcmToken) {
            $this->fcmTokenService->deactivateToken($user->id, $fcmToken);
        }

        return true;
    }
}
