<?php

namespace App\Http\Resources\Vendor;

use App\Http\Resources\UserResource;
use Illuminate\Http\Resources\Json\JsonResource;

class WorkshopResource extends JsonResource
{
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description ?? [],  // Return the full description array with all languages
            'phone_number' => $this->phone_number,
            'whatsapp' => $this->whatsapp,
            'city' => $this->city,
            'address' => $this->address,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'is_active' => $this->is_active,
            // 'is_approved' => $this->is_approved,
            'rating' => $this->rating,
            'show_count' => $this->show_count ?? 0,
            'location_count' => $this->location_count ?? 0,
            'photo_url' => $this->getFirstMediaUrl('workshop_photo') ?? null,

            'is_bookmarked' => $this->when($request->user(), function () use ($request) {
                return $request->user()->bookmarkedWorkshops()->where('workshop_id', $this->id)->exists();
            }, false),
            'services' => $this->whenLoaded('services', function () {
                return WorkshopServiceResource::collection($this->services);
            }),
            'working_hours' => $this->whenLoaded('workingHours', function () {
                return WorkingHourResource::collection($this->workingHours);
            }),
            'vendor' => $this->whenLoaded('user', function () {
                return new UserResource($this->user);
            }),
        ];
    }
}
