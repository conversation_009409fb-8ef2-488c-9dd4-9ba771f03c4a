<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class CreateOfferRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'title' => 'required|array',
            'title.en' => 'required|string|max:255',
            'title.ar' => 'required|string|max:255',
            'description' => 'required|array',
            'description.en' => 'required|string',
            'description.ar' => 'required|string',
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:5120', // 5MB max
            'discount_text' => 'required|array',
            'discount_text.en' => 'required|string|max:255',
            'discount_text.ar' => 'required|string|max:255',
            'is_active' => 'boolean',
            'display_order' => 'nullable|integer|min:0',
            'start_date' => 'nullable|date_format:Y-m-d H:i:s',
            'end_date' => 'nullable|date_format:Y-m-d H:i:s|after_or_equal:start_date',
            'button_text' => 'required|array',
            'button_text.en' => 'required|string|max:255',
            'button_text.ar' => 'required|string|max:255',
            'button_link' => 'nullable|string|max:255',
            'is_limited_time' => 'boolean',
        ];
    }
}
