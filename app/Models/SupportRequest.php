<?php

namespace App\Models;

use App\Enums\SupportRequestIssueType;
use App\Enums\SupportRequestStatus;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class SupportRequest extends Model
{
    use HasFactory, SoftDeletes;

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'request_id',
        'user_id',
        'subject',
        'issue_type',
        'details',
        'admin_response',
        'status',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
        'deleted_at' => 'datetime',
        'status' => SupportRequestStatus::class,
        'issue_type' => SupportRequestIssueType::class,
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // SupportRequest model is a standalone entity
        });
    }

    /**
     * Get the user that owns the support request.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Generate a unique request ID with the format REQ-XX.
     */
    public static function generateRequestId(): string
    {
        $lastRequest = self::orderBy('id', 'desc')->withTrashed()->first();
        $nextId = $lastRequest ? (int) substr($lastRequest->request_id, 4) + 1 : 1;

        return 'REQ-'.str_pad($nextId, 2, '0', STR_PAD_LEFT);
    }
}
