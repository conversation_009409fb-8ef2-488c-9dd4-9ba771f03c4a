<?php

namespace App\Http\Resources\Customer;

use Illuminate\Http\Resources\Json\JsonResource;

class SupportRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'request_id' => $this->request_id,
            'subject' => $this->subject,
            'issue_type' => $this->issue_type,
            'details' => $this->details,
            'admin_response' => $this->admin_response,
            'status' => $this->status,
            'date_created' => $this->created_at->format('Y-m-d'),
        ];
    }
}
