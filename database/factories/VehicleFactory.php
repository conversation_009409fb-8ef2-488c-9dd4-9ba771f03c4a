<?php

namespace Database\Factories;

use App\Models\User;
use App\Models\Vehicle;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\Vehicle>
 */
class VehicleFactory extends Factory
{
    protected $model = Vehicle::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        $vehicleTypes = ['SUV', 'Sedan', 'MPV', 'Hatchback', 'Convertible', 'Coupe'];

        return [
            'user_id' => User::factory(),
            'name' => fake()->randomElement(['Toyota Fortuner', 'Audi A6', 'Hyundai Verna', 'Toyota Innova', 'Ford Endeavour', 'Kia Seltos']),
            'make_brand' => fake()->randomElement(['Toyota', 'Audi', 'Hyundai', 'Ford', 'Kia', 'Honda']),
            'model_year' => (string) fake()->numberBetween(2010, 2023),
            'vin_number' => fake()->regexify('VIN-[A-Z0-9]{10}'),
            'chassis_number' => fake()->regexify('CH-[A-Z0-9]{10}'),
            'mileage' => fake()->numberBetween(0, 200000),
            'vehicle_type' => fake()->randomElement($vehicleTypes),
            'plate_number' => fake()->regexify('[A-Z]{2}-[0-9]{4}'),
        ];
    }
}
