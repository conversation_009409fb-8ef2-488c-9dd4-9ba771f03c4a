<?php

namespace App\Console\Commands;

use App\Enums\UserType;
use App\Models\User;
use App\Services\NotificationService;
use Illuminate\Console\Command;

class TestAdminNotificationsCommand extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test:admin-notifications {type=all : Type of notification to test (vendor|support|system|all)}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Test admin notification system with sample data';

    protected NotificationService $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        parent::__construct();
        $this->notificationService = $notificationService;
    }

    /**
     * Execute the console command.
     */
    public function handle()
    {
        $type = $this->argument('type');

        $this->info('Testing Admin Notification System...');

        // Check if we have admin users
        $adminCount = User::where('user_type', UserType::ADMIN)->count();
        if ($adminCount === 0) {
            $this->error('No admin users found. Please create admin users first.');

            return 1;
        }

        $this->info("Found {$adminCount} admin user(s)");

        switch ($type) {
            case 'vendor':
                $this->testVendorNotifications();
                break;
            case 'support':
                $this->testSupportNotifications();
                break;
            case 'system':
                $this->testSystemNotifications();
                break;
            case 'all':
                $this->testVendorNotifications();
                $this->testSupportNotifications();
                $this->testSystemNotifications();
                break;
            default:
                $this->error('Invalid type. Use: vendor, support, system, or all');

                return 1;
        }

        $this->info('Admin notification tests completed!');

        return 0;
    }

    private function testVendorNotifications()
    {
        $this->info('Testing vendor notifications...');

        // Create a test vendor user
        $testVendor = User::create([
            'name' => 'Test Vendor',
            'first_name' => 'Test',
            'last_name' => 'Vendor',
            'phone_number' => '+*********'.rand(100, 999),
            'email' => 'test.vendor.'.rand(1000, 9999).'@example.com',
            'is_verified' => false,
            'user_type' => UserType::VENDOR,
            'is_vendor_account_approved' => false,
        ]);

        // Test vendor registration notification
        $this->notificationService->createVendorRegistrationNotification($testVendor);
        $this->line('✓ Vendor registration notification created');

        // Test vendor approval request notification
        $this->notificationService->createVendorApprovalRequestNotification($testVendor);
        $this->line('✓ Vendor approval request notification created');

        // Clean up test vendor
        $testVendor->forceDelete();
    }

    private function testSupportNotifications()
    {
        $this->info('Testing support request notifications...');

        // Create a test customer user
        $testCustomer = User::create([
            'name' => 'Test Customer',
            'first_name' => 'Test',
            'last_name' => 'Customer',
            'phone_number' => '+*********'.rand(100, 999),
            'email' => 'test.customer.'.rand(1000, 9999).'@example.com',
            'is_verified' => true,
            'user_type' => UserType::CUSTOMER,
        ]);

        // Create a test support request
        $testSupportRequest = \App\Models\SupportRequest::create([
            'request_id' => 'TEST-'.rand(10000, 99999),
            'user_id' => $testCustomer->id,
            'subject' => 'Test Support Request',
            'issue_type' => \App\Enums\SupportRequestIssueType::BUG_REPORT,
            'details' => 'This is a test support request for notification testing.',
            'status' => \App\Enums\SupportRequestStatus::PENDING,
        ]);

        // Test support request notification
        $this->notificationService->createSupportRequestNotification($testSupportRequest);
        $this->line('✓ Support request notification created');

        // Clean up test data
        $testSupportRequest->forceDelete();
        $testCustomer->forceDelete();
    }

    private function testSystemNotifications()
    {
        $this->info('Testing system alert notifications...');

        // Test system alert notification
        $this->notificationService->createSystemAlertNotification(
            'System Maintenance Alert',
            'The system will undergo maintenance on '.now()->addDays(1)->format('Y-m-d H:i'),
            'تنبيه صيانة النظام',
            'سيخضع النظام للصيانة في '.now()->addDays(1)->format('Y-m-d H:i')
        );
        $this->line('✓ System alert notification created');

        // Test another system alert
        $this->notificationService->createSystemAlertNotification(
            'High Traffic Alert',
            'The system is experiencing high traffic. Performance may be affected.',
            'تنبيه حركة مرور عالية',
            'يواجه النظام حركة مرور عالية. قد يتأثر الأداء.'
        );
        $this->line('✓ High traffic alert notification created');
    }
}
