<?php

namespace App\Services\Vehicle;

use App\DTOs\VehicleServiceDTO;
use App\Models\VehicleService;

class VehicleServiceService
{
    public function getAllByVehicleId(int $vehicleId)
    {
        return VehicleService::where('vehicle_id', $vehicleId)
            ->orderBy('service_date', 'desc')
            ->get();
    }

    public function create(VehicleServiceDTO $dto): VehicleService
    {
        return VehicleService::create($dto->toArray());
    }

    public function getById(int $id): ?VehicleService
    {
        return VehicleService::find($id);
    }

    public function update(int $id, VehicleServiceDTO|array $data): ?VehicleService
    {
        $service = $this->getById($id);

        if (! $service) {
            return null;
        }

        $updateData = $data instanceof VehicleServiceDTO ? $data->toArray() : $data;
        $service->update($updateData);

        return $service->fresh();
    }

    public function delete(int $id): bool
    {
        $service = $this->getById($id);

        if (! $service) {
            return false;
        }

        return $service->delete();
    }
}
