<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Http\Resources\OnboardingScreen\OnboardingScreenResource;
use App\Services\OnboardingScreenService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;

class OnboardingController extends Controller
{
    /**
     * @var OnboardingScreenService
     */
    protected $onboardingScreenService;

    /**
     * OnboardingController constructor.
     */
    public function __construct(OnboardingScreenService $onboardingScreenService)
    {
        $this->onboardingScreenService = $onboardingScreenService;
    }

    /**
     * Get all active onboarding screens.
     */
    public function index(Request $request): AnonymousResourceCollection
    {
        $locale = $request->header('Accept-Language', 'en');
        $screens = $this->onboardingScreenService->getAllScreens($locale);

        return OnboardingScreenResource::collection($screens);
    }
}
