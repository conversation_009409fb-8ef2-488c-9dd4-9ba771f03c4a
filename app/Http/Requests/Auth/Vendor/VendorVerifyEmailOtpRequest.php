<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Http\Requests\BaseApiRequest;
use App\Models\Otp;

class VendorVerifyEmailOtpRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|string|email|exists:otps,email',
            'otp' => [
                'required',
                'string',
                'size:4',
                function ($attribute, $value, $fail) {
                    if (! Otp::where('email', $this->email)
                        ->where('otp', $value)
                        ->where('expires_at', '>', now())
                        ->exists()) {
                        $fail('OTP has expired or does not exist');
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح',
                'email.exists' => 'لم يتم إرسال رمز التحقق إلى هذا البريد الإلكتروني',
                'otp.required' => 'رمز التحقق مطلوب',
                'otp.size' => 'يجب أن يتكون رمز التحقق من 4 أرقام',
                'otp.numeric' => 'يجب أن يكون رمز التحقق أرقامًا فقط',
            ];
        }

        return [
            'email.required' => 'Email address is required',
            'email.email' => 'Please enter a valid email address',
            'email.exists' => 'OTP has not been sent to this email address',
            'otp.required' => 'OTP is required',
            'otp.size' => 'OTP must be 4 digits',
            'otp.numeric' => 'OTP must be numeric',
        ];
    }
}
