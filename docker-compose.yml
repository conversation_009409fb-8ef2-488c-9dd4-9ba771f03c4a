services:
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: nabih-app-dev
    restart: unless-stopped
    working_dir: /var/www
    volumes:
      - .:/var/www:delegated
      - ./storage:/var/www/storage:delegated
      - app-cache:/var/www/bootstrap/cache
      - ./docker/php/php.dev.ini:/usr/local/etc/php/conf.d/99-overrides.ini
    # Ensure runtime directories are writable by the web server inside the container
    command: >-
      sh -c "chown -R www-data:www-data /var/www/storage /var/www/bootstrap/cache && php-fpm"
    environment:
      - PHP_IDE_CONFIG=serverName=nabih-api
      - XDEBUG_CONFIG=client_host=host.docker.internal
      - "DB_HOST=mysql"
      - "REDIS_HOST=redis"
      - "QUEUE_CONNECTION=redis"
      - "SESSION_DRIVER=redis"
      - "CACHE_DRIVER=redis"
    depends_on:
      - mysql
      - redis
    networks:
      - app-network

  nginx:
    image: nginx:alpine
    container_name: nabih-nginx-dev
    restart: unless-stopped
    ports:
      - "8000:80"
    volumes:
      - .:/var/www:delegated
      - ./storage:/var/www/storage:delegated
      - ./docker/nginx/conf.d/app.dev.conf:/etc/nginx/conf.d/default.conf
    depends_on:
      - app
    networks:
      - app-network

  mysql:
    image: mysql:8.0
    container_name: nabih-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_DATABASE: ${DB_DATABASE:-laravel}
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_PASSWORD: ${DB_PASSWORD:-secret}
      MYSQL_USER: ${DB_USERNAME:-laravel}
    volumes:
      - dbdata:/var/lib/mysql
      - ./docker/mysql/my.cnf:/etc/mysql/conf.d/my.cnf
    ports:
      - "3307:3306"
    networks:
      - app-network

  redis:
    image: redis:latest
    container_name: nabih-redis-dev
    restart: unless-stopped
    ports:
      - "6380:6379"
    volumes:
      - redisdata:/data
    command: redis-server --appendonly yes
    networks:
      - app-network

  mailpit:
    image: axllent/mailpit:latest
    container_name: nabih-mailpit-dev
    restart: unless-stopped
    ports:
      - "1025:1025" # SMTP
      - "8025:8025" # HTTP
    networks:
      - app-network

volumes:
  dbdata:
    driver: local
  redisdata:
    driver: local
  app-cache:
    driver: local

networks:
  app-network:
    driver: bridge
    name: nabih-dev-network
