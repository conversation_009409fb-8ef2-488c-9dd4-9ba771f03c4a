<?php

namespace App\Http\Controllers\Vehicle;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vehicle\VehicleNoteRequest;
use App\Http\Resources\Vehicle\VehicleNoteResource;
use App\Jobs\ProcessTranslationJob;
use App\Models\Vehicle;
use App\Models\VehicleNote;
use App\Services\Vehicle\VehicleNoteService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class VehicleNoteController extends Controller
{
    protected $vehicleNoteService;

    public function __construct(VehicleNoteService $vehicleNoteService)
    {
        $this->vehicleNoteService = $vehicleNoteService;
    }

    public function index(Request $request, int $vehicleId): JsonResponse
    {
        $vehicle = Vehicle::findOrFail($vehicleId);
        if (! $request->user()->can('viewAny', [VehicleNote::class, $vehicle])) {
            return ApiResponse::error('You do not have permission to view these notes', 403);
        }

        $notes = $this->vehicleNoteService->getAllByVehicleId($vehicleId);

        return ApiResponse::success([
            'notes' => VehicleNoteResource::collection($notes),
        ], 'Notes retrieved successfully');
    }

    public function store(VehicleNoteRequest $request, int $vehicleId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $note = $this->vehicleNoteService->create($dto);

            if (! empty($dto->details)) {
                ProcessTranslationJob::dispatch(
                    VehicleNote::class,
                    $note->id,
                    'details',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'note' => new VehicleNoteResource($note),
            ], 'Note added successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to create note: '.$e->getMessage(), 500);
        }
    }

    public function show(Request $request, int $vehicleId, int $noteId): JsonResponse
    {
        $note = $this->vehicleNoteService->getById($noteId);

        if (! $note) {
            return ApiResponse::error('Note not found', 404);
        }

        if (! $request->user()->can('view', $note)) {
            return ApiResponse::error('You do not have permission to view this note', 403);
        }

        return ApiResponse::success([
            'note' => new VehicleNoteResource($note),
        ], 'Note retrieved successfully');
    }

    public function update(VehicleNoteRequest $request, int $vehicleId, int $noteId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $dto = $request->toDTO();
            $note = $this->vehicleNoteService->update($noteId, $dto);

            if (! $note) {
                DB::rollBack();

                return ApiResponse::error('Note not found', 404);
            }

            if (! empty($dto->details)) {
                ProcessTranslationJob::dispatch(
                    VehicleNote::class,
                    $note->id,
                    'details',
                    app()->getLocale()
                );
            }

            DB::commit();

            return ApiResponse::success([
                'note' => new VehicleNoteResource($note),
            ], 'Note updated successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to update note: '.$e->getMessage(), 500);
        }
    }

    public function destroy(Request $request, int $vehicleId, int $noteId): JsonResponse
    {
        try {
            DB::beginTransaction();

            $note = VehicleNote::find($noteId);
            if (! $note) {
                DB::rollBack();

                return ApiResponse::error('Note not found', 404);
            }

            if (! $request->user()->can('delete', $note)) {
                DB::rollBack();

                return ApiResponse::error('You do not have permission to delete this note', 403);
            }

            $deleted = $this->vehicleNoteService->delete($noteId);

            if (! $deleted) {
                DB::rollBack();

                return ApiResponse::error('Failed to delete note', 500);
            }

            DB::commit();

            return ApiResponse::success([], 'Note deleted successfully');
        } catch (\Exception $e) {
            DB::rollBack();

            return ApiResponse::error('Failed to delete note: '.$e->getMessage(), 500);
        }
    }
}
