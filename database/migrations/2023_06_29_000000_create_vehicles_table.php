<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('vehicles', function (Blueprint $table) {
            $table->id();
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->string('name');
            $table->string('make_brand');
            $table->string('model_year');
            $table->string('vin_number')->nullable()->unique();
            $table->string('chassis_number')->nullable()->unique();
            $table->integer('mileage')->nullable();
            $table->string('vehicle_type');
            $table->string('plate_number')->nullable();
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('vehicles');
    }
};
