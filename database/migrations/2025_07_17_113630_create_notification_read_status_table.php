    <?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('notification_read_status', function (Blueprint $table) {
            $table->id();
            $table->foreignId('notification_id')->constrained()->onDelete('cascade');
            $table->foreignId('user_id')->constrained()->onDelete('cascade');
            $table->boolean('is_read')->default(false);
            $table->timestamp('read_at')->nullable();
            $table->timestamps();

            // Unique constraint to prevent duplicate entries
            $table->unique(['notification_id', 'user_id']);

            // Indexes for performance
            $table->index(['user_id', 'is_read']);
            $table->index(['notification_id', 'is_read']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('notification_read_status');
    }
};
