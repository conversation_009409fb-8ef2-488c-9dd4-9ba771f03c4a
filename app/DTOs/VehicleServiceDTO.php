<?php

namespace App\DTOs;

class VehicleServiceDTO
{
    public function __construct(
        public readonly int $vehicle_id,
        public readonly string $service_type,
        public readonly string $service_date,
        public readonly string $maintenance_time,
        public readonly int $mileage,
        public readonly float $cost,
        public readonly ?array $review = null
    ) {}

    public function toArray(): array
    {
        return [
            'vehicle_id' => $this->vehicle_id,
            'service_type' => $this->service_type,
            'service_date' => $this->service_date,
            'maintenance_time' => $this->maintenance_time,
            'mileage' => $this->mileage,
            'cost' => $this->cost,
            'review' => $this->review,
        ];
    }
}
