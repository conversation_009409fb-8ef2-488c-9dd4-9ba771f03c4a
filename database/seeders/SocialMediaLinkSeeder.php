<?php

namespace Database\Seeders;

use App\Enums\SocialMediaType;
use App\Models\SocialMediaLink;
use Illuminate\Database\Seeder;

class SocialMediaLinkSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Clear existing data first
        SocialMediaLink::truncate();

        // Create sample social media links
        $socialMediaLinks = [
            [
                'type' => SocialMediaType::FACEBOOK->value,
                'title' => 'Official Facebook Page',
                'url' => 'https://www.facebook.com/nabih',
                'username' => 'nabih',
                'description' => 'Follow us on Facebook for the latest updates',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::TWITTER->value,
                'title' => 'Twitter Profile',
                'url' => 'https://twitter.com/nabih',
                'username' => 'nabih',
                'description' => 'Follow us on Twitter for news and announcements',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::INSTAGRAM->value,
                'title' => 'Instagram Page',
                'url' => 'https://www.instagram.com/nabih',
                'username' => 'nabih',
                'description' => 'Check out our Instagram for photos and stories',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::LINKEDIN->value,
                'title' => 'LinkedIn Company Page',
                'url' => 'https://www.linkedin.com/company/nabih',
                'username' => 'nabih',
                'description' => 'Connect with us on LinkedIn',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::YOUTUBE->value,
                'title' => 'YouTube Channel',
                'url' => 'https://www.youtube.com/c/nabih',
                'username' => 'nabih',
                'description' => 'Subscribe to our YouTube channel for video content',
                'is_active' => false,
            ],
            [
                'type' => SocialMediaType::WHATSAPP->value,
                'title' => 'WhatsApp Business',
                'url' => 'https://wa.me/966551234567',
                'username' => '+966 55 123 4567',
                'description' => 'Contact us on WhatsApp for quick support',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::EMAIL->value,
                'title' => 'Email',
                'url' => 'https://www.nabih.com',
                'username' => 'nabih',
                'description' => 'Contact us via email',
                'is_active' => true,
            ],
            [
                'type' => SocialMediaType::PHONE->value,
                'title' => 'Phone',
                'url' => 'https://www.nabih.com',
                'username' => 'nabih',
                'description' => 'Contact us via phone',
                'is_active' => true,
            ],
        ];

        foreach ($socialMediaLinks as $link) {
            SocialMediaLink::create($link);
        }
    }
}
