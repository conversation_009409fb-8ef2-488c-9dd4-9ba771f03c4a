<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('onboarding_screens', function (Blueprint $table) {
            $table->id();
            $table->integer('order')->default(1);
            $table->string('image')->nullable();
            $table->boolean('is_active')->default(true);
            $table->timestamps();
        });

        Schema::create('onboarding_screen_translations', function (Blueprint $table) {
            $table->id();
            $table->foreignId('onboarding_screen_id')->constrained()->cascadeOnDelete();
            $table->string('locale', 5);
            $table->string('title');
            $table->text('description');
            $table->timestamps();

            $table->unique(['onboarding_screen_id', 'locale'], 'os_trans_screen_locale_unique');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('onboarding_screen_translations');
        Schema::dropIfExists('onboarding_screens');
    }
};
