<?php

namespace App\Http\Requests\Vehicle;

use App\Http\Requests\BaseApiRequest;
use App\Models\Vehicle;
use Illuminate\Validation\Rule;

class UpdateVehicleRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        $vehicleId = $this->route('id');
        $vehicle = Vehicle::findOrFail($vehicleId);

        return $this->user()->can('update', $vehicle);
    }

    public function rules(): array
    {
        return [
            'name' => 'sometimes|string|max:255',
            'make_brand' => 'sometimes|string|max:255',
            'model_year' => 'sometimes|string|max:10',
            'vin_number' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('vehicles', 'vin_number')->ignore($this->route('id')),
            ],
            'chassis_number' => [
                'nullable',
                'string',
                'max:255',
                Rule::unique('vehicles', 'chassis_number')->ignore($this->route('id')),
            ],
            'mileage' => 'nullable|integer',
            'vehicle_type' => 'sometimes|string|max:50',
            'plate_number' => 'nullable|string|max:50',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'name.string' => 'يجب أن يكون اسم المركبة نصًا',
                'name.max' => 'يجب ألا يتجاوز اسم المركبة 255 حرفًا',
                'make_brand.string' => 'يجب أن تكون الماركة/البراند نصًا',
                'make_brand.max' => 'يجب ألا تتجاوز الماركة/البراند 255 حرفًا',
                'model_year.string' => 'يجب أن تكون سنة الموديل نصًا',
                'model_year.max' => 'يجب ألا تتجاوز سنة الموديل 10 أحرف',
                'vin_number.string' => 'يجب أن يكون رقم VIN نصًا',
                'vin_number.max' => 'يجب ألا يتجاوز رقم VIN 255 حرفًا',
                'vin_number.unique' => 'رقم VIN مسجل بالفعل مع مركبة أخرى',
                'chassis_number.string' => 'يجب أن يكون رقم الشاسيه نصًا',
                'chassis_number.max' => 'يجب ألا يتجاوز رقم الشاسيه 255 حرفًا',
                'chassis_number.unique' => 'رقم الشاسيه مسجل بالفعل مع مركبة أخرى',
                'mileage.integer' => 'يجب أن تكون المسافة المقطوعة رقمًا',
                'vehicle_type.string' => 'يجب أن يكون نوع المركبة نصًا',
                'vehicle_type.max' => 'يجب ألا يتجاوز نوع المركبة 50 حرفًا',
                'plate_number.string' => 'يجب أن يكون رقم اللوحة نصًا',
                'plate_number.max' => 'يجب ألا يتجاوز رقم اللوحة 50 حرفًا',
            ];
        }

        return [
            'name.string' => 'Vehicle name must be a string',
            'name.max' => 'Vehicle name must not exceed 255 characters',
            'make_brand.string' => 'Make/brand must be a string',
            'make_brand.max' => 'Make/brand must not exceed 255 characters',
            'model_year.string' => 'Model year must be a string',
            'model_year.max' => 'Model year must not exceed 10 characters',
            'vin_number.string' => 'VIN number must be a string',
            'vin_number.max' => 'VIN number must not exceed 255 characters',
            'vin_number.unique' => 'This VIN number is already registered with another vehicle',
            'chassis_number.string' => 'Chassis number must be a string',
            'chassis_number.max' => 'Chassis number must not exceed 255 characters',
            'chassis_number.unique' => 'This chassis number is already registered with another vehicle',
            'mileage.integer' => 'Mileage must be a number',
            'vehicle_type.string' => 'Vehicle type must be a string',
            'vehicle_type.max' => 'Vehicle type must not exceed 50 characters',
            'plate_number.string' => 'Plate number must be a string',
            'plate_number.max' => 'Plate number must not exceed 50 characters',
        ];
    }
}
