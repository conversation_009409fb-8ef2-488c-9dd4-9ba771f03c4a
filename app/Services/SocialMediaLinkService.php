<?php

namespace App\Services;

use App\Enums\SocialMediaType;
use App\Models\SocialMediaLink;
use Illuminate\Pagination\LengthAwarePaginator;

class SocialMediaLinkService
{
    public function getPaginatedList(int $perPage = 15, array $filters = []): LengthAwarePaginator
    {
        $query = SocialMediaLink::query();

        // Apply filters if provided
        if (isset($filters['type']) && $filters['type']) {
            $query->where('type', $filters['type']);
        }

        if (isset($filters['is_active']) && $filters['is_active'] !== null) {
            $query->where('is_active', $filters['is_active']);
        }

        if (isset($filters['search']) && $filters['search']) {
            $search = $filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('title', 'like', "%{$search}%")
                    ->orWhere('url', 'like', "%{$search}%")
                    ->orWhere('username', 'like', "%{$search}%");
            });
        }

        // Order by creation date
        return $query->orderBy('created_at', 'desc')
            ->paginate($perPage);
    }

    public function getActive()
    {
        return SocialMediaLink::where('is_active', true)
            ->orderBy('created_at')
            ->get();
    }

    public function findById(int $id): ?SocialMediaLink
    {
        return SocialMediaLink::find($id);
    }

    public function store(array $data): SocialMediaLink
    {
        return SocialMediaLink::create($data);
    }

    public function update(int $id, array $data): ?SocialMediaLink
    {
        $socialMediaLink = $this->findById($id);

        if (! $socialMediaLink) {
            return null;
        }

        $socialMediaLink->update($data);

        return $socialMediaLink->fresh();
    }

    public function delete(int $id): bool
    {
        $socialMediaLink = $this->findById($id);

        if (! $socialMediaLink) {
            return false;
        }

        return $socialMediaLink->delete();
    }

    public function getSocialMediaTypeOptions(): array
    {
        return SocialMediaType::options();
    }
}
