<?php

namespace App\Enums;

enum NotificationType: string
{
    case REMINDER = 'reminder';
    case OFFER = 'offer';

    // Admin notification types
    case VENDOR_REGISTRATION = 'vendor_registration';
    case VENDOR_APPROVAL_REQUEST = 'vendor_approval_request';
    case CUSTOMER_SUPPORT_REQUEST = 'customer_support_request';
    case SYSTEM_ALERT = 'system_alert';

    /**
     * Get all available notification types
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get display name for frontend
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::REMINDER => 'Vehicle Reminder',
            self::OFFER => 'Offer Notification',
            self::VENDOR_REGISTRATION => 'New Vendor Registration',
            self::VENDOR_APPROVAL_REQUEST => 'Vendor Approval Request',
            self::CUSTOMER_SUPPORT_REQUEST => 'Customer Support Request',
            self::SYSTEM_ALERT => 'System Alert',
        };
    }

    /**
     * Get icon class for frontend
     */
    public function getIconClass(): string
    {
        return match ($this) {
            self::REMINDER => 'fas fa-bell',
            self::OFFER => 'fas fa-gift',
            self::VENDOR_REGISTRATION => 'fas fa-user-plus',
            self::VENDOR_APPROVAL_REQUEST => 'fas fa-user-check',
            self::CUSTOMER_SUPPORT_REQUEST => 'fas fa-headset',
            self::SYSTEM_ALERT => 'fas fa-exclamation-triangle',
        };
    }
}
