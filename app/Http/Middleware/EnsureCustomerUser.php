<?php

namespace App\Http\Middleware;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Models\User;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class EnsureCustomerUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response)  $next
     */
    public function handle(Request $request, Closure $next): Response
    {
        // First check if user is authenticated
        if ($request->user()) {
            // If user is authenticated, verify it's a customer
            if (! $request->user()->isCustomer()) {
                return ApiResponse::error('This account is not registered as a customer. Please log in with a customer account or contact support.', 403);
            }

            // If user is a customer, allow access
            return $next($request);
        }

        // For non-authenticated routes, check phone_number or email
        if ($request->has('phone_number')) {
            $user = User::where('phone_number', $request->phone_number)->first();

            if ($user && $user->user_type !== UserType::CUSTOMER) {
                return ApiResponse::error('This account is not registered as a customer. Please log in with a customer account or contact support.', 403);
            }
        } elseif ($request->has('email')) {
            $user = User::where('email', $request->email)->first();

            if ($user && $user->user_type !== UserType::CUSTOMER) {
                return ApiResponse::error('This account is not registered as a customer. Please log in with a customer account or contact support.', 403);
            }
        }

        return $next($request);
    }
}
