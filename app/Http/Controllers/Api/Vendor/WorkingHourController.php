<?php

namespace App\Http\Controllers\Api\Vendor;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Vendor\UpdateWorkingHoursRequest;
use App\Http\Resources\Vendor\WorkingHourResource;
use App\Models\Workshop;
use App\Services\VendorWorkshopService;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Gate;

class WorkingHourController extends Controller
{
    protected $vendorWorkshopService;

    public function __construct(VendorWorkshopService $vendorWorkshopService)
    {
        $this->vendorWorkshopService = $vendorWorkshopService;
    }

    public function index(Workshop $workshop): JsonResponse
    {
        if (Gate::denies('view', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        $workingHours = $this->vendorWorkshopService->getWorkingHours($workshop);

        return ApiResponse::success([
            'working_hours' => WorkingHourResource::collection($workingHours),
        ], 'Working hours retrieved successfully');
    }

    public function update(UpdateWorkingHoursRequest $request, Workshop $workshop): JsonResponse
    {
        if (Gate::denies('update', $workshop)) {
            return ApiResponse::error('Unauthorized access to this workshop', 403);
        }

        // Convert request to collection of DTOs
        $workingHourDTOs = $request->toDTO($workshop->id);

        $this->vendorWorkshopService->updateWorkingHours($workshop, $workingHourDTOs);

        $updatedHours = $this->vendorWorkshopService->getWorkingHours($workshop);

        return ApiResponse::success([
            'working_hours' => WorkingHourResource::collection($updatedHours),
        ], 'Working hours updated successfully');
    }
}
