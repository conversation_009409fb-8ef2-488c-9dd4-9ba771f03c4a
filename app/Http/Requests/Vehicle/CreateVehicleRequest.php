<?php

namespace App\Http\Requests\Vehicle;

use App\Helpers\ApiResponse;
use App\Http\Requests\BaseApiRequest;
use Illuminate\Http\Exceptions\HttpResponseException;

class CreateVehicleRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    protected function failedAuthorization()
    {
        throw new HttpResponseException(
            ApiResponse::error('You do not have permission to create a vehicle', 403)
        );
    }

    public function rules(): array
    {
        return [
            'name' => 'required|string|max:255',
            'make_brand' => 'required|string|max:255',
            'model_year' => 'required|string|max:10',
            'vin_number' => 'nullable|string|max:255|unique:vehicles,vin_number',
            'chassis_number' => 'nullable|string|max:255|unique:vehicles,chassis_number',
            'mileage' => 'nullable|integer',
            'vehicle_type' => 'required|string|max:50',
            'plate_number' => 'nullable|string|max:50',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'name.required' => 'اسم المركبة مطلوب',
                'name.string' => 'يجب أن يكون اسم المركبة نصًا',
                'name.max' => 'يجب ألا يتجاوز اسم المركبة 255 حرفًا',
                'make_brand.required' => 'الماركة/البراند مطلوب',
                'make_brand.string' => 'يجب أن تكون الماركة/البراند نصًا',
                'make_brand.max' => 'يجب ألا تتجاوز الماركة/البراند 255 حرفًا',
                'model_year.required' => 'سنة الموديل مطلوبة',
                'model_year.string' => 'يجب أن تكون سنة الموديل نصًا',
                'model_year.max' => 'يجب ألا تتجاوز سنة الموديل 10 أحرف',
                'vin_number.string' => 'يجب أن يكون رقم VIN نصًا',
                'vin_number.max' => 'يجب ألا يتجاوز رقم VIN 255 حرفًا',
                'vin_number.unique' => 'رقم VIN موجود بالفعل',
                'chassis_number.string' => 'يجب أن يكون رقم الشاسيه نصًا',
                'chassis_number.max' => 'يجب ألا يتجاوز رقم الشاسيه 255 حرفًا',
                'chassis_number.unique' => 'رقم الشاسيه موجود بالفعل',
                'mileage.integer' => 'يجب أن تكون المسافة المقطوعة عددًا صحيحًا',
                'vehicle_type.required' => 'نوع المركبة مطلوب',
                'vehicle_type.string' => 'يجب أن يكون نوع المركبة نصًا',
                'vehicle_type.max' => 'يجب ألا يتجاوز نوع المركبة 50 حرفًا',
                'plate_number.string' => 'يجب أن يكون رقم اللوحة نصًا',
                'plate_number.max' => 'يجب ألا يتجاوز رقم اللوحة 50 حرفًا',
            ];
        }

        return [
            'name.required' => 'Vehicle name is required',
            'name.string' => 'Vehicle name must be a string',
            'name.max' => 'Vehicle name must not exceed 255 characters',
            'make_brand.required' => 'Make/brand is required',
            'make_brand.string' => 'Make/brand must be a string',
            'make_brand.max' => 'Make/brand must not exceed 255 characters',
            'model_year.required' => 'Model year is required',
            'model_year.string' => 'Model year must be a string',
            'model_year.max' => 'Model year must not exceed 10 characters',
            'vin_number.string' => 'VIN number must be a string',
            'vin_number.max' => 'VIN number must not exceed 255 characters',
            'vin_number.unique' => 'This VIN number is already registered with another vehicle',
            'chassis_number.string' => 'Chassis number must be a string',
            'chassis_number.max' => 'Chassis number must not exceed 255 characters',
            'chassis_number.unique' => 'This chassis number is already registered with another vehicle',
            'mileage.integer' => 'Mileage must be an integer',
            'vehicle_type.required' => 'Vehicle type is required',
            'vehicle_type.string' => 'Vehicle type must be a string',
            'vehicle_type.max' => 'Vehicle type must not exceed 50 characters',
            'plate_number.string' => 'Plate number must be a string',
            'plate_number.max' => 'Plate number must not exceed 50 characters',
            'mileage.integer' => 'Mileage must be a number',
            'vehicle_type.required' => 'Vehicle type is required',
            'vehicle_type.string' => 'Vehicle type must be a string',
            'vehicle_type.max' => 'Vehicle type must not exceed 50 characters',
            'plate_number.string' => 'Plate number must be a string',
            'plate_number.max' => 'Plate number must not exceed 50 characters',
        ];
    }
}
