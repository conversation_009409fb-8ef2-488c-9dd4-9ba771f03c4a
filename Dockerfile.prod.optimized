FROM php:8.4-fpm

# Install system dependencies
RUN apt-get update && apt-get install -y \
    git \
    curl \
    zip \
    unzip \
    supervisor

# Install PHP extension dependencies - this layer can be cached
RUN apt-get update && apt-get install -y \
    libpng-dev \
    libonig-dev \
    libxml2-dev \
    libzip-dev \
    libexif-dev \
    build-essential \
    autoconf \
    libtool \
    pkg-config \
    zlib1g-dev \
    libjpeg-dev \
    libfreetype-dev

# Configure PHP settings for Composer
RUN echo "allow_url_fopen = On\n\
proc_open.enable = On\n\
memory_limit = 2G" > /usr/local/etc/php/conf.d/composer-settings.ini

# Install PHP extensions
RUN docker-php-ext-configure gd --with-freetype --with-jpeg \
    && docker-php-ext-install -j$(nproc) gd zip pdo pdo_mysql mbstring exif pcntl bcmath

# Install Redis extension
RUN pecl install redis && docker-php-ext-enable redis

# Install gRPC extension
RUN pecl install grpc && docker-php-ext-enable grpc

# Install Node.js and npm
RUN curl -fsSL https://deb.nodesource.com/setup_22.x | bash - \
    && apt-get install -y nodejs \
    && npm install -g npm

# Clear cache
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /var/www

# Install Composer and make it executable
COPY --from=composer:2.8 /usr/bin/composer /usr/bin/composer
RUN chmod +x /usr/bin/composer && composer --version

# Create supervisor directory and configuration
RUN mkdir -p /etc/supervisor/conf.d/

# Copy supervisor configuration
COPY docker/supervisor/supervisord.conf /etc/supervisor/conf.d/supervisor.conf
COPY docker/supervisor/conf.d/certbot-renewal.conf /etc/supervisor/conf.d/certbot-renewal.conf

# Copy certbot renewal script
COPY docker/scripts/certbot-renewal.sh /usr/local/bin/certbot-renewal.sh
RUN chmod +x /usr/local/bin/certbot-renewal.sh

# Copy startup script
COPY docker/startup.sh /usr/local/bin/startup.sh
RUN chmod +x /usr/local/bin/startup.sh

# Expose port 9000 and start php-fpm server
EXPOSE 9000

# The application code will be mounted as a volume
# Start with our custom startup script
CMD ["/usr/local/bin/startup.sh"]
