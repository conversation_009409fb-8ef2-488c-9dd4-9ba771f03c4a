<?php

namespace Database\Seeders;

use App\Enums\SupportRequestIssueType;
use App\Enums\UserType;
use App\Models\SupportRequest;
use App\Models\User;
use Illuminate\Database\Seeder;

class SupportRequestSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all customers
        $customers = User::where('user_type', UserType::CUSTOMER->value)->take(5)->get();

        if ($customers->isEmpty()) {
            $this->command->info('No customers found, skipping support request seeding.');

            return;
        }

        $issueTypes = [
            \App\Enums\SupportRequestIssueType::INQUIRY,
            \App\Enums\SupportRequestIssueType::COMPLAINT,
            \App\Enums\SupportRequestIssueType::BUG_REPORT,
            \App\Enums\SupportRequestIssueType::FEATURE_REQUEST,
            \App\Enums\SupportRequestIssueType::OTHER,
        ];
        $statuses = [
            \App\Enums\SupportRequestStatus::PENDING,
            \App\Enums\SupportRequestStatus::OPEN,
            \App\Enums\SupportRequestStatus::CLOSED,
        ];

        // Create sample support requests for each customer
        foreach ($customers as $index => $customer) {
            // Create 2-3 support requests per customer
            $requestCount = rand(2, 3);

            for ($i = 1; $i <= $requestCount; $i++) {
                $requestId = 'REQ-'.str_pad(($index * 3) + $i, 2, '0', STR_PAD_LEFT);
                $issueType = $issueTypes[array_rand($issueTypes)];
                $status = $statuses[array_rand($statuses)];

                $supportRequest = SupportRequest::create([
                    'request_id' => $requestId,
                    'user_id' => $customer->id,
                    'subject' => $this->getSubjectForIssueType($issueType),
                    'issue_type' => $issueType,
                    'details' => $this->getDetailsForIssueType($issueType),
                    'status' => $status,
                ]);

                // Add admin response for some requests
                if ($status !== \App\Enums\SupportRequestStatus::OPEN) {
                    $supportRequest->admin_response = $this->getAdminResponse($status);
                    $supportRequest->save();
                }
            }
        }

        $this->command->info('Support requests seeded successfully.');
    }

    /**
     * Get a sample subject based on issue type.
     */
    private function getSubjectForIssueType(\App\Enums\SupportRequestIssueType $issueType): string
    {
        $subjects = [
            \App\Enums\SupportRequestIssueType::INQUIRY->value => [
                'Login Issues After Password Reset',
                'Vehicle Service History Access',
                'Registration Help',
                'Download Service Records',
            ],
            \App\Enums\SupportRequestIssueType::COMPLAINT->value => [
                'Vehicle Service Quality Issue',
                'Pricing Discrepancy',
                'Staff Behavior Complaint',
                'Delayed Service Completion',
            ],
            \App\Enums\SupportRequestIssueType::BUG_REPORT->value => [
                'App Crashes on Profile Page',
                'Service History Not Loading',
                'Payment Processing Error',
                'Order Status Not Updated',
                'Incorrect Information Displayed',
            ],
            \App\Enums\SupportRequestIssueType::FEATURE_REQUEST->value => [
                'Request for Mobile App',
                'Integration with Vehicle Tracking',
                'Automated Service Reminders',
            ],
            \App\Enums\SupportRequestIssueType::OTHER->value => [
                'General Inquiry',
                'Account Information Update',
                'Service Partner Recommendation',
            ],
        ];

        $typeSubjects = $subjects[$issueType->value] ?? $subjects[\App\Enums\SupportRequestIssueType::INQUIRY->value];

        return $typeSubjects[array_rand($typeSubjects)];
    }

    /**
     * Get sample details based on issue type.
     */
    private function getDetailsForIssueType(\App\Enums\SupportRequestIssueType $issueType): string
    {
        $details = [
            SupportRequestIssueType::INQUIRY->value => [
                'Unable to log in to account after password reset. I tried multiple times but keep getting an error message.',
                'How can I access my complete vehicle service history? I need it for insurance purposes.',
                'I need help registering my second vehicle in the system.',
                'Is there a way to download all my service records as a PDF?',
            ],
            \App\Enums\SupportRequestIssueType::COMPLAINT->value => [
                'The last service I received was of poor quality. My vehicle is making strange noises again.',
                'I was charged more than the quoted price for my last service.',
                'One of your staff members was rude and unhelpful when I visited last week.',
                'My service appointment was delayed by over 2 hours without any notification.',
            ],
            \App\Enums\SupportRequestIssueType::BUG_REPORT->value => [
                'The app crashes every time I try to view my profile.',
                'My service history is not loading in the app.',
                'I get an error message when trying to process a payment.',
                'My order status has not been updated for 3 days now.',
                'The information displayed about my vehicle is incorrect.',
            ],
            \App\Enums\SupportRequestIssueType::FEATURE_REQUEST->value => [
                'I would like to request a mobile app for your service.',
                'Please integrate vehicle tracking in the app so I can see my vehicle status in real-time.',
                'It would be helpful to receive automated service reminders via SMS or email.',
            ],
            \App\Enums\SupportRequestIssueType::OTHER->value => [
                'I have a general question about your services.',
                'I need to update my account information.',
                'Can you recommend a good service partner in my area?',
            ],
        ];

        $typeDetails = $details[$issueType->value] ?? $details[SupportRequestIssueType::INQUIRY->value];

        return $typeDetails[array_rand($typeDetails)];
    }

    /**
     * Get sample admin response based on status.
     */
    private function getAdminResponse(\App\Enums\SupportRequestStatus $status): string
    {
        $responses = [
            \App\Enums\SupportRequestStatus::PENDING->value => [
                'We have received your support request and will review it shortly. Thank you for your patience.',
                'Your support request has been logged in our system and is pending review by our team.',
                'Thanks for contacting support. Your request is in our queue and will be addressed soon.',
            ],
            \App\Enums\SupportRequestStatus::CLOSED->value => [
                'We have resolved your issue as requested. If you need further assistance, please open a new support request.',
                'Your support request has been closed as the issue has been resolved. We appreciate your feedback.',
                'This issue has been resolved and the ticket is now closed. Thank you for your patience.',
            ],
        ];

        return $responses[$status->value][array_rand($responses[$status->value])];
    }
}
