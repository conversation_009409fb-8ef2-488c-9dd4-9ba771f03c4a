<?php

namespace Database\Seeders;

use App\Enums\UserType;
use App\Models\User;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class VendorSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create vendor role if it doesn't exist
        $vendorRole = Role::firstOrCreate(['name' => 'vendor']);

        // Create vendor permissions if they don't exist
        $vendorPermissions = [
            'workshop.view',
            'workshop.create',
            'workshop.update',
            'workshop.delete',
            'workshop.services',
            'workshop.hours',
        ];

        // Create each permission if it doesn't exist
        foreach ($vendorPermissions as $permission) {
            Permission::firstOrCreate(['name' => $permission]);
        }

        // Assign permissions to vendor role
        $vendorRole->syncPermissions($vendorPermissions);

        // Create some demo vendors if they don't exist
        $demoVendors = [
            [
                'name' => 'Demo Vendor 1',
                'email' => '<EMAIL>',
                'phone_number' => '+9665000000001',
                'password' => 'Vendor@123',
            ],
            [
                'name' => 'Demo Vendor 2',
                'email' => '<EMAIL>',
                'phone_number' => '+9665000000002',
                'password' => 'Vendor@123',
            ],
            [
                'name' => 'Demo Vendor 3',
                'email' => '<EMAIL>',
                'phone_number' => '+9665000000003',
                'password' => 'Vendor@123',
            ],
        ];

        $count = 0;
        foreach ($demoVendors as $vendorData) {
            $vendor = User::where('email', $vendorData['email'])->first();

            if (! $vendor) {
                $vendor = User::create([
                    'name' => $vendorData['name'],
                    'email' => $vendorData['email'],
                    'phone_number' => $vendorData['phone_number'],
                    'password' => bcrypt($vendorData['password']),
                    'user_type' => UserType::VENDOR->value,
                    'is_verified' => true,
                ]);

                $vendor->assignRole($vendorRole);
                $count++;
            }
        }

        if ($count > 0) {
            $this->command->info("Created {$count} demo vendors with proper permissions.");
        } else {
            $this->command->info('Demo vendors already exist.');
        }
    }
}
