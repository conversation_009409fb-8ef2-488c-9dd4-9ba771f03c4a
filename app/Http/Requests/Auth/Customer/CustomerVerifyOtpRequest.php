<?php

namespace App\Http\Requests\Auth\Customer;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\Otp;
use App\Models\User;

class CustomerVerifyOtpRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'phone_number' => 'required|string|regex:/^\+[1-9]\d{1,14}$/|exists:otps,phone_number',
            'otp' => [
                'required',
                'string',
                'size:4',
                function ($attribute, $value, $fail) {
                    if (! Otp::where('phone_number', $this->phone_number)
                        ->where('otp', $value)
                        ->where('expires_at', '>', now())
                        ->exists()) {
                        $locale = app()->getLocale();

                        if ($locale === 'ar') {
                            $fail('انتهت صلاحية رمز التحقق أو غير موجود');
                        } else {
                            $fail('Invalid or expired OTP. Try again.');
                        }
                    }
                },
            ],
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'phone_number.required' => 'رقم الهاتف مطلوب',
                'phone_number.regex' => 'يرجى إدخال رقم هاتف دولي صالح بتنسيق E.164 (مثال: +966501234567)',
                'phone_number.exists' => 'لم يتم إرسال رمز OTP إلى هذا الرقم',
                'otp.required' => 'رمز التحقق مطلوب',
                'otp.size' => 'يجب أن يتكون رمز التحقق من 4 أرقام',
                'otp.numeric' => 'يجب أن يكون رمز التحقق أرقامًا فقط',
            ];
        }

        return [
            'phone_number.required' => 'Phone number is required',
            'phone_number.regex' => 'Please enter a valid international phone number in E.164 format (e.g. +966501234567)',
            'phone_number.exists' => 'OTP has not been sent to this phone number',
            'otp.required' => 'OTP is required',
            'otp.size' => 'OTP must be 4 digits',
            'otp.numeric' => 'OTP must be numeric',
        ];
    }

    protected function afterValidation()
    {
        $user = User::where('phone_number', $this->phone_number)->first();

        if (! $user || $user->user_type !== UserType::CUSTOMER) {
            $validator = \Illuminate\Support\Facades\Validator::make($this->all(), []);
            $validator->errors()->add('phone_number', 'The User is not a customer');
            $this->failedValidation($validator);
        }
    }
}
