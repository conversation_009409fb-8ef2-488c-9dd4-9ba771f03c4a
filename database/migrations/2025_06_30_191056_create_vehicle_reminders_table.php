<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('vehicle_reminders', function (Blueprint $table) {
            $table->id();
            $table->foreignId('vehicle_id')->constrained('vehicles')->onDelete('cascade');
            $table->string('reminder_type');
            $table->boolean('is_mileage_based')->default(false);
            $table->date('reminder_date')->nullable();
            $table->integer('mileage')->nullable();
            $table->json('review')->nullable(); // Stores translations in JSON format: {"en": "English text", "ar": "Arabic text"}
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('vehicle_reminders');
    }
};
