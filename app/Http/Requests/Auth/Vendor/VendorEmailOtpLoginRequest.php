<?php

namespace App\Http\Requests\Auth\Vendor;

use App\Enums\UserType;
use App\Http\Requests\BaseApiRequest;
use App\Models\User;
use Illuminate\Support\Facades\Validator;

class VendorEmailOtpLoginRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'email' => 'required|string|email|exists:users,email',
        ];
    }

    public function messages(): array
    {
        $locale = app()->getLocale();

        if ($locale === 'ar') {
            return [
                'email.required' => 'البريد الإلكتروني مطلوب',
                'email.email' => 'يرجى إدخال بريد إلكتروني صالح',
                'email.exists' => 'هذا البريد الإلكتروني غير مسجل',
            ];
        }

        return [
            'email.required' => 'Email is required',
            'email.email' => 'Please enter a valid email address',
            'email.exists' => 'This email is not registered',
        ];
    }

    protected function afterValidation()
    {
        // Verify that the email belongs to a vendor
        $user = User::where('email', $this->email)
            ->where('user_type', UserType::VENDOR)
            ->first();

        if (! $user) {
            $validator = Validator::make($this->all(), []);
            $validator->errors()->add('email', 'This email is not registered as a vendor account');
            $this->failedValidation($validator);
        }
    }
}
