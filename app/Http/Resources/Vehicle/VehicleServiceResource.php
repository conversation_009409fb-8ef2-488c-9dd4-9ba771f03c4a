<?php

namespace App\Http\Resources\Vehicle;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VehicleServiceResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $review = $this->review;
        if (is_string($review)) {
            $review = json_decode($review, true);
        }

        return [
            'id' => $this->id,
            'service_type' => $this->service_type,
            'service_date' => $this->service_date,
            'maintenance_time' => $this->maintenance_time,
            'mileage' => $this->mileage,
            'cost' => $this->cost,
            'review' => $review,
        ];
    }
}
