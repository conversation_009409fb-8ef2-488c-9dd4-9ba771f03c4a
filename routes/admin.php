<?php

use App\Http\Controllers\Admin\AdminManagementController;
use App\Http\Controllers\Admin\AdminNotificationController;
use App\Http\Controllers\Admin\CustomerManagementController;
use App\Http\Controllers\Admin\DashboardController;
use App\Http\Controllers\Admin\FaqController;
use App\Http\Controllers\Admin\OfferManagementController;
use App\Http\Controllers\Admin\OnboardingScreenController;
use App\Http\Controllers\Admin\PermissionManagementController;
use App\Http\Controllers\Admin\RoleManagementController;
use App\Http\Controllers\Admin\SocialMediaLinkController;
use App\Http\Controllers\Admin\StaticContentController;
use App\Http\Controllers\Admin\SupportRequestManagementController;
use App\Http\Controllers\Admin\VehicleManagementController;
use App\Http\Controllers\Admin\VendorApprovalController;
use App\Http\Controllers\Admin\VendorManagementController;
use Illuminate\Support\Facades\Route;

Route::prefix('content')->group(function () {
    Route::get('/onboarding-screens', [OnboardingScreenController::class, 'index']);
    Route::get('/faqs', [FaqController::class, 'index']);
    Route::get('/static-content', [StaticContentController::class, 'index']);
    Route::get('/static-content/type/{type}/user-type/{userType}', [StaticContentController::class, 'getContentByType']);
    Route::get('/social-media-links', [SocialMediaLinkController::class, 'index']);
});

Route::middleware('auth:sanctum')->group(function () {

    // Route::prefix('content')->group(function () {
    //     Route::get('/onboarding-screens', [OnboardingScreenController::class, 'index']);
    //     Route::get('/faqs', [FaqController::class, 'index']);
    //     Route::get('/static-content', [StaticContentController::class, 'index']);
    //     Route::get('/static-content/type/{type}/user-type/{userType}', [StaticContentController::class, 'getContentByType']);
    //     Route::get('/social-media-links', [SocialMediaLinkController::class, 'index']);
    // });

    // Content Management Routes
    Route::prefix('content')
        ->group(function () {
            // Social Media Links
            Route::prefix('social-media-links')->group(function () {
                Route::get('/types', [SocialMediaLinkController::class, 'getSocialMediaTypes'])->middleware('permission:content.social-media.view.list');
                Route::get('/{id}', [SocialMediaLinkController::class, 'show'])->middleware('permission:content.social-media.view.single');
                Route::post('/', [SocialMediaLinkController::class, 'store'])->middleware('permission:content.social-media.manage');
                Route::put('/{id}', [SocialMediaLinkController::class, 'update'])->middleware('permission:content.social-media.manage');
                Route::delete('/{id}', [SocialMediaLinkController::class, 'destroy'])->middleware('permission:content.social-media.manage');
            });
            // Onboarding Screens
            Route::prefix('onboarding-screens')->group(function () {
                // Route::get('/', [OnboardingScreenController::class, 'index'])->middleware('permission:content.onboarding.view.list');
                Route::get('/{id}', [OnboardingScreenController::class, 'show'])->middleware('permission:content.onboarding.view.single');
                Route::post('/', [OnboardingScreenController::class, 'store'])->middleware('permission:content.onboarding.create');
                Route::post('/reorder', [OnboardingScreenController::class, 'reorder'])->middleware('permission:content.onboarding.reorder');
                Route::put('/{id}', [OnboardingScreenController::class, 'update'])->middleware('permission:content.onboarding.update');
                Route::delete('/{id}', [OnboardingScreenController::class, 'destroy'])->middleware('permission:content.onboarding.delete');
            });

            // FAQs
            Route::prefix('faqs')->group(function () {
                // Route::get('/', [FaqController::class, 'index'])->middleware('permission:content.faq.view.list');
                Route::get('/{id}', [FaqController::class, 'show'])->middleware('permission:content.faq.view.single');
                Route::post('/', [FaqController::class, 'store'])->middleware('permission:content.faq.create');
                Route::post('/reorder', [FaqController::class, 'reorder'])->middleware('permission:content.faq.reorder');
                Route::put('/{id}', [FaqController::class, 'update'])->middleware('permission:content.faq.update');
                Route::delete('/{id}', [FaqController::class, 'destroy'])->middleware('permission:content.faq.delete');

                // Additional specific FAQ routes for customer/vendor type filtering
                Route::get('/customer', [FaqController::class, 'customerFaqs'])->middleware('permission:content.faq.view.customer');
                Route::get('/vendor', [FaqController::class, 'vendorFaqs'])->middleware('permission:content.faq.view.vendor');
            });

            // Static Content Management
            Route::prefix('static-content')->group(function () {
                // Get all static content items
                // Route::get('/', [StaticContentController::class, 'index'])->middleware('permission:content.static.view.list');

                // Get specific static content by ID
                Route::get('/{id}', [StaticContentController::class, 'show'])->middleware('permission:content.static.view.single');

                // Create new static content
                Route::post('/', [StaticContentController::class, 'store'])->middleware('permission:content.static.create');

                // Update existing static content
                Route::put('/{id}', [StaticContentController::class, 'store'])->middleware('permission:content.static.update');

                // Delete static content
                Route::delete('/{id}', [StaticContentController::class, 'destroy'])->middleware('permission:content.static.delete');

                // Get content by type and user type (terms, privacy, about)
                // Route::get('/type/{type}/user-type/{userType}', [StaticContentController::class, 'getContentByType'])->middleware('permission:content.static.view.bytype');
            });
        });

    Route::prefix('admins')
        ->group(function () {
            // Get current admin profile
            Route::get('/profile', [AdminManagementController::class, 'getCurrentAdmin']);
            // Support Request Management Routes
            Route::prefix('support-requests')
                ->middleware('permission:support.view.list')
                ->group(function () {
                    Route::get('/', [SupportRequestManagementController::class, 'listSupportRequests'])->middleware('permission:support.view.list');
                    Route::get('/deleted', [SupportRequestManagementController::class, 'listDeletedSupportRequests'])->middleware('permission:support.view.deleted');
                    Route::get('/{id}', [SupportRequestManagementController::class, 'getSupportRequest'])
                        ->middleware('permission:support.view.single')
                        ->where('id', '[0-9]+');
                    // Route::post('/', [SupportRequestManagementController::class, 'createSupportRequest'])
                    //     ->middleware('permission:support.create');
                    Route::put('/{id}', [SupportRequestManagementController::class, 'updateSupportRequest'])
                        ->middleware('permission:support.update')
                        ->where('id', '[0-9]+');
                    Route::delete('/{id}', [SupportRequestManagementController::class, 'deleteSupportRequest'])
                        ->middleware('permission:support.delete')
                        ->where('id', '[0-9]+');
                    Route::post('/{id}/restore', [SupportRequestManagementController::class, 'restoreSupportRequest'])
                        ->middleware('permission:support.restore')
                        ->where('id', '[0-9]+');
                    Route::delete('/{id}/permanent', [SupportRequestManagementController::class, 'permanentlyDeleteSupportRequest'])
                        ->middleware('permission:support.delete.permanent')
                        ->where('id', '[0-9]+');
                });

            Route::prefix('vehicles')
                ->middleware('permission:vehicle.view.list')
                ->group(function () {
                    Route::get('/users/{userId}', [VehicleManagementController::class, 'listVehiclesByUser'])->middleware('permission:vehicle.view.byuser')->where('userId', '[0-9]+');
                    Route::get('/', [VehicleManagementController::class, 'listVehicles'])->middleware('permission:vehicle.view.list');
                    Route::get('/deleted', [VehicleManagementController::class, 'listDeletedVehicles'])->middleware('permission:vehicle.view.deleted');
                    Route::get('/{id}', [VehicleManagementController::class, 'getVehicle'])->middleware('permission:vehicle.view.single')->where('id', '[0-9]+');
                    Route::post('/', [VehicleManagementController::class, 'createVehicle'])
                        ->middleware('permission:vehicle.create');
                    Route::put('/{id}', [VehicleManagementController::class, 'updateVehicle'])
                        ->middleware('permission:vehicle.update')
                        ->where('id', '[0-9]+');
                    Route::delete('/{id}', [VehicleManagementController::class, 'deleteVehicle'])
                        ->middleware('permission:vehicle.delete')
                        ->where('id', '[0-9]+');
                    Route::post('/{id}/restore', [VehicleManagementController::class, 'restoreVehicle'])
                        ->middleware('permission:vehicle.restore')
                        ->where('id', '[0-9]+');
                    Route::delete('/{id}/permanent', [VehicleManagementController::class, 'permanentlyDeleteVehicle'])
                        ->middleware('permission:vehicle.delete.permanent')
                        ->where('id', '[0-9]+');
                });

            // Dashboard Routes
            Route::prefix('dashboard')
                ->group(function () {
                    Route::get('/statistics', [DashboardController::class, 'getStatistics'])->middleware('permission:dashboard.view.statistics');
                    Route::get('/maintenance-logs', [DashboardController::class, 'getMaintenanceLogs'])->middleware('permission:dashboard.view.maintenance-logs');
                    Route::get('/monthly-maintenance-count', [DashboardController::class, 'getMonthlyMaintenanceCount'])->middleware('permission:dashboard.view.statistics');
                });

            // Define root level routes for admins
            Route::get('/', [AdminManagementController::class, 'listAdmins'])->middleware('permission:admin.view.list');
            Route::get('/deleted', [AdminManagementController::class, 'listDeletedAdmins'])->middleware('permission:admin.view.deleted');
            Route::post('/', [AdminManagementController::class, 'createAdmin'])->middleware('permission:admin.create');
            Route::post('/{id}/restore', [AdminManagementController::class, 'restoreAdmin'])->middleware('permission:admin.restore');
            Route::delete('/{id}/permanent', [AdminManagementController::class, 'permanentlyDeleteAdmin'])->middleware('permission:admin.delete.permanent');

            // Define prefixed group routes BEFORE any wildcard routes
            Route::prefix('roles')
                ->group(function () {
                    Route::get('/', [RoleManagementController::class, 'listRoles'])->middleware('permission:role.view.list'); // For /admins/roles
                    Route::get('/{id}', [RoleManagementController::class, 'getRole'])->middleware('permission:role.view.single');
                    Route::post('/', [RoleManagementController::class, 'createRole'])->middleware('permission:role.create');
                    Route::put('/{id}', [RoleManagementController::class, 'updateRole'])->middleware('permission:role.update');
                    Route::delete('/{id}', [RoleManagementController::class, 'deleteRole'])->middleware('permission:role.delete');
                    Route::post('/{id}/permissions', [RoleManagementController::class, 'assignPermissions'])->middleware('permission:permission.assign');
                    Route::post('/{id}/permissions/revoke', [RoleManagementController::class, 'revokePermissions'])->middleware('permission:permission.revoke');
                });

            Route::prefix('permissions')
                ->group(function () {
                    Route::get('/', [PermissionManagementController::class, 'listPermissions'])->middleware('permission:permission.view.list');
                    Route::get('/{id}', [PermissionManagementController::class, 'getPermission'])->middleware('permission:permission.view.single');
                    Route::post('/', [PermissionManagementController::class, 'createPermission'])->middleware('permission:permission.create');
                    Route::put('/{id}', [PermissionManagementController::class, 'updatePermission'])->middleware('permission:permission.update');
                    Route::delete('/{id}', [PermissionManagementController::class, 'deletePermission'])->middleware('permission:permission.delete');

                    Route::get('/{id}/roles', [PermissionManagementController::class, 'getRolesForPermission'])->middleware('permission:permission.view.single');
                });

            Route::prefix('vendors')
                ->group(function () {
                    Route::get('/', [VendorManagementController::class, 'listVendors'])->middleware('permission:vendor.view.list');
                    Route::get('/deleted', [VendorManagementController::class, 'listDeletedVendors'])->middleware('permission:vendor.view.deleted');
                    Route::get('/{id}', [VendorManagementController::class, 'getVendor'])->middleware('permission:vendor.view.single');
                    Route::post('/', [VendorManagementController::class, 'createVendor'])->middleware('permission:vendor.create');
                    Route::put('/{id}', [VendorManagementController::class, 'updateVendor'])->middleware('permission:vendor.update');
                    Route::delete('/{id}', [VendorManagementController::class, 'deleteVendor'])->middleware('permission:vendor.delete');
                    Route::post('/{id}/restore', [VendorManagementController::class, 'restoreVendor'])->middleware('permission:vendor.restore');
                    Route::delete('/{id}/permanent', [VendorManagementController::class, 'permanentlyDeleteVendor'])->middleware('permission:vendor.delete.permanent');

                    // Vendor approval routes
                    Route::get('/approval/unapproved', [VendorApprovalController::class, 'getUnapprovedVendors'])->middleware('permission:vendor.view.unapproved');
                    Route::get('/approval/approved', [VendorApprovalController::class, 'getApprovedVendors'])->middleware('permission:vendor.view.approved');
                    Route::post('/{vendor}/approve', [VendorApprovalController::class, 'approveVendor'])->middleware('permission:vendor.approve');
                    Route::post('/{vendor}/disapprove', [VendorApprovalController::class, 'disapproveVendor'])->middleware('permission:vendor.disapprove');
                });

            Route::prefix('customers')
                ->group(function () {
                    Route::get('/', [CustomerManagementController::class, 'listCustomers'])->middleware('permission:customer.view.list');
                    Route::get('/deleted', [CustomerManagementController::class, 'listDeletedCustomers'])->middleware('permission:customer.view.deleted');
                    Route::get('/{id}', [CustomerManagementController::class, 'getCustomer'])->middleware('permission:customer.view.single');
                    Route::post('/', [CustomerManagementController::class, 'createCustomer'])->middleware('permission:customer.create');
                    Route::put('/{id}', [CustomerManagementController::class, 'updateCustomer'])->middleware('permission:customer.update');
                    Route::delete('/{id}', [CustomerManagementController::class, 'deleteCustomer'])->middleware('permission:customer.delete');
                    Route::post('/{id}/restore', [CustomerManagementController::class, 'restoreCustomer'])->middleware('permission:customer.restore');
                    Route::delete('/{id}/permanent', [CustomerManagementController::class, 'permanentlyDeleteCustomer'])->middleware('permission:customer.delete.permanent');
                });

            // Add wildcard admin routes AFTER all prefixed group routes to avoid conflicts
            Route::get('/{id}', [AdminManagementController::class, 'getAdmin'])->middleware('permission:admin.view.single')->where('id', '[0-9]+');
            Route::put('/{id}', [AdminManagementController::class, 'updateAdmin'])
                ->middleware('permission:admin.update')
                ->where('id', '[0-9]+');
            Route::delete('/{id}', [AdminManagementController::class, 'deleteAdmin'])
                ->middleware('permission:admin.delete')
                ->where('id', '[0-9]+');

            // Admin role and permission assignment routes
            Route::post('/{id}/roles', [AdminManagementController::class, 'assignRoles'])->middleware('permission:role.assign');
            Route::post('/{id}/permissions', [AdminManagementController::class, 'assignPermissions'])->middleware('permission:permission.assign');

            // Commented out routes preserved for reference
            // Route::get('/{id}/roles', [AdminManagementController::class, 'listAdminRoles'])->middleware('permission:role.view');
            // Route::get('/{id}/permissions', [AdminManagementController::class, 'listAdminPermissions'])->middleware('permission:permission.view');
            // Route::get('/{id}/permissions/full', [AdminManagementController::class, 'getAdminPermissions'])->middleware('permission:permission.view');

            // Offer Management Routes
            Route::prefix('offers')
                ->group(function () {
                    Route::get('/', [OfferManagementController::class, 'listOffers']);
                    Route::get('/{id}', [OfferManagementController::class, 'getOffer'])
                        ->where('id', '[0-9]+');
                    Route::post('/', [OfferManagementController::class, 'createOffer']);
                    Route::post('/update/{id}', [OfferManagementController::class, 'updateOffer'])
                        ->where('id', '[0-9]+');
                    Route::delete('/{id}', [OfferManagementController::class, 'deleteOffer'])
                        ->where('id', '[0-9]+');
                    Route::post('/update-display-order', [OfferManagementController::class, 'updateDisplayOrder']);
                });

            // Admin Notification Routes
            Route::prefix('notifications')
                ->group(function () {
                    Route::get('/', [AdminNotificationController::class, 'index']);
                    Route::get('/unread-count', [AdminNotificationController::class, 'getUnreadCount']);
                    Route::get('/statistics', [AdminNotificationController::class, 'getStatistics']);
                    Route::post('/mark-all-read', [AdminNotificationController::class, 'markAllAsRead']);
                    Route::get('/{notificationId}', [AdminNotificationController::class, 'show'])
                        ->where('notificationId', '[0-9]+');
                    Route::post('/{notificationId}/toggle-read', [AdminNotificationController::class, 'toggleReadStatus'])
                        ->where('notificationId', '[0-9]+');
                });
        });
});
