<?php

namespace App\Enums;

enum SupportRequestIssueType: string
{
    case INQUIRY = 'Inquiry';
    case COMPLAINT = 'Complaint';
    case BUG_REPORT = 'Bug Report';
    case FEATURE_REQUEST = 'Feature Request';
    case OTHER = 'Other';

    /**
     * Get all available issue types as an array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Check if an issue type is valid
     */
    public static function isValid(string $issueType): bool
    {
        return in_array($issueType, self::values());
    }

    /**
     * Get options for form select fields
     */
    public static function options(): array
    {
        $options = [];
        foreach (self::cases() as $case) {
            $options[] = [
                'label' => __('issue_types.'.$case->value),
                'value' => $case->value,
            ];
        }

        return $options;
    }
}
