<?php

namespace App\Http\Resources\StaticContent;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class StaticContentResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'type' => $this->type,
            'user_type' => $this->user_type,
            'is_active' => $this->is_active,
            'translations' => $this->translations->mapWithKeys(function ($item) {
                return [$item->locale => [
                    'title' => $item->title,
                    'content' => $item->content,
                ]];
            }),
        ];
    }
}
