<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('support_requests', function (Blueprint $table) {
            $table->id();
            $table->string('request_id')->unique()->comment('Format: REQ-xx');
            $table->unsignedBigInteger('user_id');
            $table->string('subject');
            $table->string('issue_type'); // Inquiry, Complaint, Bug Report, etc.
            $table->text('details');
            $table->text('admin_response')->nullable();
            $table->string('status')->default('pending'); // pending, open, closed
            $table->timestamps();
            $table->softDeletes();

            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('support_requests');
    }
};
