<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class Offer extends Model implements HasMedia
{
    use HasFactory, InteractsWithMedia, SoftDeletes;

    protected $fillable = [
        'title',
        'description',
        'discount_text',
        'is_active',
        'display_order',
        'start_date',
        'end_date',
        'button_text',
        'button_link',
        'is_limited_time',
    ];

    protected $casts = [
        'is_active' => 'boolean',
        'is_limited_time' => 'boolean',
        'start_date' => 'datetime',
        'end_date' => 'datetime',
        'title' => 'array',
        'description' => 'array',
        'button_text' => 'array',
        'discount_text' => 'array',
    ];

    /**
     * Boot the model and set up event listeners for cascading deletes.
     */
    public static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            // No child relationships to cascade delete
            // Offer model is a standalone entity
        });
    }

    public function scopeActive($query)
    {
        return $query->where('is_active', true)
            ->where(function ($q) {
                $q->whereNull('start_date')
                    ->orWhere('start_date', '<=', now());
            })
            ->where(function ($q) {
                $q->whereNull('end_date')
                    ->orWhere('end_date', '>=', now());
            });
    }

    public function scopeOrdered($query)
    {
        return $query->orderBy('display_order', 'asc');
    }

    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('offer_image')
            ->singleFile();
    }

    public function notifications(): \Illuminate\Database\Eloquent\Relations\MorphMany
    {
        return $this->morphMany(Notification::class, 'notifiable');
    }
}
