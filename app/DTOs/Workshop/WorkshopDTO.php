<?php

namespace App\DTOs\Workshop;

use App\DTOs\BaseDTO;
use App\Http\Requests\Vendor\WorkshopRequest;
use Illuminate\Http\UploadedFile;

class WorkshopDTO extends BaseDTO
{
    public function __construct(
        public readonly int $user_id,
        public readonly string $name,
        public readonly ?string $description = null,
        public readonly ?string $phone_number = null,
        public readonly ?string $whatsapp = null,
        public readonly ?string $city = null,
        public readonly ?string $address = null,
        public readonly ?string $latitude = null,
        public readonly ?string $longitude = null,
        public readonly ?bool $is_active = true,
        public readonly bool $is_approved = true,
        public readonly ?UploadedFile $photo = null
    ) {}

    /**
     * Create a DTO from a request
     */
    public static function fromRequest(WorkshopRequest $request, ?int $user_id = null): self
    {
        $data = $request->validated();

        return new self(
            user_id: $user_id ?? auth()->id(),
            name: $data['name'],
            description: $data['description'] ?? null,
            phone_number: $data['phone_number'] ?? null,
            whatsapp: $data['whatsapp'] ?? null,
            city: $data['city'] ?? null,
            address: $data['address'] ?? null,
            latitude: $data['latitude'] ?? null,
            longitude: $data['longitude'] ?? null,
            is_active: $data['is_active'] ?? true,
            is_approved: true,
            photo: $request->hasFile('photo') ? $request->file('photo') : null
        );
    }
}
