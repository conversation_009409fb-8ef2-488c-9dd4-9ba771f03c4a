<?php

namespace App\Http\Resources\Customer;

use App\Enums\NotificationType;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NotificationResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'notification_type' => $this->notification_type,
            'is_read' => $this->readStatus()->where('user_id', $request->user()->id)->first()->is_read ?? false,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            'notification_data' => $this->getNotificationData(),
        ];
    }

    private function getNotificationData(): ?array
    {
        if (! $this->notifiable) {
            return null;
        }

        switch ($this->notification_type) {
            case NotificationType::REMINDER:
                $vehicle = $this->notifiable->vehicle()->withTrashed()->first();

                return [
                    'reminder_type' => $this->notifiable->reminder_type,
                    'reminder_date' => $this->notifiable->reminder_date,
                    'vehicle_name' => $vehicle?->name ?? 'Vehicle not found',
                ];

            case NotificationType::OFFER:
                return [
                    'offer_title' => $this->notifiable?->title,
                    'offer_description' => $this->notifiable?->description,
                    'is_active' => $this->notifiable?->is_active ?? false,
                    'start_date' => $this->notifiable?->start_date,
                    'end_date' => $this->notifiable?->end_date,
                ];
        }

        return null;
    }
}
