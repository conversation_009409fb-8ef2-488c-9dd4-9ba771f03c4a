<?php

namespace App\Http\Requests\Customer;

use App\Enums\SupportRequestIssueType;
use Illuminate\Foundation\Http\FormRequest;

class CreateSupportRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true; // Customer is authorized
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules()
    {
        return [
            'subject' => 'required|string|max:255',
            'issue_type' => ['required', 'string', 'in:'.implode(',', SupportRequestIssueType::values())],
            'details' => 'required|string|max:5000',
        ];
    }
}
