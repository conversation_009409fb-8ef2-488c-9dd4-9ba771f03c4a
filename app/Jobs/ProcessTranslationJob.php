<?php

namespace App\Jobs;

use App\Services\TranslationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ProcessTranslationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $modelType;

    protected $modelId;

    protected $field;

    protected $sourceLanguage;

    public function __construct(string $modelType, int $modelId, string $field, string $sourceLanguage)
    {
        $this->modelType = $modelType;
        $this->modelId = $modelId;
        $this->field = $field;
        $this->sourceLanguage = $sourceLanguage;
    }

    public function handle(TranslationService $translationService): void
    {
        $model = $this->modelType::find($this->modelId);
        $currentValue = $model->{$this->field};

        if (is_array($currentValue)) {
            $content = $currentValue[$this->sourceLanguage] ?? '';
        }

        if (empty($content)) {
            return;
        }

        $translatedContent = $translationService->getTranslations($content, $this->sourceLanguage);

        $model->{$this->field} = $translatedContent;
        $model->save();
    }
}
