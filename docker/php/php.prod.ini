; Production PHP configuration
upload_max_filesize = 50M
post_max_size = 50M
memory_limit = 256M
max_execution_time = 300

; Production: OpCache settings optimized for performance
opcache.enable = 1
opcache.memory_consumption = 256
opcache.interned_strings_buffer = 16
opcache.max_accelerated_files = 20000
opcache.validate_timestamps = 0
opcache.save_comments = 1
opcache.fast_shutdown = 1
opcache.enable_file_override = 1
opcache.enable_cli = 0
opcache.revalidate_freq = 0
opcache.jit = 1255
opcache.jit_buffer_size = 128M

; Production: Error handling - hide errors from users
display_errors = Off
display_startup_errors = Off
error_reporting = E_ALL & ~E_DEPRECATED & ~E_STRICT
log_errors = On
error_log = /var/log/php_errors.log

; Session
session.save_handler = redis
session.save_path = "tcp://redis:6379"
session.gc_maxlifetime = 86400

; Production: Redis optimization
redis.session.locking_enabled = 1
redis.session.lock_retries = 10
redis.session.lock_wait_time = 2000

; Security settings
expose_php = Off
allow_url_fopen = On
disable_functions = exec,passthru,shell_exec,system,popen,curl_multi_exec,show_source

; Performance settings
realpath_cache_size = 4096k
realpath_cache_ttl = 600
