<?php

namespace App\Http\Controllers\Admin;

use App\Enums\UserType;
use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CreateCustomerRequest;
use App\Http\Requests\Admin\UpdateCustomerRequest;
use App\Http\Resources\Admin\CustomerCollection;
use App\Http\Resources\Admin\CustomerResource;
use App\Models\User;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CustomerManagementController extends Controller
{
    public function createCustomer(CreateCustomerRequest $request): JsonResponse
    {
        try {
            DB::beginTransaction();

            $customer = User::create([
                'name' => $request->name,
                'phone_number' => $request->phone_number,
                'user_type' => UserType::CUSTOMER->value,
                'gender' => $request->gender,
                'is_verified' => $request->is_verified,
            ]);

            DB::commit();

            return ApiResponse::success([
                'customer' => new CustomerResource($customer),
            ], 'Customer user created successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create customer user', $e);
        }
    }

    public function updateCustomer(UpdateCustomerRequest $request, int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $customer = User::where('id', $id)
                ->where('user_type', UserType::CUSTOMER->value)
                ->firstOrFail();

            $customer->update([
                'name' => $request->name,
                'phone_number' => $request->phone_number,
                'gender' => $request->gender,
                'is_verified' => $request->is_verified,
            ]);

            DB::commit();

            return ApiResponse::success([
                'customer' => new CustomerResource($customer),
            ], 'Customer user updated successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update customer user', $e);
        }
    }

    public function deleteCustomer(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $customer = User::where('id', $id)
                ->where('user_type', UserType::CUSTOMER->value)
                ->firstOrFail();

            $customer->delete();

            DB::commit();

            return ApiResponse::success([], 'Customer user deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete customer user', $e);
        }
    }

    public function listCustomers(Request $request): JsonResponse
    {
        try {
            $query = User::withCount('vehicles')->where('user_type', UserType::CUSTOMER->value);

            if ($request->has('verified')) {
                $query->where('is_verified', $request->verified == 'true');
            }

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($query) use ($search) {
                    $query->where('name', 'like', "%{$search}%")
                        ->orWhere('email', 'like', "%{$search}%")
                        ->orWhere('phone_number', 'like', "%{$search}%");
                });
            }

            $customers = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new CustomerCollection($customers),
                'Customers retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve customers', $e);
        }
    }

    public function getCustomer(int $id): JsonResponse
    {
        try {
            $customer = User::where('id', $id)
                ->where('user_type', UserType::CUSTOMER->value)
                ->firstOrFail();

            return ApiResponse::success([
                'customer' => new CustomerResource($customer),
            ]);
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve customer details', $e);
        }
    }

    /**
     * List all soft-deleted customer users
     */
    public function listDeletedCustomers(Request $request): JsonResponse
    {
        try {
            $query = User::onlyTrashed()
                ->where('user_type', UserType::CUSTOMER->value);

            // Apply search filter if provided
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%$search%")
                        ->orWhere('email', 'like', "%$search%")
                        ->orWhere('phone_number', 'like', "%$search%");
                });
            }

            $customers = $query->paginate($request->per_page ?? 15);

            return ApiResponse::success(
                new CustomerCollection($customers),
                'Deleted customer users retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve deleted customer users', $e);
        }
    }

    /**
     * Restore a soft-deleted customer
     */
    public function restoreCustomer(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $customer = User::onlyTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::CUSTOMER->value)
                ->firstOrFail();

            $customer->restore();

            DB::commit();

            return ApiResponse::success([
                'customer' => new CustomerResource($customer),
            ], 'Customer user restored successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to restore customer user', $e);
        }
    }

    /**
     * Permanently delete a soft-deleted customer
     */
    public function permanentlyDeleteCustomer(int $id): JsonResponse
    {
        try {
            DB::beginTransaction();

            $customer = User::withTrashed()
                ->where('id', $id)
                ->where('user_type', UserType::CUSTOMER->value)
                ->firstOrFail();

            // Force delete the customer
            $customer->forceDelete();

            DB::commit();

            return ApiResponse::success([], 'Customer user permanently deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to permanently delete customer user', $e);
        }
    }
}
