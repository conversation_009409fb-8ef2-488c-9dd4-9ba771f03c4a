<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\BaseApiRequest;

class AssignPermissionsRequest extends BaseApiRequest
{
    public function authorize(): bool
    {
        return $this->user() && $this->user()->can('permission.assign');
    }

    public function rules(): array
    {
        return [
            'permissions' => 'required|array',
            'permissions.*' => 'exists:permissions,name|distinct',
        ];
    }

    public function attributes(): array
    {
        return [
            'permissions.*' => 'permission',
        ];
    }
}
