<?php

namespace App\Http\Controllers\Admin;

use App\Helpers\ApiResponse;
use App\Http\Controllers\Controller;
use App\Http\Resources\Admin\VehicleCollection;
use App\Http\Resources\Admin\VehicleResource;
use App\Models\Vehicle;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class VehicleManagementController extends Controller
{
    public function listVehiclesByUser(Request $request, $userId): JsonResponse
    {
        try {
            $query = Vehicle::with(['user', 'services' => function ($query) {
                $query->orderBy('service_date', 'desc');
            }, 'notes', 'reminders'])
                ->where('user_id', $userId);

            $vehicles = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new VehicleCollection($vehicles),
                'Vehicles retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve vehicles by user', $e);
        }
    }

    public function listVehicles(Request $request): JsonResponse
    {
        try {
            $query = Vehicle::with(['user', 'services' => function ($query) {
                $query->orderBy('service_date', 'desc');
            }, 'notes', 'reminders']);

            // Apply search filters
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('make_brand', 'like', "%{$search}%")
                        ->orWhere('plate_number', 'like', "%{$search}%")
                        ->orWhereHas('user', function ($q) use ($search) {
                            $q->where('name', 'like', "%{$search}%")
                                ->orWhere('email', 'like', "%{$search}%")
                                ->orWhere('phone', 'like', "%{$search}%");
                        });
                });
            }

            // Owner filter
            if ($request->has('owner_id')) {
                $query->where('user_id', $request->owner_id);
            }

            // Make/brand filter
            if ($request->has('make_brand')) {
                $query->where('make_brand', 'like', "%{$request->make_brand}%");
            }

            // Vehicle type filter
            if ($request->has('vehicle_type')) {
                $query->where('vehicle_type', $request->vehicle_type);
            }

            // Apply sorting
            $sortField = $request->input('sort_field', 'created_at');
            $sortDirection = $request->input('sort_direction', 'desc');
            $query->orderBy($sortField, $sortDirection);

            $vehicles = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new VehicleCollection($vehicles),
                'Vehicles retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve vehicles', $e);
        }
    }

    public function getVehicle(int $id): JsonResponse
    {
        try {
            $vehicle = Vehicle::with(['user', 'services' => function ($query) {
                $query->orderBy('service_date', 'desc');
            }])->findOrFail($id);

            return ApiResponse::success(
                new VehicleResource($vehicle),
                'Vehicle details retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve vehicle details', $e);
        }
    }

    public function createVehicle(Request $request): JsonResponse
    {
        try {
            $validator = Validator::make($request->all(), [
                'user_id' => 'required|exists:users,id',
                'name' => 'required|string|max:255',
                'make_brand' => 'required|string|max:255',
                'model_year' => 'nullable|string|max:4',
                'vin_number' => 'nullable|string|max:50',
                'chassis_number' => 'nullable|string|max:50',
                'mileage' => 'nullable|integer',
                'vehicle_type' => 'nullable|string|max:50',
                'plate_number' => 'nullable|string|max:20',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error($validator->errors()->first(), 422);
            }

            DB::beginTransaction();

            $vehicle = Vehicle::create($request->all());

            DB::commit();

            return ApiResponse::success(
                new VehicleResource($vehicle->load('user')),
                'Vehicle created successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to create vehicle', $e);
        }
    }

    public function updateVehicle(Request $request, int $id): JsonResponse
    {
        try {
            $vehicle = Vehicle::findOrFail($id);

            $validator = Validator::make($request->all(), [
                'user_id' => 'sometimes|exists:users,id',
                'name' => 'sometimes|string|max:255',
                'make_brand' => 'sometimes|string|max:255',
                'model_year' => 'nullable|string|max:4',
                'vin_number' => 'nullable|string|max:50',
                'chassis_number' => 'nullable|string|max:50',
                'mileage' => 'nullable|integer',
                'vehicle_type' => 'nullable|string|max:50',
                'plate_number' => 'nullable|string|max:20',
            ]);

            if ($validator->fails()) {
                return ApiResponse::error($validator->errors()->first(), 422);
            }

            DB::beginTransaction();

            $vehicle->update($request->all());

            DB::commit();

            return ApiResponse::success(
                new VehicleResource($vehicle->load('user')),
                'Vehicle updated successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to update vehicle', $e);
        }
    }

    public function deleteVehicle(int $id): JsonResponse
    {
        try {
            $vehicle = Vehicle::findOrFail($id);

            DB::beginTransaction();

            $vehicle->delete();

            DB::commit();

            return ApiResponse::success([], 'Vehicle deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to delete vehicle', $e);
        }
    }

    public function listDeletedVehicles(Request $request): JsonResponse
    {
        try {
            $query = Vehicle::onlyTrashed()->with(['user']);

            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('make_brand', 'like', "%{$search}%")
                        ->orWhere('plate_number', 'like', "%{$search}%");
                });
            }

            $vehicles = $query->paginate($request->per_page ?? 10);

            return ApiResponse::success(
                new VehicleCollection($vehicles),
                'Deleted vehicles retrieved successfully'
            );
        } catch (Exception $e) {
            return ApiResponse::errorWithLog('Failed to retrieve deleted vehicles', $e);
        }
    }

    public function restoreVehicle(int $id): JsonResponse
    {
        try {
            $vehicle = Vehicle::onlyTrashed()->findOrFail($id);

            DB::beginTransaction();

            $vehicle->restore();

            DB::commit();

            return ApiResponse::success(
                new VehicleResource($vehicle->load('user')),
                'Vehicle restored successfully'
            );
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to restore vehicle', $e);
        }
    }

    public function permanentlyDeleteVehicle(int $id): JsonResponse
    {
        try {
            $vehicle = Vehicle::withTrashed()->findOrFail($id);

            DB::beginTransaction();

            // Delete related records first
            $vehicle->services()->delete();
            $vehicle->reminders()->delete();
            $vehicle->notes()->delete();

            // Now permanently delete the vehicle
            $vehicle->forceDelete();

            DB::commit();

            return ApiResponse::success([], 'Vehicle permanently deleted successfully');
        } catch (Exception $e) {
            DB::rollBack();

            return ApiResponse::errorWithLog('Failed to permanently delete vehicle', $e);
        }
    }
}
