<?php

namespace App\Http\Resources;

use App\Enums\UserType;
use App\Http\Resources\Vendor\WorkshopResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    public function toArray(Request $request)
    {
        if ($this->user_type === UserType::VENDOR) {
            $data = [
                'id' => $this->id,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'phone_number' => $this->phone_number,
                'email' => $this->email,
                'address' => $this->address,
                'city' => $this->city,
                'is_vendor_account_approved' => $this->is_vendor_account_approved,
                'avatar_url' => $this->getFirstMediaUrl('avatar') ?? null,
                'workshops' => WorkshopResource::collection($this->whenLoaded('workshops')),
            ];

            return $data;

        } elseif ($this->user_type === UserType::CUSTOMER) {
            $data = [
                'id' => $this->id,
                'name' => $this->name,
                'phone_number' => $this->phone_number,
                'gender' => $this->gender?->value,
                'avatar_url' => $this->getFirstMediaUrl('avatar') ?? null,
            ];

            return $data;

        } elseif ($this->user_type === UserType::ADMIN) {
            return [
                'id' => $this->id,
                'first_name' => $this->first_name,
                'last_name' => $this->last_name,
                'email' => $this->email,
            ];
        }
    }
}
