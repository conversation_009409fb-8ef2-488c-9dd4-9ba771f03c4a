<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use <PERSON>tie\Permission\Models\Permission;
use <PERSON>tie\Permission\Models\Role;

class RolesAndPermissionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get all permissions
        $allPermissions = Permission::all();

        // Create Content Manager role
        $contentManagerRole = Role::firstOrCreate(['name' => 'content-manager']);
        $contentManagerPermissions = Permission::whereIn('name', [
            // Content management permissions
            'content.onboarding.view.list',
            'content.onboarding.view.single',
            'content.onboarding.create',
            'content.onboarding.update',
            'content.onboarding.delete',
            'content.onboarding.reorder',
            'content.faq.view.list',
            'content.faq.view.single',
            'content.faq.view.customer',
            'content.faq.view.vendor',
            'content.faq.create',
            'content.faq.update',
            'content.faq.delete',
            'content.faq.reorder',
            'content.static.view.list',
            'content.static.view.single',
            'content.static.view.bytype',
            'content.static.create',
            'content.static.update',
            'content.static.delete',
            // Dashboard permissions (limited)
            'dashboard.view.statistics',
        ])->get();
        $contentManagerRole->syncPermissions($contentManagerPermissions);
        $this->command->info('Content Manager role created with '.$contentManagerPermissions->count().' permissions.');

        // Create Customer Support role
        $customerSupportRole = Role::firstOrCreate(['name' => 'customer-support']);
        $customerSupportPermissions = Permission::whereIn('name', [
            // Support Request permissions
            'support.view.list',
            'support.view.single',
            'support.view.deleted',
            'support.create',
            'support.update',
            // Customer permissions (view only)
            'customer.view.list',
            'customer.view.single',
            // Vehicle permissions (view only)
            'vehicle.view.list',
            'vehicle.view.single',
            'vehicle.view.byuser',
            // Dashboard permissions (limited)
            'dashboard.view.statistics',
            'dashboard.view.maintenance-logs',
        ])->get();
        $customerSupportRole->syncPermissions($customerSupportPermissions);
        $this->command->info('Customer Support role created with '.$customerSupportPermissions->count().' permissions.');

        // Create Vendor Manager role
        $vendorManagerRole = Role::firstOrCreate(['name' => 'vendor-manager']);
        $vendorManagerPermissions = Permission::whereIn('name', [
            // Vendor permissions (full access)
            'vendor.view.list',
            'vendor.view.single',
            'vendor.view.deleted',
            'vendor.view.unapproved',
            'vendor.view.approved',
            'vendor.create',
            'vendor.update',
            'vendor.delete',
            'vendor.restore',
            'vendor.approve',
            'vendor.disapprove',
            // Dashboard permissions
            'dashboard.view.statistics',
            'dashboard.view.maintenance-logs',
        ])->get();
        $vendorManagerRole->syncPermissions($vendorManagerPermissions);
        $this->command->info('Vendor Manager role created with '.$vendorManagerPermissions->count().' permissions.');

        // Create Customer Manager role
        $customerManagerRole = Role::firstOrCreate(['name' => 'customer-manager']);
        $customerManagerPermissions = Permission::whereIn('name', [
            // Customer permissions (full access)
            'customer.view.list',
            'customer.view.single',
            'customer.view.deleted',
            'customer.create',
            'customer.update',
            'customer.delete',
            'customer.restore',
            // Vehicle permissions (view and limited management)
            'vehicle.view.list',
            'vehicle.view.single',
            'vehicle.view.deleted',
            'vehicle.view.byuser',
            // Dashboard permissions
            'dashboard.view.statistics',
            'dashboard.view.maintenance-logs',
        ])->get();
        $customerManagerRole->syncPermissions($customerManagerPermissions);
        $this->command->info('Customer Manager role created with '.$customerManagerPermissions->count().' permissions.');

        // Create System Administrator role (all permissions except super-admin specific ones)
        $systemAdminRole = Role::firstOrCreate(['name' => 'system-admin']);
        $systemAdminPermissions = Permission::whereNotIn('name', [
            'admin.delete.permanent',
            'permission.create',
            'permission.update',
            'permission.delete',
        ])->get();
        $systemAdminRole->syncPermissions($systemAdminPermissions);
        $this->command->info('System Administrator role created with '.$systemAdminPermissions->count().' permissions.');
    }
}
